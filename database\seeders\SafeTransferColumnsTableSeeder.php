<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SafeTransferColumnsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('safe_transfer_columns')->delete();
        
        \DB::table('safe_transfer_columns')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Amount' => '1',
                'From_Safe' => '1',
                'To_Safe' => '1',
                'User' => '1',
                'Coin' => '1',
                'Cost_Center' => '1',
                'Note' => '1',
                'Delegate' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-06 11:31:50',
                'Branch' => '1',
            ),
        ));
        
        
    }
}
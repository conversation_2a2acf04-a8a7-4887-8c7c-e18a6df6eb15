<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsPurchasePetrol extends Model
{
    use HasFactory;
        protected $table = 'products_purchase_petrols';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'Price',
        'SmallCode',
        'SmallQty',
        'Total',
        'Store',
        'Product',
        'Unit',
        'Petrol',
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
   
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    

            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Petrol()
    {
        return $this->belongsTo(PurchasePetrol::class,'Petrol');
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingOrder extends Model
{
    use HasFactory;
    
      protected $table = 'shipping_orders';
      protected $fillable = [
        'Code',
        'Date',
        'Weight',
        'Number',
        'Goods_Price',
        'Shipping_Price',
        'Total',
        'Breakable',
        'Note',
        'Status',
        'Type',
        'Delegate',
        'Vendor',
        'Client',
        'Coin',
        'Cost_Center',
        'Draw',
        'Vend',
        'Cli',
        'Open',
        'Paid',
        'Ship_Sort',
        'Adderss',
        'Requests',
        'Sure',
        'Safe',
        'Residual',
        'Emp_Note',
        'Shipping_Delegate',
        'Cancel',
        'Cancel_Note',
        'Cancel_Pay',
        'Cancel_Not_Pay',
        'Vendor_Shipping',
        'Request_Done',
          

    ];
    
    
         public function Status()
    {
        return $this->belongsTo(ShippingStatus::class,'Status');
    }
    
             public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
             public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
    
            public function Type()
    {
        return $this->belongsTo(ShippingType::class,'Type');
    }
    
            public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
            public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
    
            public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
                public function Adderss()
    {
        return $this->belongsTo(ClientAddress::class,'Adderss');
    }
    
    
    
}

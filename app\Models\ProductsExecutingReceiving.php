<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsExecutingReceiving extends Model
{
    use HasFactory;
      protected $table = 'products_executing_receivings';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'Dep',
        'Price',
        'Total',
        'Store',
        'Product',
        'Unit',
        'Executing',
        'type',
    
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Executing()
    {
        return $this->belongsTo(ExecutingReceiving::class,'Executing');
    }
    

}

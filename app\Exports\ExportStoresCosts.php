<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;
use App\Models\Stores;
class ExportStoresCosts implements FromCollection ,WithHeadings , WithChunkReading
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      set_time_limit(0);

        $storex=$this->store;
        $storee =  $storex['store'];
         $group = $storex['group'] ;
        $brand =  $storex['brand'] ;
         $branch = $storex['branch'] ;
        $product_Name =   $storex['product_Name'] ;
         $product_Code = $storex['product_Code'] ;
         $zero = $storex['zero'] ;
        
          if(app()->getLocale() == 'ar' ){ 
                 if($zero == 0){
                     

                     
              $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
                          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store', $storee);
    })  
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
              
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
 
->select('products_qties.P_Ar_Name','products_qties.P_Code','products_qties.Qty','products_qties.Price','products_qties.TotalCost','stores.Name as StoreName')
                  ->get();      
              
                 }else{
                    $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
                          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store', $storee);
    })  
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
              
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
 
->select('products_qties.P_Ar_Name','products_qties.P_Code','products_qties.Qty','products_qties.Price','products_qties.TotalCost','stores.Name as StoreName')
                  ->get();      
                     
                 }
 
          }else{
              
              
            if($zero == 0){
                     

                     
              $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
                          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store', $storee);
    })  
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
              
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
 
->select('products_qties.P_En_Name','products_qties.P_Code','products_qties.Qty','products_qties.Price','products_qties.TotalCost','stores.NameEn as StoreName')
                  ->get();      
              
                 }else{
                    $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
                          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store', $storee);
    })  
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
              
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
 
->select('products_qties.P_En_Name','products_qties.P_Code','products_qties.Qty','products_qties.Price','products_qties.TotalCost','stores.NameEn as StoreName')
                  ->get();      
                     
                 }  
              
          }
              
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Product Name',
          'Product Code',
          'Qty',
          'Cost Price',
          'Total Cost',
          'Store',
        ];
    }
   

    
              public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }  
    

}

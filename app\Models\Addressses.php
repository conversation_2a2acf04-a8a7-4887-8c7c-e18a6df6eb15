<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Addressses extends Model
{
    use HasFactory;
       protected $table = 'addressses';
      protected $fillable = [
        'Address_Name',
        'Street',
        'Special_Mark',
        'Buliding',
        'Floor',
        'Flat',
        'Details',
        'Location',
        'Governrate',
        'City',
        'Place',
        'Customer',
     
   
    ];
    
               public function Governrate()
    {
        return $this->belongsTo(Governrate::class,'Governrate');
    }
    
                   public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
                   public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }
    
                   public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }

}

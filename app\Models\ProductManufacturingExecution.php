<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductManufacturingExecution extends Model
{
    use HasFactory;
           protected $table = 'product_manufacturing_executions';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Precent',
        'Qty',
        'RequiredQty',
        'Store',
        'Patch_Number',
        'Product',
        'Unit',
        'ManuExecution',
    
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function ManuExecution()
    {
        return $this->belongsTo(ManufacturingExecution::class,'ManuExecution');
    }
    

}

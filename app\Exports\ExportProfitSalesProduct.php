<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductUnits;
use App\Models\ProductsPurchases;
use App\Models\ProductSales;
use App\Models\ProductsStoresTransfers;
use App\Models\ProductsStartPeriods;
use App\Models\OutcomManufacturingModel;
class ExportProfitSalesProduct implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->store;

         $storee=$storex['store'];
         $branch=$storex['branch'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $product_Name=$storex['product_Name'];
         $product_Code=$storex['product_Code'];
   
   

   $items=ProductsQty::orderBy('id','asc')
       
          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store',$storee);
    })
       

       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })  
            ->get();   
              
        

        $result = array();
        foreach($items as $record){
            
            $units=ProductUnits::where('Product',$record->Product)->get();
            $rr=ProductUnits::where('Product',$record->Product)->where('Def',1)->first();
  $plow=ProductUnits::where('Product',$record->Product)->where('Rate',1)->first();  
             
             
    $purchs=ProductsPurchases::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('Total_Bf_Tax');
             
    $purchsEE=ProductsPurchases::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('Price');
    $purchsEEQ=ProductsPurchases::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('SmallQty');
    $saless=ProductSales::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('Price');
    $salessQ=ProductSales::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('SmallQty');
             
             
$countPurchs=ProductsPurchases::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('SmallQty');
$countSales=ProductSales::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('SmallQty');
  
        $storesTransfer=ProductsStoresTransfers::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$record->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$record->Store)->get()->sum('SmallTrans_Qty');
             
$purchsStart=ProductsStartPeriods::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('Total');
$countStart=ProductsStartPeriods::where('Product',$record->Product)->where('SmallCode',$plow->Barcode)->where('Store',$record->Store)->get()->sum('SmallQty');

                $OUTCOME=OutcomManufacturingModel::where('Product',$record->Product)->where('Store',$record->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$record->Product)->where('Store',$record->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;
     
             if($CollectCount == 0){
                 $Average = $Collect ;
             }else{
                 
                 $Average = $Collect /  $CollectCount ;
             }
               
     $yy=$rr->Price ;

           $f=  ($yy * $countSales )  - ($Average * $countSales);
           $ff=   ($Average * $countSales);
           $fff=  ($yy * $countSales);

            



if(!empty($record->Store()->first()->Name)){$Store=$record->Store()->first()->Name;}else{$Store='';}
if(!empty($record->Branch()->first()->Arabic_Name)){$Branch=$record->Branch()->first()->Arabic_Name;}else{$Branch='';}
if(!empty($record->Group()->first()->Name)){$Group=$record->Group()->first()->Name;}else{$Group='';}
if(!empty($record->Brand()->first()->Name)){$Brand=$record->Brand()->first()->Name;}else{$Brand='';}

   if(app()->getLocale() == 'ar' ){ 

           $result[] = array(
              'Product_Code'=>$record->P_Code,
              'Product_Name' => $record->P_Ar_Name .''. ( $record->V_Name) .''. ($record->VV_Name),
              'Unit' => $record->Unit()->first()->Name,
              'Qty' => $record->Qty,
              'Price' => $yy,
              'Cost' => $Average,
              'Profit' => $f,
              'Store' => $Store,
              'Branch' => $Branch,
              'Group' => $Group,
              'Brand' =>$Brand 
               
             
                                              
        
           );
       
   }else{
       
             $result[] = array(
              'Product_Code'=>$record->P_Code,
              'Product_Name' => $record->P_En_Name .''. ( $record->V_Name) .''. ($record->VV_Name),
              'Unit' => $record->Unit()->first()->NameEn,
              'Qty' => $record->Qty,
              'Price' => $yy,
              'Cost' => $Average,
              'Profit' => $f,
              'Store' => $Store,
              'Branch' => $Branch,
              'Group' => $Group,
              'Brand' =>$Brand 
               
             
                                              
        
           );    
       
   }
       
       
        }

        return collect($result);
    }
    

    public function headings(): array
    {
        return [
          'Product_Code',
          'Product_Name',
          'Unit',
          'Qty',
          'Price',
          'Cost',
          'Profit',
          'Store',
          'Branch',
          'Group',
          'Brand'
        
        ];
    }
    
 

}

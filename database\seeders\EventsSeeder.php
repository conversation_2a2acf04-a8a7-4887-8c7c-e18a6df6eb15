<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Event;

class EventsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Event::create([
            'Start_Date' => date('Y-m-d'),
            'End_Date' => date('Y-m-d', strtotime('+1 year')),
            'Event_Ar_Name' => 'انتهاء الاشتراك',
            'Event_En_Name' => 'Expired Supscribe',
            'Type' => 'System',
            'Type_ID' => '1',
            'Type_Code' => 'EXPIRED_SUBSCRIBE',
            'Emp' => null,
            'Client' => null,
            'Product' => null,
            'Customer' => null,
        ]);
    }
}

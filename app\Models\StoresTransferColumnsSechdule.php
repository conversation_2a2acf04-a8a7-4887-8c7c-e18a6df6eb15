<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoresTransferColumnsSechdule extends Model
{
    use HasFactory;
     protected $table = 'stores_transfer_columns_sechdules';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',               
        'Amount',               
        'From_Store',               
        'To_Store',               
        'User',               
        'Coin',               
        'Shipping',               
        'Note',               
        'Delegate',     
        'Branch',     
          
        'Product_Name',               
        'Product_Code',               
        'Group',               
        'Brand',               
        'Qty',               
        'Price',               
        'Trans_Qty',               
        'Unit',               
        'Total',               
                 


    ];
}

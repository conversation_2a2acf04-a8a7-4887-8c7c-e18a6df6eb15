<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountsDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('accounts_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Draw')->nullable();
            $table->integer('Coin')->nullable();
            $table->timestamps();
            $table->string('Sure_Recipts')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('accounts_default_data');
    }
}
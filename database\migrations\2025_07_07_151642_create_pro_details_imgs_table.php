<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProDetailsImgsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pro_details_imgs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Image')->nullable();
            $table->string('Arabic_Title')->nullable();
            $table->string('Arabic_Desc')->nullable();
            $table->string('English_Title')->nullable();
            $table->string('English_Desc')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pro_details_imgs');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SecretariatImportGoods extends Model
{
    use HasFactory;
        protected $table = 'secretariat_import_goods';
      protected $fillable = [
        'Code',
        'Date',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Account',
        'Store',
        'User',

    ];

 
          public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }

              public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    

          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

}

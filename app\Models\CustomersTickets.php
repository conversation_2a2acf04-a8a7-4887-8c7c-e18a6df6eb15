<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomersTickets extends Model
{
    use HasFactory;
            protected $table = 'customers_tickets';
    protected $fillable = [
        'Code',
        'Problem',
        'ProblemEn',
        'Status',
        'Bill_Number',
        'Responsible',
        'Customer',
        'User',
    ];
   
           public function Responsible()
    {
        return $this->belongsTo(Employess::class,'Responsible');
    }
    
               public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }





    
    
}

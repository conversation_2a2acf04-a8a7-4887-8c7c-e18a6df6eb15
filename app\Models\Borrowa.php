<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Borrowa extends Model
{
    use HasFactory;
            protected $table = 'borrowas';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Amount',
        'Draw',
        'Note',
        'Safe',
        'Emp',
        'Coin',
        'Cost_Center',
        'User',
    ];
    
    
       public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
         public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
}

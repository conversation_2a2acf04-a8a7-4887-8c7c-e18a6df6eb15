<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    use HasFactory;
          protected $table = 'cities';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Gov',
        'Ship_Price',
        'Shipping_Company',
        'SearchCode',
   
    ];
    
               public function Gov()
    {
        return $this->belongsTo(Governrate::class,'Gov');
    }
    
                  public function Shipping_Company()
    {
        return $this->belongsTo(ShippingCompany::class,'Shipping_Company');
    }
    
    
                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
    
    
}

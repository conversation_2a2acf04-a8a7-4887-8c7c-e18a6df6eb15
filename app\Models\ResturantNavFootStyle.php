<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantNavFootStyle extends Model
{
    use HasFactory;
       protected $table = 'resturant_nav_foot_styles';
      protected $fillable = [
          
        'Navbar_BG',
        'Navbar_Txt_Color',
        'Navbar_Txt_Hover_Color',
        'Navbar_Btn_BG',
        'Navbar_Btn_Color',
        'Navbar_Sub_Menu_BG',
        'Navbar_Sub_Menu_Color',
          
        'Footer_BG_Type',
        'Footer_BG_Color',
        'Footer_BG_Image',
        'Footer_Title_Color',
        'Footer_Txt_Color',
        'Footer_Social_Color',
        'Footer_Social_Hover_Color',
        'Footer_CopyRight_Color',
        'Footer_CopyRight_Company_Color',
       
       
    ];
}

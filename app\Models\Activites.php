<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activites extends Model
{
    use HasFactory;
                protected $table = 'activites';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingModel extends Model
{
    use HasFactory;
          protected $table = 'manufacturing_models';
      protected $fillable = [
        'Code',
        'Date',
        'Name',
        'NameEn',
        'Time',
        'Draw',
        'Note',
        'Product_Numbers',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Hall',
        'Coin',
        'Cost_Center',
        'User',
        'Type',
    
    ];

         public function Hall()
    {
        return $this->belongsTo(ManufacturingHalls::class,'Hall');
    }
      public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
 
    
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SafesBanksTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('safes_banks')->delete();
        
        \DB::table('safes_banks')->insert(array (
            0 => 
            array (
                'id' => 3,
                'Code' => '3',
                'Date' => '2021-06-20',
                'Name' => 'الخزينه الرئيسيه',
                'Type' => '1',
                'Note' => NULL,
                'Account' => 30,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => '2022-10-18 22:35:12',
                'Branch' => '3',
                'Service_Fee' => NULL,
            ),
            1 => 
            array (
                'id' => 4,
                'Code' => '4',
                'Date' => '2021-06-20',
                'Name' => 'بنك مصر',
                'Type' => '2',
                'Note' => NULL,
                'Account' => 32,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => '2022-10-18 22:35:21',
                'Branch' => '3',
                'Service_Fee' => NULL,
            ),
            2 => 
            array (
                'id' => 5,
                'Code' => '5',
                'Date' => '2021-06-20',
                'Name' => 'البنك الأهلي',
                'Type' => '2',
                'Note' => NULL,
                'Account' => 33,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => '2022-10-18 22:35:27',
                'Branch' => '3',
                'Service_Fee' => NULL,
            ),
        ));
        
        
    }
}
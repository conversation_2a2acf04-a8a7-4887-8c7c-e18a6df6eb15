<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientsStatementsColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clients_statements_columns', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Account_Code');
            $table->string('Account_Name');
            $table->string('Debiator_Before');
            $table->string('Creditor_Before');
            $table->string('Total_Debitor');
            $table->string('Total_Creditor');
            $table->string('Debitor_Balance');
            $table->string('Creditor_Balance');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clients_statements_columns');
    }
}
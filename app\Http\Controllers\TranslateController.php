<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\UsersMoves;
use App\Models\PurposeTravel;
use App\Models\Languages;
use App\Models\Empassies;
use App\Models\TranslationTourismCompanies;
use App\Models\TranslteModules;
use App\Models\TranslteModulesDetails;
use App\Models\EmpassyReserveDate;
use App\Models\Coins;
use App\Models\AcccountingManual;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\AddTranslate;
use DB ;
use Str ;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use SpamProtector;
use Storage;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class TranslateController extends Controller
{
    
function __construct()
{

$this->middleware('permission:الغرض من السفر', ['only' => ['PurposeTravel']]);
$this->middleware('permission:اللغات', ['only' => ['Languages']]);
$this->middleware('permission:السفارات', ['only' => ['Empassies']]);
$this->middleware('permission:شركات الترجمة و السياحة', ['only' => ['TranslationTourismCompanies']]);
$this->middleware('permission:نماذج الترجمة', ['only' => ['TranslteModules']]);
$this->middleware('permission:حجز موعد سفارة', ['only' => ['EmpassyReserveDate']]);
$this->middleware('permission:اضافة ترجمة', ['only' => ['AddTranslate']]);


}
    
    
    
            //PurposeTravel
            public function PurposeTravel(){
        $items=PurposeTravel::all();
         return view('admin.Translate.PurposeTravel',['items'=>$items]);
    }
    
     public function AddPurposeTravel(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         PurposeTravel::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='الغرض من السفر';
           $dataUser['ScreenEn']='Purpose Travel';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditPurposeTravel($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           PurposeTravel::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='الغرض من السفر';
           $dataUser['ScreenEn']='Purpose Travel';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeletePurposeTravel($id){
                      
        $del=PurposeTravel::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='الغرض من السفر';
           $dataUser['ScreenEn']='Purpose Travel';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

        
            //Languages
            public function Languages(){
        $items=Languages::all();
         return view('admin.Translate.Languages',['items'=>$items]);
    }
    
     public function AddLanguages(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Languages::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='اللغات';
           $dataUser['ScreenEn']='Languages';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditLanguages($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           Languages::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='اللغات';
           $dataUser['ScreenEn']='Languages';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteLanguages($id){
                      
        $del=Languages::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='اللغات';
           $dataUser['ScreenEn']='Languages';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

            
            //Empassies
            public function Empassies(){
        $items=Empassies::all();
         return view('admin.Translate.Empassies',['items'=>$items]);
    }
    
     public function AddEmpassies(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Arabic_Address'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }    
         
         $data['Arabic_Address']=request('Arabic_Address');

         
         if(!empty(request('English_Name'))){
         $data['English_Address']=request('English_Address');
         }else{
             
             
           $data['English_Address']=request('Arabic_Address');   
         }
        
         Empassies::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='السفارات';
           $dataUser['ScreenEn']='Empassies';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditEmpassies($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Arabic_Address'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Address']=request('Arabic_Address');
         $data['English_Address']=request('English_Address');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           Empassies::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='السفارات';
           $dataUser['ScreenEn']='Empassies';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteEmpassies($id){
                      
        $del=Empassies::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='السفارات';
           $dataUser['ScreenEn']='Empassies';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

    
                //TranslationTourismCompanies
            public function TranslationTourismCompanies(){
        $items=TranslationTourismCompanies::all();
         return view('admin.Translate.TranslationTourismCompanies',['items'=>$items]);
    }
    
     public function AddTranslationTourismCompanies(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Precent'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Precent']=request('Precent');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         TranslationTourismCompanies::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='شركات الترجمة و السياحة';
           $dataUser['ScreenEn']='Translation/Tourism Companies';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditTranslationTourismCompanies($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
                 'Precent'=>'required',
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');
        $data['Precent']=request('Precent');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           TranslationTourismCompanies::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='شركات الترجمة و السياحة';
           $dataUser['ScreenEn']='Translation/Tourism Companies';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteTranslationTourismCompanies($id){
                      
        $del=TranslationTourismCompanies::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='شركات الترجمة و السياحة';
           $dataUser['ScreenEn']='Translation/Tourism Companies';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    


        
            //TranslteModules
            public function TranslteModules(){
        $items=TranslteModules::all();
        $Languages=Languages::all();
         return view('admin.Translate.TranslteModules',['items'=>$items,'Languages'=>$Languages]);
    }
    
     public function AddTranslteModules(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Code'=>'required',
             'Date'=>'required',
             'Lang'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Lang']=request('Lang');
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         TranslteModules::create($data);
         
         $trans=TranslteModules::orderBy('id','desc')->first();
         
         if(!empty(request('Arabic_Text'))){
             
             
             $Arabic_Text=request('Arabic_Text');
             $Translate_Text=request('Translate_Text');
             
            for($i=0 ; $i < count($Arabic_Text) ; $i++){
                
                $uu['Arabic_Text']=$Arabic_Text[$i];
                $uu['Translate_Text']=$Translate_Text[$i];
                $uu['Module']=$trans->id;
             TranslteModulesDetails::create($uu);   
            } 
             
             
             
         }
         
         



         
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='نماذج الترجمة';
           $dataUser['ScreenEn']='Translte Modules';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditTranslteModules($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
            $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Lang']=request('Lang');
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           TranslteModules::where('id',$id)->update($data);
         
         
               
         if(!empty(request('Arabic_Text'))){
             
             TranslteModulesDetails::where('Module',$id)->delete();
             $Arabic_Text=request('Arabic_Text');
             $Translate_Text=request('Translate_Text');
             
            for($i=0 ; $i < count($Arabic_Text) ; $i++){
                
                $uu['Arabic_Text']=$Arabic_Text[$i];
                $uu['Translate_Text']=$Translate_Text[$i];
                $uu['Module']=$id;
             TranslteModulesDetails::create($uu);   
            } 
             
             
             
         }
    
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='نماذج الترجمة';
           $dataUser['ScreenEn']='Translte Modules';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteTranslteModules($id){
                      
        $del=TranslteModules::find($id);
         
            TranslteModulesDetails::where('Module',$id)->delete();
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='نماذج الترجمة';
           $dataUser['ScreenEn']='Translte Modules';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

    
    //EmpassyReserveDate
                public function EmpassyReserveDate(){
        $items=EmpassyReserveDate::all();
        $PurposeTravel=PurposeTravel::all();
        $Empassies=Empassies::all();
        $Coins=Coins::all();
                    
                 
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
            $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get(); 
                    
               $res=EmpassyReserveDate::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }            
                    
                    
         return view('admin.Translate.EmpassyReserveDate',[
             'items'=>$items,
             'PurposeTravel'=>$PurposeTravel,
             'Empassies'=>$Empassies,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Clients'=>$Clients,
             'Code'=>$Code,
         
         ]);
    }
    
     public function AddEmpassyReserveDate(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Client'=>'required',
             'Purpose'=>'required',
             'Empassy'=>'required',
             'Booking_Date'=>'required',
             'Cost'=>'required',
     
             
               ],[

         ]);

       
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Purpose']=request('Purpose');
         $data['Empassy']=request('Empassy');
         $data['Booking_Date']=request('Booking_Date');
         $data['Cost']=request('Cost');
         $data['Pay']=request('Pay');
         $data['Residual']=request('Residual');
         $data['Safe']=request('Safe');
         $data['Draw']=request('Draw');
         $data['Coin']=request('Coin');
         EmpassyReserveDate::create($data);
         
         
         
                             
                if(request('Residual') == 0){
                    
                      $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>request('Code'),
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Cost'),
            'Total_Creditor' =>request('Cost'),
            'Note' => null,
  
        )
    );
         

        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cost');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cost');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cost');
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Cost');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Cost');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Cost');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                    
                }else{
                    
                          $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' =>request('Cost'),
            'Total_Creditor' =>request('Cost'),
            'Note' => null,
  
        )
    );
         

        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cost');
        $PRODUCTSS['Account']=48;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cost');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cost');
        $Gen['Account']=48;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Cost');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Client');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
   
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Cost');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Cost');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Client');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);    
                    
              $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Pay');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Pay');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Pay');
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Pay');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    

        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Pay');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Pay');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);             
                    
                    
                    
                    
                    
                }    
                    
              
         
         
         
         
         
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='حجز موعد سفارة';
           $dataUser['ScreenEn']='Empassy Reserve Date';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditEmpassyReserveDate($id){ 
         
       $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Client'=>'required',
             'Purpose'=>'required',
             'Empassy'=>'required',
             'Booking_Date'=>'required',
             'Cost'=>'required',
     
             
               ],[

         ]);

       
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Purpose']=request('Purpose');
         $data['Empassy']=request('Empassy');
         $data['Booking_Date']=request('Booking_Date');
         $data['Cost']=request('Cost');
         $data['Pay']=request('Pay');
         $data['Residual']=request('Residual');
         $data['Safe']=request('Safe');
         $data['Draw']=request('Draw');
         $data['Coin']=request('Coin');
           EmpassyReserveDate::where('id',$id)->update($data);
         
         
                               
        $del=EmpassyReserveDate::find($id);
         
              GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         
            
                if(request('Residual') == 0){
                    
                      $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>request('Code'),
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Cost'),
            'Total_Creditor' =>request('Cost'),
            'Note' => null,
  
        )
    );
         

        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cost');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cost');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cost');
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Cost');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Cost');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Cost');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                    
                }else{
                    
                          $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' =>request('Cost'),
            'Total_Creditor' =>request('Cost'),
            'Note' => null,
  
        )
    );
         

        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cost');
        $PRODUCTSS['Account']=48;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cost');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cost');
        $Gen['Account']=48;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Cost');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Client');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
   
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Cost');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Cost');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Client');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);    
                    
              $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Pay');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Pay');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Pay');
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Pay');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    

        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Pay');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Pay');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);             
                    
                    
                    
                    
                    
                }    
                    
              
         
         
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='حجز موعد سفارة';
           $dataUser['ScreenEn']='Empassy Reserve Date';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteEmpassyReserveDate($id){
                      
        $del=EmpassyReserveDate::find($id);
         
              GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='حجز موعد سفارة';
           $dataUser['ScreenEn']='Empassy Reserve Date';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

//AddTranslate
    
                    public function AddTranslate(){
        $items=AddTranslate::all();
        $Languages=Languages::all();
        $Companies=TranslationTourismCompanies::all();
   

          
            $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get(); 
                    
               $res=AddTranslate::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }            
                    
                    
         return view('admin.Translate.AddTranslate',[
             'items'=>$items,
             'Languages'=>$Languages,
             'Companies'=>$Companies,
             'Clients'=>$Clients,
             'Code'=>$Code,
         
         ]);
    }
    
     public function AddAddTranslate(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Client'=>'required',
             'Client_Type'=>'required',
             'From_Lang'=>'required',
             'To_Lang'=>'required',
       
     
             
               ],[

         ]);

       
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Client_Type']=request('Client_Type');
         $data['From_Lang']=request('From_Lang');
         $data['To_Lang']=request('To_Lang');
         $data['Company']=request('Company');
         $data['Num_Translted_Word']=request('Num_Translted_Word');
         $data['ID_Name']=request('ID_Name');
         $data['ID_Profession']=request('ID_Profession');
         $data['ID_Martial_Status']=request('ID_Martial_Status');
         $data['Passport_Name']=request('Passport_Name');    
         $data['Passport_Profession']=request('Passport_Profession');
         $data['Passport_Martial_Status']=request('Passport_Martial_Status');
         $data['Extracted']=request('Extracted');
         $data['Extracted_Birthplace']=request('Extracted_Birthplace');
         $data['Extracted_Issuer']=request('Extracted_Issuer');
         $data['CommercialRegistration']=request('CommercialRegistration');
         $data['Commercial_Name']=request('Commercial_Name');
         $data['Commercial_Type']=request('Commercial_Type');
         $data['Commercial_Start_Date']=request('Commercial_Start_Date');
         $data['Commercial_Number']=request('Commercial_Number');
         $data['Commercial_Capital']=request('Commercial_Capital');
         $data['Commercial_Issuer']=request('Commercial_Issuer');
         $data['Commercial_Address']=request('Commercial_Address');
         AddTranslate::create($data);
         
         

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='اضافة ترجمة';
           $dataUser['ScreenEn']='Add Translate';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditAddTranslate($id){ 
         
         $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Client'=>'required',
             'Client_Type'=>'required',
             'From_Lang'=>'required',
             'To_Lang'=>'required',
        
             
               ],[

         ]);

       
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Client_Type']=request('Client_Type');
         $data['From_Lang']=request('From_Lang');
         $data['To_Lang']=request('To_Lang');
         $data['Company']=request('Company');
         $data['Num_Translted_Word']=request('Num_Translted_Word');
         $data['ID_Name']=request('ID_Name');
         $data['ID_Profession']=request('ID_Profession');
         $data['ID_Martial_Status']=request('ID_Martial_Status');
         $data['Passport_Name']=request('Passport_Name');    
         $data['Passport_Profession']=request('Passport_Profession');
         $data['Passport_Martial_Status']=request('Passport_Martial_Status');
         $data['Extracted']=request('Extracted');
         $data['Extracted_Birthplace']=request('Extracted_Birthplace');
         $data['Extracted_Issuer']=request('Extracted_Issuer');
         $data['CommercialRegistration']=request('CommercialRegistration');
         $data['Commercial_Name']=request('Commercial_Name');
         $data['Commercial_Type']=request('Commercial_Type');
         $data['Commercial_Start_Date']=request('Commercial_Start_Date');
         $data['Commercial_Number']=request('Commercial_Number');
         $data['Commercial_Capital']=request('Commercial_Capital');
         $data['Commercial_Issuer']=request('Commercial_Issuer');
         $data['Commercial_Address']=request('Commercial_Address');
           AddTranslate::where('id',$id)->update($data);
         
         
  
         
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
       $dataUser['Screen']='اضافة ترجمة';
           $dataUser['ScreenEn']='Add Translate';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteAddTranslate($id){
                      
        $del=AddTranslate::find($id);
         
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
       $dataUser['Screen']='اضافة ترجمة';
           $dataUser['ScreenEn']='Add Translate';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
   
    
    










    













    
}

<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Purchases;
use App\Models\ReturnPurch;
use DB;
class ExportPurchasesDetailsBillsReport implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result


        $storex=$this->store;
        $storee =  $storex['store'];
        $from =  $storex['from'];
        $to =  $storex['to'];
        $branch =  $storex['branch'];
        $clients_Group =  $storex['clients_Group'];
        $cost_Center =  $storex['cost_Center'];
        $coin =  $storex['coin'];
        $code =  $storex['code'];
        $refrence_Number =  $storex['refrence_Number'];
        $safe =  $storex['safe'];
        $client =  $storex['client'];
        $payment_Method =  $storex['payment_Method'];
        $delegate =  $storex['delegate'];
        $user =  $storex['user'];
        $shipping_Company =  $storex['shipping_Company'];
        $types =  $storex['types'];
        $typeX =  $storex['typeX'];


   if(app()->getLocale() == 'ar' ){         
         $prods = DB::table('products_purchases')->whereBetween('purchases.Date',[$from,$to])
             
             
                               ->join('purchases', function ($join) {
    
            $join->on('products_purchases.Purchase', '=', 'purchases.id');
        })  
                  
                  
             
             
                ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('purchases.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('purchases.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('purchases.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('purchases.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('purchases.Code', $code);
    }) 
          
 
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('purchases.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('purchases.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('purchases.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('purchases.Vendor', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('purchases.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('purchases.Delegate', $delegate);
    })     



          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('purchases.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('purchases.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('purchases.Status', $types);
    })  
 

             
            ->join('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('purchases.Vendor', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('purchases.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('purchases.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('purchases.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('purchases.Date'
         ,'purchases.Time'
         ,'purchases.Code'
         ,'acccounting_manuals.Name as Vendor'
         ,'stores.Name as Store'
         ,'safes_banks.Name as Safe'
         ,'purchases.Total_Price'
         ,'purchases.Total_Discount'
         ,'purchases.Total_Taxes'
         ,'purchases.Pay'
              ,'employesses.Name as Delegate'
           ,'products_purchases.Product_Code as Product_Code'
              ,'products_purchases.P_Ar_Name as P_Ar_Name'
              ,'products_purchases.P_En_Name as P_En_Name'
              ,'products_purchases.V_Name as V_Name'
              ,'products_purchases.VV_Name as VV_Name'
              ,'products_purchases.Qty as Qty'
              ,'products_purchases.Price as Price'
              ,'products_purchases.Discount as Discount'
              ,'products_purchases.Tax as Tax'
              ,'products_purchases.Total_Bf_Tax as Total_Bf_Tax'
              ,'products_purchases.Total_Tax as TotalTax'
              ,'products_purchases.Total as Total'


        )
                  ->get();
   }else{
       
       
           $prods = DB::table('purchases')->whereBetween('purchases.Date',[$from,$to])
             
                ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('purchases.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('purchases.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('purchases.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('purchases.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('purchases.Code', $code);
    }) 
          
 
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('purchases.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('purchases.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('purchases.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('purchases.Vendor', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('purchases.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('purchases.Delegate', $delegate);
    })     



          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('purchases.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('purchases.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('purchases.Status', $types);
    })  
 

             
            ->join('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('purchases.Vendor', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('purchases.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('purchases.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('purchases.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('purchases.Date'
         ,'purchases.Time'
         ,'purchases.Code'
         ,'acccounting_manuals.NameEn as Vendor'
         ,'stores.NameEn as Store'
         ,'safes_banks.NameEn as Safe'
         ,'purchases.Total_Price'
         ,'purchases.Total_Discount'
         ,'purchases.Total_Taxes'
         ,'purchases.Pay'
              ,'employesses.NameEn as Delegate'
           ,'products_purchases.Product_Code as Product_Code'
              ,'products_purchases.P_Ar_Name as P_Ar_Name'
              ,'products_purchases.P_En_Name as P_En_Name'
              ,'products_purchases.V_Name as V_Name'
              ,'products_purchases.VV_Name as VV_Name'
              ,'products_purchases.Qty as Qty'
              ,'products_purchases.Price as Price'
              ,'products_purchases.Discount as Discount'
              ,'products_purchases.Tax as Tax'
              ,'products_purchases.Total_Bf_Tax as Total_Bf_Tax'
              ,'products_purchases.Total_Tax as TotalTax'
              ,'products_purchases.Total as Total'
        )
                  ->get();
       
       
       
   }

        


        
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
       'Date',
          'Time',
          'Code',
          'Client',
          'Store',
          'Safe',
          'Total_Price',
          'Total_Discount',
          'Total_Tax',
          'Pay',
          'Delegate',
          'Product_Code',
          'P_Ar_Name',
          'P_En_Name',
          'V_Name',
          'VV_Name',
          'Qty',
          'Price',
          'Discount',
          'Tax',
          'Total_Bf_Tax',
          'TotalTax',
          'Total',

        ];
    }
    
    
    

}

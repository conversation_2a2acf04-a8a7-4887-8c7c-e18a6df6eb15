<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductMovesColumnsSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_moves_columns_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Product_Code');
            $table->string('Product_Name');
            $table->string('Unit');
            $table->string('Type');
            $table->string('Bill_Num');
            $table->string('Incom');
            $table->string('Outcom');
            $table->string('Credit');
            $table->string('Group');
            $table->string('Brand')->nullable();
            $table->string('Store');
            $table->string('User');
            $table->string('Safe');
            $table->string('Branch');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_moves_columns_sechdules');
    }
}
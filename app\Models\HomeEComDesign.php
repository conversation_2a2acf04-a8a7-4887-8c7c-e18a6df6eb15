<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeEComDesign extends Model
{
    use HasFactory;
    protected $table = 'home_e_com_designs';
      protected $fillable = [
        'Slider_BG_Type',
        'Slider_BG_Image',
        'Slider_BG_Color',
        'Slider_Button_BG_Color',
        'Slider_Button_Txt_Color',
        'Slider_Button_Hover_BG_Color',
        'Slider_Button_Hover_Txt_Color',
        'Slider_Title_Txt_Color',
        'Slider_Desc_Txt_Color',
        'Ads_Top_Img_First_BG_Color',  
          'Ads_Top_Img_First_Before_BG_Color',
        'Ads_Top_Img_Second_BG_Color',
        'Ads_Top_Img_Second_Before_BG_Color',
        'Ads_Top_Img_Button_BG_Color',
        'Ads_Top_Img_Button_Txt_Color',
        'Ads_Top_Img_Button_Hover_BG_Color',
        'Ads_Top_Img_Button_Hover_Txt_Color',
        'Support_Icons_BG_Color',
        'Support_Icons_Txt_Color',      
          'Support_Icons_Color',
        'Ads_Bootom_Imgs_BG_Color',
        'Ads_Bootom_Imgs_Middle_BG_Color',
        'Ads_Bootom_Imgs_Button_BG_Color',
        'Ads_Bootom_Imgs_Button_Txt_Color',
        'Ads_Bootom_Imgs_Button_Hover_BG_Color',
        'Ads_Bootom_Imgs_Button_Hover_Txt_Color',
        'Partners_BG_Color',
      

    ];
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
class Admin extends Authenticatable
{
    use HasFactory, Notifiable;
    use HasRoles;
    protected $table = 'admins';
    protected $fillable = [
        'email',
        'name',
        'nameEn',
        'password',
        'image',
        'phone',
        'hidden',
        'emp',
        'ship',
        'vend',
        'cli',
        'safe',
        'store',
        'account',
        'type',
        'status',
        'roles_name',
        'lat',
        'long',
        'code',
        'token',
        'price_sale',
        'discount',
        'price_1',
        'price_2',
        'price_3',
        'pos_pay',
        'executor',
        'cost_price',
        'price_level',
        'guest',
        'pos_stores',
        'pos_hold',
        'cost_price_purch',
        'cost_price_sales',
        'manu_order_precent',
           'pos_product',
           'Cash',
           'Later',
           'Check',
           'Installment',
           'Cash_Visa',
           'Cash_Collection',
           'Delivery',
           'InstallmentCompanies',
           'Date',
           'ticket_price',
           'ticket_discount',
           'package',
           'job_order_price',
        
    ];

 protected $hidden = [
        'password',
        'remember_token',
    ];
    
                   public function package()
    {
        return $this->belongsTo(Packages::class,'package');
    }
    
    public function UsersMoves()
    {
        return $this->hasOne(UsersMoves::class);
    }
    
       public function AcccountingManual()
    {
        return $this->hasOne(AcccountingManual::class);
    }
    
         public function GeneralDaily()
    {
        return $this->hasOne(GeneralDaily::class);
    }
    
           public function IncomChecks()
    {
        return $this->hasOne(IncomChecks::class);
    }
    
            public function ExportChecks()
    {
        return $this->hasOne(ExportChecks::class);
    }
    
                    public function SafesBanks()
    {
        return $this->hasOne(SafesBanks::class);
    }
    
                     public function Stores()
    {
        return $this->hasOne(Stores::class);
    }
    
                       public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }
    
                         public function StartPeriods()
    {
        return $this->hasOne(StartPeriods::class);
    }
    
                       public function Inventory()
    {
        return $this->hasOne(Inventory::class);
    }
    
    
                          public function Settlement()
    {
        return $this->hasOne(Settlement::class);
    }
    
                              public function SafeTransfers()
    {
        return $this->hasOne(SafeTransfers::class);
    }
    
                              public function StorsTransfers()
    {
        return $this->hasOne(StorsTransfers::class);
    }
    
    public function Employess()
    {
        return $this->hasOne(Employess::class);
    }
    
    
    
           public function emp()
    {
        return $this->belongsTo(Employess::class,'emp');
    }
    
              public function executor()
    {
        return $this->belongsTo(Employess::class,'executor');
    }
    
    
              public function cli()
    {
        return $this->belongsTo(Customers::class,'cli');
    }
    
    
               public function ship()
    {
        return $this->belongsTo(ShippingCompany::class,'ship');
    }
    
               public function vend()
    {
        return $this->belongsTo(Vendors::class,'vend');
    }
    
               public function safe()
    {
        return $this->belongsTo(AcccountingManual::class,'safe');
    }
    
               public function store()
    {
        return $this->belongsTo(Stores::class,'store');
    }
               public function account()
    {
        return $this->belongsTo(AcccountingManual::class,'account');
    }

                             public function Vendors()
    {
        return $this->hasOne(Vendors::class);
    }
    
         public function PurchasesOrder()
    {
        return $this->hasOne(PurchasesOrder::class);
    }
    
                     public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }
    
                        public function RecivedPurcht()
    {
        return $this->hasOne(RecivedPurcht::class);
    }
    
                         public function ReturnPurch()
    {
        return $this->hasOne(ReturnPurch::class);
    }
    
                           public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
     public function CustomersTickets()
    {
        return $this->hasOne(CustomersTickets::class);
    }
    
                    public function Interviews()
    {
        return $this->hasOne(Interviews::class);
    }
    
      
        public function Quote()
    {
        return $this->hasOne(Quote::class);
    }
    
           public function SalesOrder()
    {
        return $this->hasOne(SalesOrder::class);
    }
    
         public function Sales()
    {
        return $this->hasOne(Sales::class);
    }
    
                       public function RecivedSales()
    {
        return $this->hasOne(RecivedSales::class);
    }
    
    
                         public function ReturnSales()
    {
        return $this->hasOne(ReturnSales::class);
    }
    
              public function Borrowa()
    {
        return $this->hasOne(Borrowa::class);
    }
    
            public function Deduction()
    {
        return $this->hasOne(Deduction::class);
    }
    
                public function Entitlement()
    {
        return $this->hasOne(Entitlement::class);
    }
    
                  public function Holidays()
    {
        return $this->hasOne(Holidays::class);
    }
    
                   public function Attendance()
    {
        return $this->hasOne(Attendance::class);
    }
    
                   public function Departure()
    {
        return $this->hasOne(Departure::class);
    }
    
                   public function RegOverTime()
    {
        return $this->hasOne(RegOverTime::class);
    }
    
                      public function Loan()
    {
        return $this->hasOne(Loan::class);
    }
    
                    public function PaySalary()
    {
        return $this->hasOne(PaySalary::class);
    }
    
                       public function Assets()
    {
        return $this->hasOne(Assets::class);
    }
    
    
    
 
}

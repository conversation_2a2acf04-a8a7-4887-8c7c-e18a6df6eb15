<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateModulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('modules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Capital');
            $table->string('Accounts');
            $table->string('Stores');
            $table->string('CRM');
            $table->string('HR');
            $table->string('Manufacturing');
            $table->string('Maintenance');
            $table->string('Secretariat');
            $table->timestamps();
            $table->string('Petrol');
            $table->string('ECommerce');
            $table->string('Shipping');
            $table->string('Bill_Electronic');
            $table->string('Hotels');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('modules');
    }
}
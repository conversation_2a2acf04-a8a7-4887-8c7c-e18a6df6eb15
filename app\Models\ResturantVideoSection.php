<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantVideoSection extends Model
{
    use HasFactory;
                  protected $table = 'resturant_video_sections';
      protected $fillable = [
        'BG_Image',
        'Icon',
        'Title_Image',
        'Video_Link',
          
        'Image_1',
        'Image_2',
        'Image_3',
        'Image_4',
          
        'Number_1',
        'Number_2',
        'Number_3',
        'Number_4',
          
          
        'Arabic_Title_1',
        'Arabic_Title_2',
        'Arabic_Title_3',
        'Arabic_Title_4',
          
        'English_Title_1',
        'English_Title_2',
        'English_Title_3',
        'English_Title_4',
          
   
    ];
}

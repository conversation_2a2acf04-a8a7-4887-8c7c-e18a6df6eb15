<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReserveCourseStudents extends Model
{
    use HasFactory;
     protected $table = 'reserve_course_students';
      protected $fillable = [
        'Student',
        'Pay',
        'Cost',
        'Residual',
        'Constraint',
        'Reserve',
        'Dollar_Value',
      
    ];

         public function Reserve()
    {
        return $this->belongsTo(ReserveCourse::class,'Reserve');
    }       
    
    public function Student()
    {
        return $this->belongsTo(Students::class,'Student');
    }
}

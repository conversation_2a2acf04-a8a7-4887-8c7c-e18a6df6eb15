<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class Clients implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         //DB::table('upload_accountings')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('customers')->insert([

     
            'Code'	   =>$value[1]
            ,'Date'  =>$value[2]
            ,'Name'  =>$value[3]
            ,'Price_Level'  =>$value[4]
            ,'Phone'  =>$value[5]
            ,'email'  =>$value[6]
            ,'password'  =>$value[7]
            ,'ID_Number'  =>$value[8]
            ,'Address'  =>$value[9]
            ,'Qualifications'  =>$value[10]
            ,'Birthdate'  =>$value[11]
            ,'Social_Status'  =>$value[12]
            ,'Passport_Number'  =>$value[13]
            ,'Company_Name'  =>$value[14]
            ,'Commercial_Registration_No'  =>$value[15]
            ,'Tax_Card_No'  =>$value[16]
            ,'Bank_Account'  =>$value[17]
            ,'Image'  =>$value[18]
            ,'Next_Time'  =>$value[19]
            ,'Executions_Status'  =>$value[20]
            ,'Governrate'  =>$value[21]
            ,'City'  =>$value[22]
            ,'Responsible'  =>$value[23]
            ,'Activity'  =>$value[24]
            ,'Campagin'  =>$value[25]
            ,'ClientStatus'  =>$value[26]
            ,'Account'  =>$value[27]
            ,'User'  =>$value[28]
            ,'created_at'  =>$value[29]
            ,'updated_at'  =>$value[30]
            ,'Platform'  =>$value[31]
            ,'Contract_Start'  =>$value[32]
            ,'Contract_End'  =>$value[33]
            ,'code'  =>$value[34]
            ,'country'  =>$value[35]
            ,'Tax_Registration_Number'  =>$value[36]
            ,'Tax_activity_code'  =>$value[37]
            ,'work_nature'  =>$value[38]
            ,'Buliding_Num'  =>$value[39]
             ,'Street'  =>$value[40]
            ,'Postal_Code'  =>$value[41]
            ,'tax_magistrate'  =>$value[42]
            ,'Floor'  =>$value[43]
            ,'Room'  =>$value[44]
            ,'Landmark'  =>$value[45]
            ,'Add_Info'  =>$value[46]
            ,'Phone2'  =>$value[47]
            ,'Phone3'  =>$value[48]
            ,'Phone4'  =>$value[49]
            ,'Warranty'  =>$value[50]
            ,'Group'  =>$value[51]
            ,'Place'  =>$value[52]
            ,'Nationality'  =>$value[53]
            ,'Product'  =>$value[54]
            ,'token'  =>$value[55]
            ,'NameEn'  =>$value[56]
            ,'arr'  =>$value[57]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}




	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	

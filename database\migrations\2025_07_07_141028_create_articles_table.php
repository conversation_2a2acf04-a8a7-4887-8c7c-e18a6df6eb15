<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->text('English_Desc')->nullable();
            $table->string('Image')->nullable();
            $table->timestamp('Date')->nullable();
            $table->string('Sub_Image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('articles');
    }
}

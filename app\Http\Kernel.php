<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON><PERSON> extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Fruitcake\Cors\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \Spatie\Honeypot\ProtectAgainstSpam::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'Lang' => \App\Http\Middleware\Lang::class,
        'Admin' => \App\Http\Middleware\Admin::class,
        'IFNotGuest' => \App\Http\Middleware\IFNotGuest::class,
        'IFGuest' => \App\Http\Middleware\IFGuest::class,
        'IFAuth' => \App\Http\Middleware\IFAuth::class,
        'IFNotAuth' => \App\Http\Middleware\IFNotAuth::class,
        'CartCount' => \App\Http\Middleware\CartCount::class,
        'EXPIRED' => \App\Http\Middleware\EXPIRED::class,
        'CartResurantUse' => \App\Http\Middleware\CartResurantUse::class,
        
        'GalleryResurantUse' => \App\Http\Middleware\GalleryResurantUse::class,
        'ReviewsResurantUse' => \App\Http\Middleware\ReviewsResurantUse::class,
        'BlogsResurantUse' => \App\Http\Middleware\BlogsResurantUse::class,
        'PrivacyResurantUse' => \App\Http\Middleware\PrivacyResurantUse::class,
        'TermsResurantUse' => \App\Http\Middleware\TermsResurantUse::class,
        
        'ViewAuthECom' => \App\Http\Middleware\ViewAuthECom::class,
        'ViewAuthResturant' => \App\Http\Middleware\ViewAuthResturant::class,
        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,      
    ];
}

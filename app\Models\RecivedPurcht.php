<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecivedPurcht extends Model
{
    use HasFactory;
         protected $table = 'recived_purchts';
      protected $fillable = [
           'Code',
        'Date',
        'Total_Trans_Qty',
        'Total_Trans_Value',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Purchase',
        'User',
    ];

           public function Purchase()
    {
        return $this->belongsTo(Purchases::class,'Purchase');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

    
        public function RecivedPurchProducts()
    {
        return $this->hasOne(RecivedPurchProducts::class);
    }
 
}


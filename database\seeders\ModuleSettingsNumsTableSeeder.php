<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ModuleSettingsNumsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('module_settings_nums')->delete();
        
        \DB::table('module_settings_nums')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Branch_Select' => NULL,
                'Branch_Num' => NULL,
                'Store_Select' => NULL,
                'Store_Num' => NULL,
                'Users_Select' => NULL,
                'Users_Num' => NULL,
                'created_at' => '2022-04-03 15:16:24',
                'updated_at' => '2022-04-03 15:16:24',
                'Group1' => NULL,
                'Group2' => NULL,
                'Group3' => NULL,
                'Group4' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'Branch_Select' => '1',
                'Branch_Num' => '4',
                'Store_Select' => '0',
                'Store_Num' => NULL,
                'Users_Select' => '0',
                'Users_Num' => NULL,
                'created_at' => '2022-04-03 15:27:42',
                'updated_at' => '2022-04-03 15:27:42',
                'Group1' => NULL,
                'Group2' => NULL,
                'Group3' => NULL,
                'Group4' => NULL,
            ),
        ));
        
        
    }
}
<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ProductFile implements ToCollection, WithChunkReading , WithBatchInserts
{
 
    public function collection(Collection $collection)
    {
        

         DB::table('import_new_prods')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('import_new_prods')->insert([

            'Name'	   =>$value[1]
            ,'Type'	   =>$value[2]
            ,'Group'  =>$value[3]
            ,'Brand'  =>$value[4]
            ,'Unit'  =>$value[5]
            ,'Rate'  =>$value[6]
            ,'Barcode'  =>$value[7]
            ,'Price_1'  =>$value[8]
            ,'Price_2'  =>$value[9]
            ,'Price_3'  =>$value[10]
            ,'Def'  =>$value[11]
            ,'Num'  =>$value[12]
            ,'created_at'  =>$value[13]
            ,'updated_at'  =>$value[14]
            ,'Code_Type'  =>$value[15]
            ,'World_Code'  =>$value[16]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

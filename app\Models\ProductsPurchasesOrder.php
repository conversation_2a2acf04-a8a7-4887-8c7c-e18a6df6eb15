<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsPurchasesOrder extends Model
{
    use HasFactory;
         protected $table = 'products_purchases_orders';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'Qty',
        'Price',
        'Discount',
        'TDiscount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
        'Product',
        'Exp_Date',
        'V1',
        'V2',
        'Unit',
        'P_Order',
        'Date',
        'Vendor',
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
         public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function P_Order()
    {
        return $this->belongsTo(PurchasesOrder::class,'P_Order');
    }
    
                public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
    
}

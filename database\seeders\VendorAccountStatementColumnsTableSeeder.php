<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VendorAccountStatementColumnsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('vendor_account_statement_columns')->delete();
        
        \DB::table('vendor_account_statement_columns')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Refrence_Number' => '1',
                'Branch' => '1',
                'Store' => '1',
                'Payment_Method' => '1',
                'Safe' => '1',
                'Type' => '1',
                'Shipping' => '1',
                'Cost_Center' => '1',
                'User' => '1',
                'Coin' => '1',
                'Due_Date' => '1',
                'Delegate' => '1',
                'Note' => '1',
                'Total_Return' => '1',
                'Total_Price' => '1',
                'Total_Discount' => '1',
                'Total_Tax' => '1',
                'Total_Net' => '1',
                'Paid' => '1',
                'Residual' => '1',
                'Vendor' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-16 22:39:00',
                'ShiftCode' => '1',
                'Executor' => '1',
            ),
        ));
        
        
    }
}
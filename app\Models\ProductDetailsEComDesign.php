<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductDetailsEComDesign extends Model
{
    use HasFactory;
    protected $table = 'product_details_e_com_designs';
      protected $fillable = [
        'Title_Color',
        'Txt_Color',
        'Price_Color',
        'Rate_Color',
        'Select_BG_Color',
        'Select_Txt_Color',
        'Qty_BG_Color',
        'Qty_Txt_Color',
        'Qty_Input_BG_Color',
        'Qty_Input_Txt_Color',  
          'Comment_Input_BG_Color',
        'Comment_Button_BG_Color',
        'Comment_Button_Txt_Color',
        'Comment_Button_BG_Hover_Color',
        'Comment_Button_Txt_Hover_Color',
        'Related_Title_BG_Color',
        'Related_Title_Txt_Color',
        'Related_Product_BG_Color',
        'Related_Product_Group_BG_Color',      
          'Related_Product_Group_Txt_Color',
        'Related_Product_Group_Hover_BG_Color',
        'Related_Product_Group_Hover_Txt_Color',
        'Related_Product_Icon_BG_Color',
        'Related_Product_Icon_Txt_Color',
        'Related_Product_Icon_Hover_BG_Color',
        'Related_Product_Icon_Hover_Txt_Color',
        'Related_Product_Txt_Color',
        'Related_Product_Price_Color',
        'Related_Product_Hover_Price_Color',  
          'Related_Product_Rate_Color',


    ];
}

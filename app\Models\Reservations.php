<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reservations extends Model
{
    use HasFactory;
       protected $table = 'reservations';
      protected $fillable = [
        'Code',
        'Date',
        'From',
        'To',
        'Amount',
        'Checkout',
        'Expire',
        'Draw',
        'Client',
        'Safe',
        'Coin',
        'Room',
        'User',
        'Note',
   
    ];
    
    
      public function Room()
    {
        return $this->belongsTo(Rooms::class,'Room');
    }
    
        public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
}

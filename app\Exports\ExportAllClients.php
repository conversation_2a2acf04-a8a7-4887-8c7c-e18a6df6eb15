<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllClients implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
   
        
        
           if(app()->getLocale() == 'ar' ){ 
        
           $prods = DB::table('customers')
               
          
                  
                   ->leftJoin('client_statuses', function ($join) {
    
            $join->on('customers.ClientStatus', '=', 'client_statuses.id');
        })     
               
               ->leftJoin('campaigns', function ($join) {
    
            $join->on('customers.Campagin', '=', 'campaigns.id');
        })  
               
               ->leftJoin('activites', function ($join) {
    
            $join->on('customers.Activity', '=', 'activites.id');
        })   
               

               ->leftJoin('platforms', function ($join) {
    
            $join->on('customers.Platform', '=', 'platforms.id');
        })  
               
               ->leftJoin('places', function ($join) {
    
            $join->on('customers.Place', '=', 'places.id');
        })   
               
               ->leftJoin('governrates', function ($join) {
    
            $join->on('customers.Governrate', '=', 'governrates.id');
        })   
               
               ->leftJoin('cities', function ($join) {
    
            $join->on('customers.City', '=', 'cities.id');
        })  
               
               ->leftJoin('countris', function ($join) {
    
            $join->on('customers.Nationality', '=', 'countris.id');
        })   
               
               ->leftJoin('customers_groups', function ($join) {
    
            $join->on('customers.Group', '=', 'customers_groups.id');
        })
               
               
                 ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('customers.Account', '=', 'acccounting_manuals.id');
        })
               
                 ->leftJoin('employesses', function ($join) {
    
            $join->on('customers.Responsible', '=', 'employesses.id');
        })
               
                 ->leftJoin('products', function ($join) {
    
            $join->on('customers.Product', '=', 'products.id');
        })
               
->select('customers.Code'
         ,'customers.Name'
         ,'customers.Price_Level'
         ,'customers.Phone'
         ,'customers.Phone2'
         ,'customers_groups.Arabic_Name as Group'
         ,'customers.Address'
         ,'customers.Company_Name'
         ,'governrates.Arabic_Name as Governrate'
        ,'cities.Arabic_Name as City'
          ,'employesses.Name as Responsible'
          ,'activites.Arabic_Name as Activity'
          ,'campaigns.Arabic_Name as Campagin'
          ,'client_statuses.Arabic_Name as ClientStatus'
          ,'acccounting_manuals.Name as Account'
          ,'platforms.Arabic_Name as Platform'
         ,'customers.Tax_Registration_Number'
         ,'customers.Tax_activity_code'
             ,'places.Arabic_Name as Place'
             ,'countris.Arabic_Name as Nationality'
             ,'products.P_Ar_Name as Product'

        )
                  ->get();
 
               
           }else{
               
             
           $prods = DB::table('customers')
               
          
                  
                   ->leftJoin('client_statuses', function ($join) {
    
            $join->on('customers.ClientStatus', '=', 'client_statuses.id');
        })     
               
               ->leftJoin('campaigns', function ($join) {
    
            $join->on('customers.Campagin', '=', 'campaigns.id');
        })  
               
               ->leftJoin('activites', function ($join) {
    
            $join->on('customers.Activity', '=', 'activites.id');
        })   
               

               ->leftJoin('platforms', function ($join) {
    
            $join->on('customers.Platform', '=', 'platforms.id');
        })  
               
               ->leftJoin('places', function ($join) {
    
            $join->on('customers.Place', '=', 'places.id');
        })   
               
               ->leftJoin('governrates', function ($join) {
    
            $join->on('customers.Governrate', '=', 'governrates.id');
        })   
               
               ->leftJoin('cities', function ($join) {
    
            $join->on('customers.City', '=', 'cities.id');
        })  
               
               ->leftJoin('countris', function ($join) {
    
            $join->on('customers.Nationality', '=', 'countris.id');
        })   
               
               ->leftJoin('customers_groups', function ($join) {
    
            $join->on('customers.Group', '=', 'customers_groups.id');
        })
               
               
                 ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('customers.Account', '=', 'acccounting_manuals.id');
        })
               
                 ->leftJoin('employesses', function ($join) {
    
            $join->on('customers.Responsible', '=', 'employesses.id');
        })
               
                 ->leftJoin('products', function ($join) {
    
            $join->on('customers.Product', '=', 'products.id');
        })
               
->select('customers.Code'
         ,'customers.NameEn'
         ,'customers.Price_Level'
         ,'customers.Phone'
         ,'customers.Phone2'
         ,'customers_groups.English_Name as Group'
         ,'customers.Address'
         ,'customers.Company_Name'
         ,'governrates.English_Name as Governrate'
        ,'cities.English_Name as City'
          ,'employesses.NameEn as Responsible'
          ,'activites.English_Name as Activity'
          ,'campaigns.English_Name as Campagin'
          ,'client_statuses.English_Name as ClientStatus'
          ,'acccounting_manuals.NameEn as Account'
          ,'platforms.English_Name as Platform'
         ,'customers.Tax_Registration_Number'
         ,'customers.Tax_activity_code'
             ,'places.English_Name as Place'
             ,'countris.English_Name as Nationality'
             ,'products.P_En_Name as Product'

        )
                  ->get();
      
               
           }
               
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Name',
          'Price_Level',
          'Phone',
          'Phone2',
          'Group',
          'Address',
          'Company_Name',
          'Governrate',
          'City',
          'Responsible',
          'Activity',
          'Campagin',
          'ClientStatus',
          'Account',
          'Platform',
          'Tax_Registration_Number',
          'Tax_activity_code',
          'Place',
          'Nationality',
          'Product',
         
        
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpInstallmentDetails extends Model
{
    use HasFactory;
      protected $table = 'emp_installment_details';
      protected $fillable = [
        'Date',
        'Value',
        'Status',
        'Emp',
        'Install',
      
    
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
          public function Install()
    {
        return $this->belongsTo(EmpInstallment::class,'Install');
    }
    
    
}

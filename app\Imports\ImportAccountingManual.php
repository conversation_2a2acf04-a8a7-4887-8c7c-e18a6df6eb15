<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;

class ImportAccountingManual implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        
        DB::table('upload_accountings')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('upload_accountings')->insert([

     
            'Name'  =>$value[1]
            ,'Type'  =>$value[2]
            ,'Parent'  =>$value[3]
            ,'Note'  =>$value[4]
            ,'User'  =>$value[5]
            ,'Account_Code'  =>$value[6]        
            ,'created_at'  =>$value[7]
            ,'updated_at'  =>$value[8]
           

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

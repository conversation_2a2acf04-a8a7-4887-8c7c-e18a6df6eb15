<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ImportInsurance_Paper implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('insurance_papers')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('insurance_papers')->insert([

         
            'Code'	   =>$value[1]
            ,'Date'  =>$value[2]
            ,'Draw'  =>$value[3]
            ,'Note'  =>$value[4]
            ,'Due_Date'  =>$value[5]
            ,'Amount'  =>$value[6]
            ,'Status'  =>$value[7]
            ,'Coin'  =>$value[8]
            ,'Cost_Center'  =>$value[9]
            ,'Account'  =>$value[10]
            ,'Bank'  =>$value[11]
            ,'User'  =>$value[12]
            ,'created_at'  =>$value[13]
            ,'updated_at'  =>$value[14]    
            ,'From'  =>$value[15]
            ,'To'  =>$value[16]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResignationRequest extends Model
{
    use HasFactory;
         protected $table = 'resignation_requests';
    protected $fillable = [
        'Code',
        'Date',
        'Resignation_Date',
        'File',
        'Emp',
        'Note',
        'Status',
        'Reason',
      
    ];
   
           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

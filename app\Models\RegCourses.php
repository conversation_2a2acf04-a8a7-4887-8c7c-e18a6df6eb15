<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegCourses extends Model
{
    use HasFactory;
         protected $table = 'reg_courses';
      protected $fillable = [

                'Code',
                'Date',
                'Hall',
                'Reserve_Course',
                'Teacher',
                'Teacher_Attend',
            
             

    ];
    
    
        public function Hall()
    {
        return $this->belongsTo(CoursesHalls::class,'Hall');
    } 
    
            public function Reserve_Course()
    {
        return $this->belongsTo(ReserveCourse::class,'Reserve_Course');
    } 
    
    
            public function Teacher()
    {
        return $this->belongsTo(Teachers::class,'Teacher');
    } 
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Customers extends Authenticatable
{
       use HasFactory, Notifiable;
        protected $table = 'customers';
    protected $fillable = [
        'code',
        'country',
        'Code',
        'Date',
        'Name',
        'NameEn',
        'Price_Level',
        'Phone',
        'Phone2',
        'Phone3',
        'Phone4',
        'email',
        'Group',
        'password',
        'ID_Number',
        'Address',
        'Qualifications',
        'Birthdate',
        'Social_Status',
        'Passport_Number',
        'Company_Name',
        'Commercial_Registration_No',
        'Tax_Card_No',
        'Bank_Account',
        'Image',
        'Next_Time',
        'Executions_Status',
        'Governrate',
        'City',
        'Responsible',
        'Activity',
        'Campagin',
        'ClientStatus',
        'Account',
        'Platform',
        'Contract_Start',
        'Contract_End',
        'User',
          'Tax_Registration_Number',
          'Tax_activity_code',
          'work_nature',
          'Place',
          'Nationality',
          'Buliding_Num',
          'Street',
          'Postal_Code',
          'tax_magistrate',
         'Floor',
          'Room',
          'Landmark',
          'Add_Info',
          'Product',
          'Warranty',
          'token',
          'SearchCode',
   
    ];
   
           public function Governrate()
    {
        return $this->belongsTo(Governrate::class,'Governrate');
    }
    
               public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function country()
    {
        return $this->belongsTo(Countris::class,'country');
    }
    
    
    
               public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
               public function Responsible()
    {
        return $this->belongsTo(Employess::class,'Responsible');
    }
    
               public function Activity()
    {
        return $this->belongsTo(Activites::class,'Activity');
    }
    
               public function Campagin()
    {
        return $this->belongsTo(Campaigns::class,'Campagin');
    }
    
               public function ClientStatus()
    {
        return $this->belongsTo(ClientStatus::class,'ClientStatus');
    }
    
               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
               public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
                 public function Platform()
    {
        return $this->belongsTo(Platforms::class,'Platform');
    }
    
        public function Group()
    {
        return $this->belongsTo(CustomersGroup::class,'Group');
    }
    
    public function CustomersFiles()
    {
        return $this->hasOne(CustomersFiles::class);
    }
    
         public function CustomersTickets()
    {
        return $this->hasOne(CustomersTickets::class);
    }
    
                       public function Interviews()
    {
        return $this->hasOne(Interviews::class);
    }
    
    
                       public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }
    
                  public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }
    

    
}

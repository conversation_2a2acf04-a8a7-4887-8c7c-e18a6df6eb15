<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMostSalesProductsColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('most_sales_products_columns', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Product_Code');
            $table->string('Product_Name');
            $table->string('Qty');
            $table->string('Price');
            $table->string('Discount');
            $table->string('Tax');
            $table->string('Total');
            $table->string('Store');
            $table->string('Date');
            $table->string('Unit');
            $table->string('Safe');
            $table->string('Branch');
            $table->string('Group');
            $table->string('Brand')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('most_sales_products_columns');
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateModuleSettingsNumsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('module_settings_nums', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Branch_Select')->nullable();
            $table->string('Branch_Num')->nullable();
            $table->string('Store_Select')->nullable();
            $table->string('Store_Num')->nullable();
            $table->string('Users_Select')->nullable();
            $table->string('Users_Num')->nullable();
            $table->timestamps();
            $table->string('Group1')->nullable();
            $table->string('Group2')->nullable();
            $table->string('Group3')->nullable();
            $table->string('Group4')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('module_settings_nums');
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBarcodeSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('barcode_settings', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Name');
            $table->string('Type');
            $table->string('Direction');
            $table->string('Width');
            $table->string('Height');
            $table->string('Padding_L');
            $table->string('Padding_R');
            $table->string('Padding_T');
            $table->string('Padding_B');
            $table->string('Margin_L');
            $table->string('Margin_R');
            $table->string('Margin_T');
            $table->string('Margin_B');
            $table->string('Barcode_Width');
            $table->string('Barcode_Height');
            $table->string('Font_Size');
            $table->string('Line_Height');
            $table->timestamps();
            $table->string('Height_Logo')->nullable();
            $table->string('Width_Logo')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('barcode_settings');
    }
}
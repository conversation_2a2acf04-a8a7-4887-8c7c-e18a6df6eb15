<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\AcccountingManual;
use App\Models\GeneralDaily;
use DB;
class ExportVendorsStatement implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->from;
        
  
        
         $from=$storex['from'];
         $to=$storex['to'];
         $account=$storex['account'];

        
           if(app()->getLocale() == 'ar' ){ 
           $prods = DB::table('acccounting_manuals')
               
                ->when(!empty($account), function ($query) use ($account) {
        return $query->whereIn('acccounting_manuals.id', $account);
    })    
               
                ->leftJoin('general_dailies', function ($join) {
                  $storex=$this->from;
          $from=$storex['from'];
         $to=$storex['to'];


            $join->on('acccounting_manuals.id', '=','general_dailies.Account')
    ->whereBetween('general_dailies.Date',[$from,$to])
                ;
        })   
              
               
        ->select('acccounting_manuals.Code','acccounting_manuals.Name','general_dailies.Debitor_Coin','general_dailies.Creditor_Coin')

                  ->get();
               
           }else{
               
                    $prods = DB::table('acccounting_manuals')
               
                ->when(!empty($account), function ($query) use ($account) {
        return $query->whereIn('acccounting_manuals.id', $account);
    })    
               
                ->leftJoin('general_dailies', function ($join) {
                  $storex=$this->from;
          $from=$storex['from'];
         $to=$storex['to'];


            $join->on('acccounting_manuals.id', '=','general_dailies.Account')
    ->whereBetween('general_dailies.Date',[$from,$to])
                ;
        })   
              
               
        ->select('acccounting_manuals.Code','acccounting_manuals.NameEn','general_dailies.Debitor_Coin','general_dailies.Creditor_Coin')

                  ->get();      
               
               
           }
               
               
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Account_Code',
          'Account_Name',
          'Total_Debitor',
          'Total_Creditor',

        
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiscountsEmp extends Model
{
    use HasFactory;
    protected $table = 'discounts_emps';
    protected $fillable = [
        'Emp',
        'Discount',
        'AmountDiscount',

   
    ];
   
           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
    
             public function Discount()
    {
        return $this->belongsTo(DeducationsTypes::class,'Discount');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VAProducts extends Model
{
    use HasFactory;
         protected $table = 'v_a_products';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Unit',
        'Product',
        'ProductID',
    ];
    
       
                public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }

           
                public function ProductID()
    {
        return $this->belongsTo(Products::class,'ProductID');
    }          
    
    public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommentsClients extends Model
{
    use HasFactory;
      protected $table = 'comments_clients';
    protected $fillable = [
        'Comment',
        'CommentEn',
        'Responsible',
        'Customer',
        'Code',
        'Date',
        'Rate',
        'Visit_Cost',
        'Note',

    ];
   
               public function Responsible()
    {
        return $this->belongsTo(Employess::class,'Responsible');
    }

               public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }
}

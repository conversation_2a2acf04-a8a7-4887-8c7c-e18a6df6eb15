<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use DB ;
class ExportMaintanceSalesReport implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
     
        $storex=$this->from;
        $from =  $storex['from'];
        $to =  $storex['to'];
        $Store =  $storex['Store'];
        $Client =  $storex['Client'];
        $Recipient =  $storex['Recipient'];
        $Eng =  $storex['Eng'];
        $Branch =  $storex['Branch'];
        $code =  $storex['code'];
        $User =  $storex['User'];
      
        
           if(app()->getLocale() == 'ar' ){
          $prods = DB::table('recipt_maintainces')->whereBetween('recipt_maintainces.Date',[$from,$to])
              
             ->whereIn('recipt_maintainces.Status',[5,8])
    ->when(!empty($code), function ($query) use ($code) {
        return $query->where('recipt_maintainces.Code',$code);  
               
                })       
        
       
        
        ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('recipt_maintainces.Account',$Client);  
               
                })      
        
        ->when(!empty($Recipient), function ($query) use ($Recipient) {
        return $query->where('recipt_maintainces.Recipient',$Recipient);  
               
                })     
        
        ->when(!empty($Eng), function ($query) use ($Eng) {
        return $query->where('recipt_maintainces.Eng',$Eng);  
               
                })     
        
        ->when(!empty($Store), function ($query) use ($Store) {
        return $query->where('recipt_maintainces.Store',$Store);  
               
                })  
                     
        
        ->when(!empty($Branch), function ($query) use ($Branch) {
        return $query->where('recipt_maintainces.Branch',$Branch);  
               
                })      
        
        ->when(!empty($User), function ($query) use ($User) {
        return $query->where('recipt_maintainces.User',$User);  
               
                })  
                

                     ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('recipt_maintainces.Account', '=', 'acccounting_manuals.id');
        })
              
               ->leftJoin('employesses', function ($join) {
    
            $join->on('recipt_maintainces.Eng', '=', 'employesses.id');
        })      
              
         ->leftJoin('employesses as Rec', function ($join) {
    
            $join->on('recipt_maintainces.Recipient', '=', 'employesses.id');
        })      
     
            
              ->leftJoin('admins', function ($join) {
    
            $join->on('recipt_maintainces.User', '=', 'admins.id');
        })
              

                   ->leftJoin('stores', function ($join) {
    
            $join->on('recipt_maintainces.Store', '=', 'stores.id');
        })
              
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })



->select('recipt_maintainces.Date'
         ,'recipt_maintainces.Code'
         ,'branches.Arabic_Name as Branch'
         ,'acccounting_manuals.Name as ClientName'
         ,'stores.Name as StoreName'
         ,'recipt_maintainces.Total'
         ,'Rec.Name as RecName'
         ,'employesses.Name as EngName'
         ,'admins.name as UserName'
         ,'recipt_maintainces.Note'
        )
                  ->get();
           }else{
               
       $prods = DB::table('recipt_maintainces')->whereBetween('recipt_maintainces.Date',[$from,$to])
              
             ->whereIn('recipt_maintainces.Status',[5,8])
    ->when(!empty($code), function ($query) use ($code) {
        return $query->where('recipt_maintainces.Code',$code);  
               
                })       
        
       
        
        ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('recipt_maintainces.Account',$Client);  
               
                })      
        
        ->when(!empty($Recipient), function ($query) use ($Recipient) {
        return $query->where('recipt_maintainces.Recipient',$Recipient);  
               
                })     
        
        ->when(!empty($Eng), function ($query) use ($Eng) {
        return $query->where('recipt_maintainces.Eng',$Eng);  
               
                })     
        
        ->when(!empty($Store), function ($query) use ($Store) {
        return $query->where('recipt_maintainces.Store',$Store);  
               
                })  
                     
        
        ->when(!empty($Branch), function ($query) use ($Branch) {
        return $query->where('recipt_maintainces.Branch',$Branch);  
               
                })      
        
        ->when(!empty($User), function ($query) use ($User) {
        return $query->where('recipt_maintainces.User',$User);  
               
                })  
                

                     ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('recipt_maintainces.Account', '=', 'acccounting_manuals.id');
        })
              
               ->leftJoin('employesses', function ($join) {
    
            $join->on('recipt_maintainces.Eng', '=', 'employesses.id');
        })      
              
         ->leftJoin('employesses as Rec', function ($join) {
    
            $join->on('recipt_maintainces.Recipient', '=', 'employesses.id');
        })      
     
            
              ->leftJoin('admins', function ($join) {
    
            $join->on('recipt_maintainces.User', '=', 'admins.id');
        })
              

                   ->leftJoin('stores', function ($join) {
    
            $join->on('recipt_maintainces.Store', '=', 'stores.id');
        })
              
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })



->select('recipt_maintainces.Date'
         ,'recipt_maintainces.Code'
         ,'branches.English_Name as Branch'
         ,'acccounting_manuals.NameEn as ClientName'
         ,'stores.NameEn as StoreName'
         ,'recipt_maintainces.Total'
         ,'Rec.NameEn as RecName'
         ,'employesses.NameEn as EngName'
         ,'admins.nameEn as UserName'
         ,'recipt_maintainces.Note'
        )
                  ->get();           
               
               
           }
 
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Branch',
          'Client',
          'Store',
          'Total_Price',
          'Recipient',
          'Eng',
          'User',
          'Note',
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegOverTime extends Model
{
    use HasFactory;
            protected $table = 'reg_over_times';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Amount',
        'Hours_Number',
        'Hour_Rate',
        'Total_Hours',
        'Hour_Cost',
        'Note',
        'Emp',
        'Type',
        'User',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
         public function Type()
    {
        return $this->belongsTo(OverTimes::class,'Type');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

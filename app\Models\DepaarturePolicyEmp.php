<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DepaarturePolicyEmp extends Model
{
    use HasFactory;
               protected $table = 'depaarture_policy_emps';
      protected $fillable = [
        'From',
        'To',
        'Discount',
        'Emp',
       
       
    ];
    
        public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

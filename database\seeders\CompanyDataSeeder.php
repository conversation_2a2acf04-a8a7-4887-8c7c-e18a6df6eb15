<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CompanyData;

class CompanyDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        CompanyData::create([
            'Name' => 'شركة OST ERP',
            'Phone1' => '*********',
            'Phone2' => null,
            'Phone3' => null,
            'Phone4' => null,
            'Address' => 'العنوان الافتراضي',
            'Logo' => null,
            'Icon' => null,
            'Print_Text' => 'نص الطباعة الافتراضي',
            'Print_Text_Footer' => 'تذييل الطباعة',
            'Print_Text_Footer_Sales' => 'تذييل فاتورة المبيعات',
            'Print_Text_Footer_Quote' => 'تذييل عرض الأسعار',
            'Print_Text_Footer_Manufacturing' => 'تذييل التصنيع',
            'Seal' => null,
            'Name_Sales_Bill' => 'فاتورة مبيعات',
            'Name_Sales_Order_Bill' => 'أمر مبيعات',
            'Name_Quote_Bill' => 'عرض أسعار',
            'Logo_Store' => null,
            'Icon_Store' => null,
            'View' => '1',
            'Print_Text_Footer_Secretariat' => 'تذييل السكرتارية',
            'Commercial_Record' => '*********',
            'Tax_File_Number' => '*********',
            'Tax_Registration_Number' => '*********',
            'Tax_activity_code' => '123456',
            'work_nature' => 'طبيعة العمل',
            'Governrate' => null,
            'City' => null,
            'Place' => null,
            'Nationality' => null,
            'Buliding_Num' => '123',
            'Street' => 'الشارع الرئيسي',
            'Postal_Code' => '12345',
            'tax_magistrate' => 'مأمورية الضرائب',
            'Client_ID' => '*********',
            'Serial_Client_ID' => '*********',
            'Version_Type' => '1',
            'Computer_SN' => 'DEFAULT-SN',
            'Invoice_Type' => '1',
            'Floor' => '1',
            'Room' => '1',
            'Landmark' => 'معلم مميز',
            'Add_Info' => 'معلومات إضافية',
        ]);
    }
}

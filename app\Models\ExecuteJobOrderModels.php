<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExecuteJobOrderModels extends Model
{
    use HasFactory;
       protected $table = 'execute_job_order_models';
      protected $fillable = [

        'Model',                         
        'Qty',
        'Total_Cost',
        'Outcome',
        'Execute',
      
    ];

    
    public function Model()
    {
        return $this->belongsTo(ManufacturingModel::class,'Model');
    }         
        

    public function Outcome()
    {
        return $this->belongsTo(Products::class,'Outcome');
    }         
        

    public function Execute()
    {
        return $this->belongsTo(ExecuteJobOrder::class,'Execute');
    }         
    

}

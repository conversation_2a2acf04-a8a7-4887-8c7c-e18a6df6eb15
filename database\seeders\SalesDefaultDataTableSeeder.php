<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SalesDefaultDataTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('sales_default_data')->delete();
        
        \DB::table('sales_default_data')->insert(array (
            0 => 
            array (
                'id' => 3,
                'Payment_Method' => 'Cash',
                'Status' => '1',
                'V_and_C' => '0',
                'Mainus' => '0',
                'Price_Sale' => '0',
                'Safe' => 30,
                'Client' => 34,
                'Delegate' => 31,
                'Store' => 4,
                'Coin' => 1,
                'created_at' => '2022-04-13 02:52:36',
                'updated_at' => '2022-05-24 00:27:36',
                'Brand' => NULL,
                'Group' => NULL,
                'English_Name' => NULL,
                'Expire' => NULL,
                'Draw' => '1',
                'Shift_Pass' => '1234',
                'Empp' => '0',
                'Discount' => '0',
                'Delivery' => '34',
                'Execute_Precent' => 'Total_Cost',
                'StoresQty' => NULL,
                'DelegateEmp' => NULL,
                'TaxType' => NULL,
            ),
        ));
        
        
    }
}
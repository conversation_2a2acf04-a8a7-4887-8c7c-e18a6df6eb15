<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomManufacturingSecretariatModel extends Model
{
    use HasFactory;
         protected $table = 'incom_manufacturing_secretariat_models';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Precent',
        'Qty',
        'Cost',
        'Discount',
        'Tax',
        'Total_Bf_Tax',
        'Total',
        'Total_Tax',
        'Depreciation',
        'Depreciation_Qty',
        'Store',
        'Product',
        'Unit',
        'Model',
    
    ];

         public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Model()
    {
        return $this->belongsTo(ManufacturingSecretariatModel::class,'Model');
    }
    
                public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
}

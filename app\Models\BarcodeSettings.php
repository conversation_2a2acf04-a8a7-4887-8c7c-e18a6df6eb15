<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BarcodeSettings extends Model
{
    use HasFactory;
      protected $table = 'barcode_settings';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Type',
        'Direction',
        'Width',
        'Height',
        'Padding_L',
        'Padding_R',
        'Padding_T',
        'Padding_B',
        'Margin_L',
        'Margin_R',
        'Margin_T',
        'Margin_B',
        'Barcode_Width',
        'Barcode_Height',
        'Font_Size',
        'Line_Height',
        'Width_Logo',
        'Height_Logo',
   
    ];
    
    
}

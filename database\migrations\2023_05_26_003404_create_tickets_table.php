<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            		$table->longText('Code')->nullable();
            		$table->longText('Date')->nullable();
            		$table->longText('Payment_Method')->nullable();
            		$table->longText('Sender_Name')->nullable();
            		$table->longText('Sender_Address')->nullable();
            		$table->longText('Sender_Phone')->nullable();
            		$table->longText('Addressees_Name')->nullable();
            		$table->longText('Addressees_Address')->nullable();
            		$table->longText('Addressees_Phone')->nullable();
            		$table->longText('Notes')->nullable();
            		$table->longText('Sub_Total')->nullable();
            		$table->longText('Discount')->nullable();
            		$table->longText('Total')->nullable();
            		$table->longText('Store')->nullable();
            		$table->longText('Safe')->nullable();
            		$table->longText('Recived')->nullable();
            		$table->longText('Selected')->nullable();
            
            		$table->longText('Coin')->nullable();
            		$table->longText('Draw')->nullable();
            		$table->longText('Product_Numbers')->nullable();
            		$table->longText('Total_Qty')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tickets');
    }
}

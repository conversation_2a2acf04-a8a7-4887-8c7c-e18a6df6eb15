<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientStatus extends Model
{
    use HasFactory;
                protected $table = 'client_statuses';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StartPeriods extends Model
{
    use HasFactory;
       protected $table = 'start_periods';
      protected $fillable = [
        'Code',
        'Date',
        'Store',
        'Draw',
        'Note',
        'Total_Products',
        'Total_Qty',
        'Total_Price',
        'Coin',
        'User',
             'Time',
        'Branch',

       
    ];
    
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
         public function ProductsStartPeriods()
    {
        return $this->hasOne(ProductsStartPeriods::class);
    }
    
           public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
   
}

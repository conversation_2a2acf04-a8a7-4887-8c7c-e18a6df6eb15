<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Disclaimer extends Model
{
    use HasFactory;
     protected $table = 'disclaimers';
    protected $fillable = [
        'Date',
        'File',
        'Emp',
        'Note',
      
      
    ];
   
           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

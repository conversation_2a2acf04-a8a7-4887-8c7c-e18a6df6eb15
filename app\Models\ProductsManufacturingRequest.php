<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsManufacturingRequest extends Model
{
    use HasFactory;
      protected $table = 'products_manufacturing_requests';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Qty',
        'Price',
        'Discount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Exp_Date',
        'Store',
        'Product',
        'V1',
        'V2',
        'Unit',
        'Request',
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
               public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
            public function Request()
    {
        return $this->belongsTo(ManufacturingRequest::class,'Request');
    }
    
      
}

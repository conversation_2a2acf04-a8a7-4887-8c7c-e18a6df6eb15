<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketProducts extends Model
{
    use HasFactory;
      protected $table = 'ticket_products';
      protected $fillable = [

                'Product_Code',
                'P_Ar_Name',
                'P_En_Name',
                'Price',
                'Weight',
                'Length',
                'Width',
                'Height',
                'Qty',
                'Unit',
                'Total',
                'Store',
                'Product',
                'Ticket',
 
          
    ];
    
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
               public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
                  public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
    
               public function Ticket()
    {
        return $this->belongsTo(Ticket::class,'Ticket');
    }
}

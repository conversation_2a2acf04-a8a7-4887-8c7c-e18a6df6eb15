<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectTeam extends Model
{
    use HasFactory;
          protected $table = 'project_teams';
      protected $fillable = [

                'Member',
                'Project',
           

    ];
    
        public function Member()
    {
        return $this->belongsTo(Employess::class,'Member');
    }
    
        public function Project()
    {
        return $this->belongsTo(Projects::class,'Project');
    }
}

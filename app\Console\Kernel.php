<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{

    public $commands = [
       \App\Console\Commands\DB_Backup::class,
       \App\Console\Commands\LifSpanAsset::class,
    ];


    public function schedule(Schedule $schedule)
    {
      $schedule->command('DB:BACKUP')->daily();
      $schedule->command('Life:Asset')->monthly();
      $schedule->command('Salary:Due')->monthly();

        
    }

 
    public function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

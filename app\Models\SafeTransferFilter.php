<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SafeTransferFilter extends Model
{
    use HasFactory;
         protected $table = 'safe_transfer_filters';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Amount',
        'Note',
        'From_Safe',
        'To_Safe',
        'Coin',
        'Cost_Center',
        'User',
        'Status',
        'File',
        'OldAmount',
        'Edit',
        'Delegate',
        'Time',
        'ID',
        'Type',
        'Branch',
          
       
    ];
    
        public function From_Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'From_Safe');
    }
         public function To_Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'To_Safe');
    }
         public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
         public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
    
                 public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
              public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
                public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
}

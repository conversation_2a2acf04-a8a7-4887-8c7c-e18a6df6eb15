<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeachersSubjects extends Model
{
    use HasFactory;
      protected $table = 'teachers_subjects';
      protected $fillable = [

                'Teacher',
                'Subject',

           
            
 
          
    ];
    
    
        public function Teacher()
    {
        return $this->belongsTo(Teachers::class,'Teacher');
    }    
        public function Subject()
    {
        return $this->belongsTo(ScientificMaterial::class,'Subject');
    }    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssemblyProducts extends Model
{
    use HasFactory;
           protected $table = 'assembly_products';
      protected $fillable = [

                'Qty',
                'Price',
                'Total',
                'P_Ar_Name',
                'P_En_Name',
                'P_Code',
                'Unit',
                'Product',
                'p_id',
          
    ];
    
        public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
          public function p_id()
    {
        return $this->belongsTo(Products::class,'p_id');
    }
    
    
}

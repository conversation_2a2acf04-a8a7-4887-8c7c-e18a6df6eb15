<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class Groupss implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         //DB::table('products')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('items_groups')->insert([

            'id'	   =>$value[0]
            ,'Code'	   =>$value[1]
            ,'Name'  =>$value[2]
            ,'Type'  =>$value[3]
            ,'Parent'  =>$value[4]
            ,'Note'  =>$value[5]
            ,'Image'  =>$value[6]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaigns extends Model
{
    use HasFactory;
            protected $table = 'campaigns';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Platform',
   
    ];
    
               public function Platform()
    {
        return $this->belongsTo(Platforms::class,'Platform');
    }
    
                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
    
    
    
}

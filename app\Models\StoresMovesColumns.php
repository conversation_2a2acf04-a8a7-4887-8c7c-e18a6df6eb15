<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoresMovesColumns extends Model
{
    use HasFactory;
        protected $table = 'stores_moves_columns';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',                             
        'Branch',               
        'Store',                         
        'Safe',               
        'Type',                           
        'Cost_Center',                            
        'User',               
        'Coin',                             
        'Note',               
        'Total_Qty',               
        'Total_Price',                          
        'Account',               
        'Ship',    
  
           


    ];

}

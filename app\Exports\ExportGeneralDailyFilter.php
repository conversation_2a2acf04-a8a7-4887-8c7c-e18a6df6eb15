<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use DB;
class ExportGeneralDailyFilter implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
  

        $storex=$this->from;
        
        $from =  $storex['from'];
         $to = $storex['to'] ;
        $coin =  $storex['coin'] ;
         $account = $storex['account'] ;
        $type =   $storex['type'] ;
         $cost = $storex['cost'] ;
         $user = $storex['user'] ;
        

             
        
           if(app()->getLocale() == 'ar' ){ 
                    $items =DB::table('general_dailies')->whereBetween('general_dailies.Date', [$from, $to])  
                        
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin',$coin);  
               
                })  
                        
                ->when(!empty($account), function ($query) use ($account) {
        return $query->where('general_dailies.Account',$account);  
               
                })  
                        
                        
                ->when(!empty($type), function ($query) use ($type) {
        return $query->where('general_dailies.Type',$type);  
               
                })  
                        
                        
                ->when(!empty($cost), function ($query) use ($cost) {
        return $query->where('general_dailies.Cost_Center',$cost);  
               
                })  
                        
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->where('general_dailies.userr',$user);  
               
                })    
                        
                        
            ->join('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })                     
                     
                        
            ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })   
                  
                        
      ->join('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })      
                        
      ->join('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })                     
                                      
                        
        ->select('general_dailies.Date'
                ,'general_dailies.Code' 
                ,'general_dailies.Code_Type' 
                ,'general_dailies.Type' 
                ,'general_dailies.Debitor' 
                ,'general_dailies.Creditor' 
                ,'general_dailies.Statement' 
                ,'acccounting_manuals.Code as Account_Code' 
                ,'acccounting_manuals.Name as Account_Name' 
                ,'cost_centers.Arabic_Name as CostCenter' 
                ,'coins.Arabic_Name as Coin' 
                   ,'general_dailies.Draw' 
                ,'general_dailies.Debitor_Coin' 
                ,'general_dailies.Creditor_Coin' 
                 ,'admins.name as User' 
                )                
          ->get();
        
           }else{
          
               
               
                       $items =DB::table('general_dailies')->whereBetween('general_dailies.Date', [$from, $to])  
                        
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin',$coin);  
               
                })  
                        
                ->when(!empty($account), function ($query) use ($account) {
        return $query->where('general_dailies.Account',$account);  
               
                })  
                        
                        
                ->when(!empty($type), function ($query) use ($type) {
        return $query->where('general_dailies.Type',$type);  
               
                })  
                        
                        
                ->when(!empty($cost), function ($query) use ($cost) {
        return $query->where('general_dailies.Cost_Center',$cost);  
               
                })  
                        
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->where('general_dailies.userr',$user);  
               
                })    
                        
                        
            ->join('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })                     
                     
                        
            ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })   
                  
                        
      ->join('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })      
                        
      ->join('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })                     
                                      
                        
        ->select('general_dailies.Date'
                ,'general_dailies.Code' 
                ,'general_dailies.Code_Type' 
                ,'general_dailies.Type' 
                ,'general_dailies.Debitor' 
                ,'general_dailies.Creditor' 
                ,'general_dailies.Statement' 
                ,'acccounting_manuals.Code as Account_Code' 
                ,'acccounting_manuals.NameEn as Account_Name' 
                ,'cost_centers.English_Name as CostCenter' 
                ,'coins.English_Name as Coin' 
                   ,'general_dailies.Draw' 
                ,'general_dailies.Debitor_Coin' 
                ,'general_dailies.Creditor_Coin' 
                 ,'admins.nameEn as User' 
                )                
          ->get();
        
               
               
               
           }



        return collect($items);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Bond_Code',
          'Bond_Type',
          'Debitor',
          'Creditor',
          'Statement',
          'Account_Code',
          'Account_Name',
          'Cost_Centers',
          'Coin',
          'Draw',
          'Coin_Debitor',
          'Coin_Creditor',
          'User',
        ];
    }
    
    
    

}

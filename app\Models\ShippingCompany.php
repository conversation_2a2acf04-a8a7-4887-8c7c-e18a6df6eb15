<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingCompany extends Model
{
    use HasFactory;
       protected $table = 'shipping_companies';
      protected $fillable = [
        'Name',
        'NameEn',
        'Phone',
        'Tax_Card',
        'Commercial_Register',     
        'Account',     
    ];
    
            public function Sales()
    {
        return $this->hasOne(Sales::class);
    }
    
            public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }

         public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }


    
}

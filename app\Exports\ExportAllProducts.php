<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllProducts implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
   
        
        
         if(app()->getLocale() == 'ar' ){ 
           $prods = DB::table('products')
            ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product');
        })
                  
                   ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           ->join('measuerments', function ($join) {
    
            $join->on('product_units.Unit', '=', 'measuerments.id');
        })          
        
->select('products.P_Type'
         ,'products.P_Ar_Name'
         ,'products.P_En_Name'
         ,'items_groups.Name as GroupName'
         ,'brands.Name as BrandName'
         ,'measuerments.Name as UnitName'
         ,'product_units.Rate'
         ,'product_units.Barcode'
         ,'product_units.Price'
         ,'product_units.Price_Two'
         ,'product_units.Price_Three'
         ,'product_units.Def'
         
        )
                  ->get();
 
         }else{
             
               $prods = DB::table('products')
            ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product');
        })
                  
                   ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           ->join('measuerments', function ($join) {
    
            $join->on('product_units.Unit', '=', 'measuerments.id');
        })          
        
->select('products.P_Type'
         ,'products.P_Ar_Name'
         ,'products.P_En_Name'
         ,'items_groups.NameEn as GroupName'
         ,'brands.NameEn as BrandName'
         ,'measuerments.NameEn as UnitName'
         ,'product_units.Rate'
         ,'product_units.Barcode'
         ,'product_units.Price'
         ,'product_units.Price_Two'
         ,'product_units.Price_Three'
         ,'product_units.Def'
         
        )
                  ->get();    
             
             
         }
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Type',
          'Arabic Name',
          'English Name',
          'Group',
          'Brand',
          'Unit',
          'Rate',
          'Barcode',
          'Price 1',
          'Price 2',
          'Price 3',
          'Def',
         
        
        ];
    }
    
    
    

}

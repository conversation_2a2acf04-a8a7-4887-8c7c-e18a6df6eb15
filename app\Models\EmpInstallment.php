<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpInstallment extends Model
{
    use HasFactory;
    protected $table = 'emp_installments';
      protected $fillable = [
        'Amount',
        'Years_Number',
        'First_Date',
        'Install',
        'Install_Numbers',
        'Status',
        'Emp',
        'Loan',
    
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
           public function Loan()
    {
        return $this->belongsTo(Loan::class,'Loan');
    }
    
    
    
    
                        public function EmpInstallmentDetails()
    {
        return $this->hasOne(EmpInstallmentDetails::class);
    }
}

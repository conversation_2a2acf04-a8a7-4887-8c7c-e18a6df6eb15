<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;
       protected $table = 'tickets';
      protected $fillable = [

                'Code',
                'Date',
                'Payment_Method',
                'Sender_Name',
                'Sender_Address',
                'Sender_Phone',
                'Addressees_Name',
                'Addressees_Address',
                'Addressees_Phone',
                'Notes',
                'Sub_Total',
                'Discount',
                'Total',
                'Store',
                'Safe',
                'Recived',
                'Selected',
                'Coin',
                'Draw',
                'Product_Numbers',
                'Total_Qty',
                'Access_Area',
 
          
    ];
    
    
        public function Sender_Name()
    {
        return $this->belongsTo(AcccountingManual::class,'Sender_Name');
    }
    
            public function Addressees_Name()
    {
        return $this->belongsTo(AcccountingManual::class,'Addressees_Name');
    }
    
            public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
                public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
    
    
            public function Access_Area()
    {
        return $this->belongsTo(Governrate::class,'Access_Area');
    }
    
    
}

<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllReceiptVoucher implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
        
        
        
          if(app()->getLocale() == 'ar' ){ 
   
           $prods = DB::table('recipt_vouchers')
               
               
            ->leftJoin('recipt_voucher_details', function ($join) {
    
            $join->on('recipt_vouchers.id', '=', 'recipt_voucher_details.RV_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('recipt_vouchers.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('recipt_voucher_details.Account', '=', 'acccounting_manuals.id');
        })     
               
            ->leftJoin('safes_banks', function ($join) {
    
            $join->on('recipt_vouchers.Safe', '=', 'safes_banks.Account');
        })      
        
          


        
->select('recipt_vouchers.Date'
         ,'recipt_vouchers.Code'
         ,'safes_banks.Name as SafeName'
         ,'coins.Arabic_Name as Coin'
         ,'recipt_voucher_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.Name as AccountName'
         ,'recipt_voucher_details.Statement'
        
         
        )
                  ->get();
              
          }else{
              
              
              
           $prods = DB::table('recipt_vouchers')
               
               
            ->leftJoin('recipt_voucher_details', function ($join) {
    
            $join->on('recipt_vouchers.id', '=', 'recipt_voucher_details.RV_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('recipt_vouchers.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('recipt_voucher_details.Account', '=', 'acccounting_manuals.id');
        })     
               
            ->leftJoin('safes_banks', function ($join) {
    
            $join->on('recipt_vouchers.Safe', '=', 'safes_banks.Account');
        })      
        
          


        
->select('recipt_vouchers.Date'
         ,'recipt_vouchers.Code'
         ,'safes_banks.NameEn as SafeName'
         ,'coins.English_Name as Coin'
         ,'recipt_voucher_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'recipt_voucher_details.Statement'
        
         
        )
                  ->get();
              
              
              
          }
 
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Safe',
          'Coin',
          'Creditor',
          'Account Code',
          'Account Name',
          'Statement',
        ];
    }
    
    
    

}

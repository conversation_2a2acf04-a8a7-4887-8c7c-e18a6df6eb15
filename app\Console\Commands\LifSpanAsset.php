<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\Assets;
use DB ;    
use DateTime;    

class LifSpanAsset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Life:Asset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lif Span Asset';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
  
                $itemsX=Assets::where('Asset_Net','>',1)->where('Asset_Type','مستهلك')->get();      
                  $x1=date('Y')."-01";  
                  $x2=date('Y')."-02";  
                  $x3=date('Y')."-03";  
                  $x4=date('Y')."-04";  
                  $x5=date('Y')."-05";  
                  $x6=date('Y')."-06";  
                  $x7=date('Y')."-07";  
                  $x8=date('Y')."-08";  
                  $x9=date('Y')."-09";  
                  $x10=date('Y')."-10";  
                  $x11=date('Y')."-11";  
                  $x12=date('Y')."-12";  
                 foreach($itemsX as $item){
                     
                if(!empty($item->Depreciation_Complex)){     
                       if(!empty($item->Ehlak)){  
                           
  $date = new DateTime($item->Purchases_Date);
$date->modify('-1 month');
$CD=$date->format('Y-m');   
        
                if($CD != date('Y-m')){           
                           
                     if($item->Purchases_Date == $x1){ 
                     if($item->M1 != $x1){ 
               
                    if($item->Asset_Type == 'مستهلك'){

                        
                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                       
                      Assets::where('id',$item->id)->update(['M1'=>$x1]);   
                     }
                     }    
                     
                      if($item->Purchases_Date == $x2){ 
                           if($item->M2 != $x2){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                     Assets::where('id',$item->id)->update(['M2'=>$x2]);         
                          
                     }    
                     }    
                     
                      if($item->Purchases_Date == $x3){ 
                           if($item->M3 != $x3){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                           Assets::where('id',$item->id)->update(['M3'=>$x3]);   
                     }    
                     }    
                     
                      if($item->Purchases_Date == $x4){ 
                           if($item->M4 != $x4){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
              
               if(is_numeric($item->Asset_Net)){
               $ff['Asset_Net']=$item->Asset_Net - $res ;   
               }else{
                 $ff['Asset_Net']=0 - $res ;   
               }
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                           Assets::where('id',$item->id)->update(['M4'=>$x4]);   
                     }    
                     }    
                     
                      if($item->Purchases_Date == $x5){ 
                           if($item->M5 != $x5){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }  
                           Assets::where('id',$item->id)->update(['M5'=>$x5]);   
                     }    
                     }    
                     
                      if($item->Purchases_Date == $x6){ 
                           if($item->M6 != $x6){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }  
                           Assets::where('id',$item->id)->update(['M6'=>$x6]);   
                     }    
                     }    
                     
                      if($item->Purchases_Date == $x7){ 
                           if($item->M7 != $x7){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                           Assets::where('id',$item->id)->update(['M7'=>$x7]);   
                     }    
                     }    
                     
                     
                      if($item->Purchases_Date == $x8){ 
                           if($item->M8 != $x8){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' =>'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }  
                           Assets::where('id',$item->id)->update(['M8'=>$x8]);   
                     }    
                     }    
                     
                     
                      if($item->Purchases_Date == $x9){ 
                           if($item->M9 != $x9){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }   
                           Assets::where('id',$item->id)->update(['M9'=>$x9]);   
                     }    
                     }    
                     
                     
                      if($item->Purchases_Date == $x10){ 
                           if($item->M10 != $x10){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' =>'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    } 
                           Assets::where('id',$item->id)->update(['M10'=>$x10]);   
                     }    
                     }    
                     
                     
                      if($item->Purchases_Date == $x11){ 
                           if($item->M11 != $x11){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }  
                           Assets::where('id',$item->id)->update(['M11'=>$x11]);   
                     } 
                     } 
                     
                      if($item->Purchases_Date == $x12){ 
                           if($item->M12 != $x12){ 
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $CodeT=$rest->Code + 1 ; 
           }else{
              $CodeT=1; 
               
           }   
         
         
     $IDT = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,
  
        )
    );
         
   
        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

       
        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                         
               $ff['Asset_Net']=$item->Asset_Net - $res ;              
                    Assets::where('id',$item->id)->update($ff);                 
                            
                        
                        
                        
                        
                    }    
                           Assets::where('id',$item->id)->update(['M12'=>$x12]);   
                     }    
                     }    
                }
                }
                }
                     
                 }

    }
}

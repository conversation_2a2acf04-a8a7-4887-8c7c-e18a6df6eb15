<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorsStatementsColumnSechdule extends Model
{
    use HasFactory;
            protected $table = 'vendors_statements_column_sechdules';
      protected $fillable = [

        'Account_Code',               
        'Account_Name',               
        'Debiator_Before',               
        'Creditor_Before',               
        'Total_Debitor',               
        'Total_Creditor',               
        'Debitor_Balance',                     
        'Creditor_Balance',               
       

    ];

}

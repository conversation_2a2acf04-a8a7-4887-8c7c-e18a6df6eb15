<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReciptMaintaincesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recipt_maintainces', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Date');
            $table->text('Note')->nullable();
            $table->string('Serial_Num')->nullable();
            $table->string('Total')->nullable();
            $table->string('Product_Numbers')->nullable();
            $table->string('Total_Qty')->nullable();
            $table->string('Total_Discount')->nullable();
            $table->string('Total_Cost')->nullable();
            $table->string('Total_Bf_Taxes')->nullable();
            $table->string('Total_Taxes')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Company')->nullable();
            $table->string('Device_Type')->nullable();
            $table->string('Device_Case')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Account')->nullable();
            $table->string('User');
            $table->integer('Status')->default(0);
            $table->string('Pattern_Image')->nullable();
            $table->string('Time');
            $table->string('Payment_Method')->nullable();
            $table->string('Password')->nullable();
            $table->string('Pay')->nullable();
            $table->text('Eng_Note')->nullable();
            $table->string('Reason')->nullable();
            $table->string('Report_Client')->nullable();
            $table->string('Work')->nullable();
            $table->string('Store')->nullable();
            $table->string('Eng')->nullable();
            $table->string('Recipient')->nullable();
            $table->string('Branch')->nullable();
            $table->string('RefuseReason')->nullable();
            $table->text('NoteRecived')->nullable();
            $table->string('Returned')->nullable();
            $table->string('CustomerGroup')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recipt_maintainces');
    }
}

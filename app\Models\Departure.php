<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Departure extends Model
{
    use HasFactory;
           protected $table = 'departures';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Note',
        'User',
        'Attend',

       
    ];
    
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
         public function Attend()
    {
        return $this->belongsTo(Attendance::class,'Attend');
    }
    
                         public function DepartureEmp()
    {
        return $this->hasOne(DepartureEmp::class);
    }
    
    
    
}

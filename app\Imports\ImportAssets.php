<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ImportAssets implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('assets')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('assets')->insert([

         
            'Code'	   =>$value[1]
            ,'Name'  =>$value[2]
            ,'Asset_Type'  =>$value[3]
            ,'Depreciation_Method'  =>$value[4]
            ,'Purchases_Date'  =>$value[5]
            ,'Operation_Date'  =>$value[6]
            ,'Cost'  =>$value[7]
            ,'Previous_Depreciation'  =>$value[8]
            ,'Asset_Net'  =>$value[9]
            ,'Annual_Depreciation_Ratio'  =>$value[10]
            ,'Annual_Depreciation'  =>$value[11]
            ,'Life_Span'  =>$value[12]
            ,'Image'  =>$value[13]
            ,'Note'  =>$value[14]
            ,'Depreciation_Expenses'  =>$value[15]
            ,'Depreciation_Complex'  =>$value[16]
            ,'Main_Account'  =>$value[17]
            ,'Account'  =>$value[18]
            ,'User'  =>$value[19]
            ,'created_at'  =>$value[20]
            ,'updated_at'  =>$value[21]            
            ,'Draw'  =>$value[22]
            ,'Coin'  =>$value[23]
            ,'Cost_Center'  =>$value[24]
            ,'Branch'  =>$value[25]
            ,'Sort_Asset'  =>$value[26]
            ,'Vendor'  =>$value[27]
            ,'Safe'  =>$value[28]
            ,'Ehlak'  =>$value[29]
            ,'Payment_Method'  =>$value[30]
           
                    
                    
                    
                    
            

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

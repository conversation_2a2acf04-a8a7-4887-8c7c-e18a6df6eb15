<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegCoursesStudents extends Model
{
    use HasFactory;
         protected $table = 'reg_courses_students';
      protected $fillable = [

                'Student',
                'Attend',
                'Date',
                'Reg',
               

    ];
    
    
        public function Student()
    {
        return $this->belongsTo(Students::class,'Student');
    } 
}

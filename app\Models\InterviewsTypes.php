<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InterviewsTypes extends Model
{
    use HasFactory;
                  protected $table = 'interviews_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
    
                       public function Interviews()
    {
        return $this->hasOne(Interviews::class);
    }
    
    
    
}

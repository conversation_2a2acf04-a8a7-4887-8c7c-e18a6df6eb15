<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpsProducationQuantity extends Model
{
    use HasFactory;
          protected $table = 'emps_producation_quantities';
      protected $fillable = [
        'FromQ',
        'ToQ',
        'ValueQ',
        'Emp',
    ];
    
    
       public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

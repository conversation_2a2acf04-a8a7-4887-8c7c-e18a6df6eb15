<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;

class ProStratPeriod implements ToCollection, WithChunkReading , WithBatchInserts
{

    public function collection(Collection $collection)
    {
        
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
            DB::table('products_start_periods')->insert([

            'P_Code'  =>$value[1]
            ,'Qty'  =>$value[2]
            ,'Price'  =>$value[3]
            ,'Total'  =>$value[4]
            ,'Old_Qty'  =>$value[5]
            ,'Exp_Date'  =>$value[6]
            ,'SP_ID'  =>$value[7]
            ,'Unit'  =>$value[8]
            ,'Product'  =>$value[9]
            ,'V1'  =>$value[10]
            ,'V2'  =>$value[11]
            ,'created_at'  =>$value[12]
            ,'updated_at'  =>$value[13]
            ,'Date'  =>$value[14]
            ,'User'  =>$value[15]
            ,'Store'  =>$value[16]
            ,'P_Ar_Name'  =>$value[17]
            ,'P_En_Name'  =>$value[18]
            ,'V_Name'  =>$value[19]
            ,'VV_Name'  =>$value[20]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstallCompaniesSalesBillsColumns extends Model
{
    use HasFactory;
           protected $table = 'install_companies_sales_bills_columns';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',               
        'Refrence_Number',               
        'Branch',               
        'Store',               
        'Payment_Method',               
        'Safe',               
        'Type',               
        'Shipping',               
        'Cost_Center',               
        'ShiftCode',               
        'Executor',               
        'User',               
        'Coin',               
        'Due_Date',               
        'Delegate',               
        'Note',               
        'Total_Return',               
        'Total_Price',               
        'Total_Discount',               
        'Total_Tax',               
        'Total_Net',               
        'Paid',               
        'Residual',    
        'Client',    
        'InstallCompany',    
        'ContractNumber',    
        'PayFees',    
        'ServiceFee',    
        'CompanyPrecent',    
        'Product_Code',               
        'Product_Name',               
        'Unit',               
        'Av_Qty',               
        'Qty',               
        'Price',               
        'Discount',               
        'Total_BF_Tax',               
        'Tax',               
        'Total',               
        'Group',               
        'Brand',               
        'Exp_Date',               
        'Product_Store',               


    ];

}

<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Config;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Test route to verify routing is working
Route::get('/test-route', function () {
    return response()->json(['status' => 'success', 'message' => 'Routes are working properly']);
});

// Authentication Routes moved to tenant routes
// Route::get('AdminLogin', 'AdminController@LoginPage');
// Route::post('Login', 'AdminController@Login');
// Route::get('Logout', 'AdminController@Logout');
// Route::get('forgotpassword','AdminController@forgotpasswordPage');
// Route::post('forgotpassword','AdminController@forgotpassword');
// Route::get('reset/password/{token}','AdminController@reset_password');
// Route::post('reset/password/{token}','AdminController@reset_password_final');

// Admin Dashboard Route
Config::set('auth.defines','admin');
Route::group(['middleware' =>'Admin:admin'], function () {
    Route::group(['middleware' =>'auth:admin'], function() {
        Route::get('OstAdmin', function () {
            return view('admin.home');
        });
    });
});

Route::get('/WebSliderPage', 'WebsiteController@WebSliderPage');
Route::post('/AddWebSlider', 'WebsiteController@AddWebSlider');
Route::post('/EditWebSlider/{id}', 'WebsiteController@EditWebSlider');
Route::get('/DeleteWebSlider/{id}', 'WebsiteController@DeleteWebSlider');
Route::get('/UnActiveSlider/{id}', 'WebsiteController@UnActiveSlider');
Route::get('/ActiveSlider/{id}', 'WebsiteController@ActiveSlider');
Route::get('/AboutPage', 'WebsiteController@AboutPage');
Route::post('/UpdateAbout/{id}', 'WebsiteController@UpdateAbout');
Route::get('/SocialMediaPage', 'WebsiteController@SocialMediaPage');
Route::post('/SocialMediaUpdate/{id}', 'WebsiteController@SocialMediaUpdate');
Route::get('/MsgRqstPage', 'WebsiteController@MsgRqstPage');
Route::get('/DeleteMsgRqst/{id}', 'WebsiteController@DeleteMsgRqst');
Route::get('/ContactUSPage', 'WebsiteController@ContactUSPage');
Route::post('/ContactUSUpdate/{id}', 'WebsiteController@ContactUSUpdate');
Route::get('/ArticlesPage', 'WebsiteController@ArticlesPage');
Route::post('/AddArticles', 'WebsiteController@AddArticles');
Route::post('/EditArticles/{id}', 'WebsiteController@EditArticles');
Route::get('/DeleteArticles/{id}', 'WebsiteController@DeleteArticles');
Route::get('/TermsPage', 'WebsiteController@TermsPage');
Route::post('/UpdateTerms/{id}', 'WebsiteController@UpdateTerms');
Route::get('/PolicesPage', 'WebsiteController@PolicesPage');
Route::post('/UpdatePolices/{id}', 'WebsiteController@UpdatePolices');
Route::get('/CouponCodePage', 'WebsiteController@CouponCodePage');
Route::post('/AddCouponCode', 'WebsiteController@AddCouponCode');
Route::post('/EditCouponCode/{id}', 'WebsiteController@EditCouponCode');
Route::get('/DeleteCouponCode/{id}', 'WebsiteController@DeleteCouponCode');
Route::get('/FAQPage', 'WebsiteController@FAQPage');
Route::post('/AddFAQ', 'WebsiteController@AddFAQ');
Route::post('/EditFAQ/{id}', 'WebsiteController@EditFAQ');
Route::get('/DeleteFAQ/{id}', 'WebsiteController@DeleteFAQ');
Route::get('/CountrisPage', 'WebsiteController@CountrisPage');
Route::post('/AddCountris', 'WebsiteController@AddCountris');
Route::post('/EditCountris/{id}', 'WebsiteController@EditCountris');
Route::get('/DeleteCountris/{id}', 'WebsiteController@DeleteCountris');
Route::get('/ProDetailsImg', 'WebsiteController@ProDetailsImg');
Route::post('/EditProDetailsImg/{id}', 'WebsiteController@EditProDetailsImg');
Route::get('/BefroeFooter', 'WebsiteController@BefroeFooter');
Route::post('/EditBefroeFooter/{id}', 'WebsiteController@EditBefroeFooter');
Route::get('/ShopOrders', 'WebsiteController@ShopOrders');
Route::post('/ChangeStatusShop', 'WebsiteController@ChangeStatusShop');
Route::get('/EComDesign', 'WebsiteController@EComDesign');
Route::get('/AddMainEComDesignFirst', 'WebsiteController@AddMainEComDesignFirst');
Route::post('/AddMainEComDesign', 'WebsiteController@AddMainEComDesign');
Route::get('/AddHomeEComDesignFirst', 'WebsiteController@AddHomeEComDesignFirst');
Route::post('/AddHomeEComDesign', 'WebsiteController@AddHomeEComDesign');
Route::get('/AddHomeProductEComDesignFirst', 'WebsiteController@AddHomeProductEComDesignFirst');

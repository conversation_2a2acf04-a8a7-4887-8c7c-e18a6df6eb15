<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AddTranslate extends Model
{
    use HasFactory;
     protected $table = 'add_translates';
      protected $fillable = [
        'Code',
        'Date',
        'Client',
        'Client_Type',
        'From_Lang',
        'To_Lang',
        'Company',
        'Num_Translted_Word',
          
        'ID_Name',
        'ID_Profession',
        'ID_Martial_Status',
        'Passport_Name',       
          'Passport_Profession',
        'Passport_Martial_Status',
          
        'Extracted',
        'Extracted_Birthplace',
        'Extracted_Issuer',
          
        'CommercialRegistration',
        'Commercial_Name',
        'Commercial_Type',
        'Commercial_Start_Date',
        'Commercial_Number',
        'Commercial_Capital',
        'Commercial_Issuer',
        'Commercial_Address',
     
   
    ];
    
               public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }    
               public function From_Lang()
    {
        return $this->belongsTo(Languages::class,'From_Lang');
    }    
               public function To_Lang()
    {
        return $this->belongsTo(Languages::class,'To_Lang');
    }    
               public function Company()
    {
        return $this->belongsTo(TranslationTourismCompanies::class,'Company');
    }
}

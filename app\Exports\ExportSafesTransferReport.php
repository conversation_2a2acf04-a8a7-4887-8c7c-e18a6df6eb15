<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\SafeTransfers;
use DB;
class ExportSafesTransferReport implements FromCollection ,WithHeadings 
{
 
    
     private $coin=[] ;

    public function __construct($coin=0) 
    {
        $this->coin = $coin;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->coin;

         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $coinn=$storex['coin'];
         $code=$storex['code'];
         $value=$storex['value'];
         $fromSafe=$storex['fromSafe'];
         $toSafe=$storex['toSafe'];
         $delegate=$storex['delegate'];
         $user=$storex['user'];
         $types=$storex['types'];
        
     
       if(app()->getLocale() == 'ar' ){ 
           $prods = DB::table('safe_transfers')->whereBetween('safe_transfers.Date',[$from,$to])
               
           ->when(!empty($code), function ($query) use ($code) {
        return $query->where('safe_transfers.Code', $code);
    })
          

          
         ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('safe_transfers.Branch', $branch);
    })
          
                  ->when(!empty($coinn), function ($query) use ($coinn) {
        return $query->where('safe_transfers.Coin', $coinn);
    })
          
                         ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('safe_transfers.Status', $types);
    })
          
    
          
                    ->when(!empty($value), function ($query) use ($value) {
        return $query->where('safe_transfers.Amount','>=', $value);
    })
          
     
          
     ->when(!empty($fromSafe), function ($query) use ($fromSafe) {
        return $query->whereIn('safe_transfers.From_Safe', $fromSafe);
    })    
          
                   ->when(!empty($toSafe), function ($query) use ($toSafe) {
        return $query->whereIn('safe_transfers.To_Safe', $toSafe);
    })  
          
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('safe_transfers.User', $user);
    })   
          

          
                          ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('safe_transfers.Delegate', $delegate);
    })          
              

             ->leftJoin('safes_banks', function ($join) {
    
            $join->on('safe_transfers.From_Safe', '=', 'safes_banks.Account');

        })
       
                  ->leftJoin('branches', function ($join) {
    
            $join->on('safe_transfers.Branch', '=', 'branches.id');

        })
               
                 ->leftJoin('admins', function ($join) {
    
            $join->on('safe_transfers.User', '=', 'admins.id');
        })
               
                 ->leftJoin('coins', function ($join) {
    
            $join->on('safe_transfers.Coin', '=', 'coins.id');
        })

              ->leftJoin('employesses', function ($join) {
    
            $join->on('safe_transfers.Delegate', '=', 'employesses.id');
        })
        
->select('safe_transfers.Date'
         ,'safe_transfers.Code'
         ,'safe_transfers.Time'
         ,'safe_transfers.OldAmount'
         ,'safe_transfers.Amount'
         ,'safes_banks.Name as FromSafe'
         ,'safe_transfers.NameEn as toSafe'
         ,'admins.name as User'
         ,'coins.Arabic_Name as COIN'
          ,'safe_transfers.Note'
         ,'employesses.Name as Emp'
         ,'branches.Arabic_Name as Branch'
         ,'safe_transfers.Status'
        )


            ->distinct(['safe_transfers.Code'])   
                  ->get();
       }else{
           
              $prods = DB::table('safe_transfers')->whereBetween('safe_transfers.Date',[$from,$to])
               
           ->when(!empty($code), function ($query) use ($code) {
        return $query->where('safe_transfers.Code', $code);
    })
          

          
         ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('safe_transfers.Branch', $branch);
    })
          
                  ->when(!empty($coinn), function ($query) use ($coinn) {
        return $query->where('safe_transfers.Coin', $coinn);
    })
          
                         ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('safe_transfers.Status', $types);
    })
          
    
          
                    ->when(!empty($value), function ($query) use ($value) {
        return $query->where('safe_transfers.Amount','>=', $value);
    })
          
     
          
     ->when(!empty($fromSafe), function ($query) use ($fromSafe) {
        return $query->whereIn('safe_transfers.From_Safe', $fromSafe);
    })    
          
                   ->when(!empty($toSafe), function ($query) use ($toSafe) {
        return $query->whereIn('safe_transfers.To_Safe', $toSafe);
    })  
          
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('safe_transfers.User', $user);
    })   
          

          
                          ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('safe_transfers.Delegate', $delegate);
    })          
              

             ->leftJoin('safes_banks', function ($join) {
    
            $join->on('safe_transfers.From_Safe', '=', 'safes_banks.Account');

        })
       
                  ->leftJoin('branches', function ($join) {
    
            $join->on('safe_transfers.Branch', '=', 'branches.id');

        })
               
                 ->leftJoin('admins', function ($join) {
    
            $join->on('safe_transfers.User', '=', 'admins.id');
        })
               
                 ->leftJoin('coins', function ($join) {
    
            $join->on('safe_transfers.Coin', '=', 'coins.id');
        })

              ->leftJoin('employesses', function ($join) {
    
            $join->on('safe_transfers.Delegate', '=', 'employesses.id');
        })
        
->select('safe_transfers.Date'
         ,'safe_transfers.Code'
         ,'safe_transfers.Time'
         ,'safe_transfers.OldAmount'
         ,'safe_transfers.Amount'
         ,'safes_banks.NameEn as FromSafe'
         ,'safe_transfers.Name as toSafe'
         ,'admins.nameEn as User'
         ,'coins.English_Name as COIN'
          ,'safe_transfers.Note'
         ,'employesses.NameEn as Emp'
         ,'branches.English_Name as Branch'
         ,'safe_transfers.Status'
        )


            ->distinct(['safe_transfers.Code'])   
                  ->get();  
           
           
           
       }
    
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Time',
          'OldAmount',
          'Amount',
          'From_Safe',
          'To_Safe',
          'User',
          'Coin',
          'Note',
          'Delegate',
          'Branch',
          'Type'
        ];
    }
    
    
    

}

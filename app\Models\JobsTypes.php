<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobsTypes extends Model
{
    use HasFactory;
      protected $table = 'jobs_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
                              public function Employess()
    {
        return $this->hasOne(Employess::class);
    }
    
}

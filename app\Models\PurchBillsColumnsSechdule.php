<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchBillsColumnsSechdule extends Model
{
    use HasFactory;
            protected $table = 'purch_bills_columns_sechdules';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',               
        'Refrence_Number',               
        'Branch',               
        'Store',               
        'Payment_Method',               
        'Safe',               
        'Type',               
        'Shipping',               
        'Cost_Center',                                     
        'User',               
        'Coin',               
        'Due_Date',               
        'Delegate',               
        'Note',               
        'Total_Return',               
        'Total_Price',               
        'Total_Discount',               
        'Total_Tax',               
        'Total_Net',               
        'Paid',               
        'Residual',    
        'Vendor',    
          
        'Product_Code',               
        'Product_Name',               
        'Unit',                            
        'Qty',               
        'Price',               
        'Discount',               
        'Total_BF_Tax',               
        'Tax',               
        'Total',               
        'Group',               
        'Brand',               
        'Exp_Date',               
        'Product_Store',               
        'Rate',               
        'Name_Width',               


    ];

}

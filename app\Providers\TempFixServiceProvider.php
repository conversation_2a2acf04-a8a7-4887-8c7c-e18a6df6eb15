<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class TempFixServiceProvider extends ServiceProvider
{
    public function register()
    {
        if (!Schema::hasTable('company_data')) {
            Schema::create('company_data', function ($table) {
                $table->increments('id');
            });
        }
    }
}

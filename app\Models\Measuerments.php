<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Measuerments extends Model
{
    use HasFactory;
             protected $table = 'measuerments';
      protected $fillable = [
        'Name',
        'NameEn',
        'Note',
        'Code',
   
    ];
    
    
    public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }
    
    
         public function ProductUnits()
    {
        return $this->hasOne(ProductUnits::class);
    }
    
    
          public function AssemblyProducts()
    {
        return $this->hasOne(AssemblyProducts::class);
    }
    
          public function ProductsStartPeriods()
    {
        return $this->hasOne(ProductsStartPeriods::class);
    }
    
                public function ProductsQty()
    {
        return $this->hasOne(ProductsQty::class);
    }
    
           public function ProductInventory()
    {
        return $this->hasOne(ProductInventory::class);
    }
    
                public function ProductSettlement()
    {
        return $this->hasOne(ProductSettlement::class);
    }
    
                 public function ProductsStoresTransfers()
    {
        return $this->hasOne(ProductsStoresTransfers::class);
    }
    
    
         public function BarcodeProducts()
    {
        return $this->hasOne(BarcodeProducts::class);
    }
    
              public function ProductsPurchasesOrder()
    {
        return $this->hasOne(ProductsPurchasesOrder::class);
    }
    
                  public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }
    
                          public function RecivedPurchProducts()
    {
        return $this->hasOne(RecivedPurchProducts::class);
    }
    
        public function ReturnPurchProducts()
    {
        return $this->hasOne(ReturnPurchProducts::class);
    }
    
              public function ProductsQuote()
    {
        return $this->hasOne(ProductsQuote::class);
    }
    
           public function ProductSales()
    {
        return $this->hasOne(ProductSales::class);
    }
    
         public function ProductSalesOrder()
    {
        return $this->hasOne(ProductSalesOrder::class);
    }
    
           public function RecivedSalesProducts()
    {
        return $this->hasOne(RecivedSalesProducts::class);
    }
    
          public function ReturnSalesProducts()
    {
        return $this->hasOne(ReturnSalesProducts::class);
    }
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSalesSubscribes extends Model
{
    use HasFactory;
     protected $table = 'product_sales_subscribes';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Price',
        'Sub_Date',
        'Sub_Type',
        'Price',
        'Total',
        'Product',
        'V1',
        'V2',
        'Unit',
        'Subscribe',
    ];


            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }

    
            public function Subscribe()
    {
        return $this->belongsTo(SalesSubscribes::class,'Subscribe');
    }
}

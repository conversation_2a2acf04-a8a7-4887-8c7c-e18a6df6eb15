<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Settlement extends Model
{
    use HasFactory;
      protected $table = 'settlements';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Note',
        'Total_Dificit',
        'Total_Excess',
        'Total_Dificit_Price',
        'Total_Excess_Price',
        'Account_Excess',
        'Account_Dificit',
        'Store',
        'Coin',
        'User',
        'Inv_ID',
        'Cost_Center',
        'Time',
        'Branch',
   
    ];
    
           public function Account_Excess()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Excess');
    }
    
              public function Account_Dificit()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Dificit');
    }
    
              public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
             public function Inv_ID()
    {
        return $this->belongsTo(Inventory::class,'Inv_ID');
    }
    
               public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
    
        public function ProductSettlement()
    {
        return $this->hasOne(ProductSettlement::class);
    }
    
             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
}

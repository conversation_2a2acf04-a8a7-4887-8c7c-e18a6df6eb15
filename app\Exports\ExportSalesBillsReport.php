<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\ReturnSales;
use DB;
class ExportSalesBillsReport implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result


        $storex=$this->store;
        $storee =  $storex['store'];
        $from =  $storex['from'];
        $to =  $storex['to'];
        $branch =  $storex['branch'];
        $clients_Group =  $storex['clients_Group'];
        $cost_Center =  $storex['cost_Center'];
        $coin =  $storex['coin'];
        $code =  $storex['code'];
        $shift_Code =  $storex['shift_Code'];
        $refrence_Number =  $storex['refrence_Number'];
        $safe =  $storex['safe'];
        $client =  $storex['client'];
        $payment_Method =  $storex['payment_Method'];
        $delegate =  $storex['delegate'];
        $executor =  $storex['executor'];
        $user =  $storex['user'];
        $shipping_Company =  $storex['shipping_Company'];
        $types =  $storex['types'];
        $typeX =  $storex['typeX'];

        
   if(app()->getLocale() == 'ar' ){ 
        
          $prods = DB::table('sales')->whereBetween('sales.Date',[$from,$to])
             
              ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('sales.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('sales.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('sales.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('sales.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('sales.Code', $code);
    }) 
          
          
              ->when(!empty($shift_Code), function ($query) use ($shift_Code) {
        return $query->where('sales.Shift_Code', $shift_Code);
    })          
          
    
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('sales.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('sales.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('sales.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('sales.Client', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('sales.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('sales.Delegate', $delegate);
    })     


           ->when(!empty($executor), function ($query) use ($executor) {
        return $query->whereIn('sales.Executor', $executor);
    })     
  

          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('sales.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('sales.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('sales.Status', $types);
    })  
 
             
            ->join('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('sales.Client', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('sales.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('sales.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('sales.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('sales.Date'
         ,'sales.Time'
         ,'sales.Code'
         ,'sales.Refernce_Number'
         ,'branches.Arabic_Name as Branch'
         ,'acccounting_manuals.Name as Client'
         ,'stores.Name as Store'
         ,'safes_banks.Name as Safe'
         ,'sales.Total_Price'
         ,'sales.Total_Discount'
         ,'sales.Total_Taxes'
         ,'sales.Pay'
         ,'sales.Later_Due'
              ,'coins.Arabic_Name as Coin'
              ,'employesses.Name as Delegate'
              ,'employesses.Name as Executor'
              ,'admins.name as User'
              ,'cost_centers.Arabic_Name as CostCenter'
           ,'sales.Note'
        )
                  ->get();
        
       
   }else{
       
       
       
             $prods = DB::table('sales')->whereBetween('sales.Date',[$from,$to])
             
              ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('sales.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('sales.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('sales.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('sales.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('sales.Code', $code);
    }) 
          
          
              ->when(!empty($shift_Code), function ($query) use ($shift_Code) {
        return $query->where('sales.Shift_Code', $shift_Code);
    })          
          
    
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('sales.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('sales.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('sales.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('sales.Client', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('sales.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('sales.Delegate', $delegate);
    })     


           ->when(!empty($executor), function ($query) use ($executor) {
        return $query->whereIn('sales.Executor', $executor);
    })     
  

          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('sales.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('sales.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('sales.Status', $types);
    })  
 
             
            ->join('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('sales.Client', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('sales.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('sales.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('sales.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('sales.Date'
         ,'sales.Time'
         ,'sales.Code'
         ,'sales.Refernce_Number'
         ,'branches.English_Name as Branch'
         ,'acccounting_manuals.NameEn as Client'
         ,'stores.NameEn as Store'
         ,'safes_banks.NameEn as Safe'
         ,'sales.Total_Price'
         ,'sales.Total_Discount'
         ,'sales.Total_Taxes'
         ,'sales.Pay'
         ,'sales.Later_Due'
              ,'coins.English_Name as Coin'
              ,'employesses.NameEn as Delegate'
              ,'employesses.NameEn as Executor'
              ,'admins.nameEn as User'
              ,'cost_centers.English_Name as CostCenter'
           ,'sales.Note'
        )
                  ->get();
        
       
       
   }
     
        
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Time',
          'Code',
          'Refernce_Number',
          'Branch',
          'Client',
          'Store',
          'Safe',
          'Total_Price',
          'Total_Discount',
          'Total_Tax',
          'Pay',
          'Due_Date',
          'Coin',
          'Shift_Code',
          'Delegate',
          'Executor',
          'User',
          'Cost_Center',
          'Note',

        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VAPrice extends Model
{
    use HasFactory;
      protected $table = 'v_a_prices';
      protected $fillable = [
        'Price',
        'Offer_Price',
        'MainV',
        'SubV',
        'Product',
        'Added',
        
    ];
    
       
                public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }

           
                public function MainV()
    {
        return $this->belongsTo(Virables::class,'MainV');
    }
    
                   public function SubV()
    {
        return $this->belongsTo(SubVirables::class,'SubV');
    }
}

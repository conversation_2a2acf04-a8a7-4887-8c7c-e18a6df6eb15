<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code')->nullable();
            $table->string('Date')->nullable();
            $table->string('Name')->nullable();
            $table->string('Price_Level')->nullable();
            $table->string('Phone')->nullable();
            $table->string('email')->nullable();
            $table->string('password')->nullable();
            $table->string('ID_Number')->nullable();
            $table->string('Address')->nullable();
            $table->string('Qualifications')->nullable();
            $table->string('Birthdate')->nullable();
            $table->string('Social_Status')->nullable();
            $table->string('Passport_Number')->nullable();
            $table->string('Company_Name')->nullable();
            $table->string('Commercial_Registration_No')->nullable();
            $table->string('Tax_Card_No')->nullable();
            $table->string('Bank_Account')->nullable();
            $table->string('Image')->nullable();
            $table->string('Next_Time')->nullable();
            $table->string('Executions_Status')->nullable();
            $table->string('Governrate')->nullable();
            $table->string('City')->nullable();
            $table->string('Responsible')->nullable();
            $table->string('Activity')->nullable();
            $table->string('Campagin')->nullable();
            $table->string('ClientStatus')->nullable();
            $table->integer('Account')->nullable();
            $table->integer('User')->nullable();
            $table->timestamps();
            $table->string('Platform')->nullable();
            $table->string('Contract_Start')->nullable();
            $table->string('Contract_End')->nullable();
            $table->string('Group')->nullable();
            $table->string('code')->nullable();
            $table->string('country')->nullable();
            $table->string('Tax_Registration_Number')->nullable();
            $table->string('Tax_activity_code')->nullable();
            $table->string('work_nature')->nullable();
            $table->string('Place')->nullable();
            $table->string('Nationality')->nullable();
            $table->string('Buliding_Num')->nullable();
            $table->string('Street')->nullable();
            $table->string('Postal_Code')->nullable();
            $table->string('tax_magistrate')->nullable();
            $table->string('Floor')->nullable();
            $table->string('Room')->nullable();
            $table->string('Landmark')->nullable();
            $table->string('Add_Info')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customers');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsVira extends Model
{
    use HasFactory;
    
      protected $table = 'products_viras';
      protected $fillable = [

                    'Cost',
                    'Product',
                    'V1',
                    'V2',
      
          
    ];
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
         public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
         public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }

}

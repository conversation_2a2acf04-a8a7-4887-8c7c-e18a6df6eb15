<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JournalizingDetails extends Model
{
    use HasFactory;
       protected $table = 'journalizing_details';
      protected $fillable = [
        'Debitor',
        'Creditor',
        'Account',
        'Statement',
        'Joun_ID',
       
    ];
    
        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
           public function Joun_ID()
    {
        return $this->belongsTo(Journalizing::class,'Joun_ID');
    }
    
    
}

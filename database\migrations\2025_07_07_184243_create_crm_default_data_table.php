<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCrmDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crm_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Price_Level')->nullable();
            $table->integer('Governrate')->nullable();
            $table->integer('City')->nullable();
            $table->integer('Responsible')->nullable();
            $table->integer('Activity')->nullable();
            $table->integer('Campagin')->nullable();
            $table->integer('ClientStatus')->nullable();
            $table->integer('Platforms')->nullable();
            $table->timestamps();
            $table->string('Client_Delegate')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crm_default_data');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesOrder extends Model
{
    use HasFactory;
     protected $table = 'sales_orders';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'presenter',
        'annual_interest',
        'monthly_installment',
        'Years_Number',
        'total',
        'Quote',
        'installment_Num',
        'Date_First_installment',
        'ToSales',
        'Hold',
        'Later_Due',
        'Sale_Date',
        'CuponCode',
        'Shipping',
        'Delivery_Status',
        'Order_Type',
        'Name',
        'Email',
        'Phone',
        'OtherPhone',
        'Address_Name',
        'Special_MarkAdd',
        'StreetAdd',
        'BulidingAdd',
        'FloorAdd',
        'FlatAdd',
        'Governrate',
        'City',
        'Place',
        'LocationAdd',
        'Address_DetailsAdd',
        'ProfitPrecent',
        'InstallCompany',
        'ContractNumber',
        'PayFees',
        'ServiceFee',
        'CompanyPrecent',
        'Time',
        'Delegate_Recived',
        'Delegate_Recived_Time',
        'To_Sales_Time',
        'Cancel_Order',
        'Cancel_Order_Time',
        'Hold_Qty',
          
  
             'TakeawayStatus',
       'TakeawayTime',
        'Witer',
        'KitchenEnd',
        'KitchenEndTime',
        'RecivedOrder',
        'RecivedOrderTime',
        'DeliveryTime',
        'ResturantOrderType',
        'Table',
        'Total_Wight_Bill',

    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }          public function Witer()
    {
        return $this->belongsTo(Employess::class,'Witer');
    }          public function Table()
    {
        return $this->belongsTo(ResturantTables::class,'Table');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
              public function ProductSalesOrder()
    {
        return $this->hasOne(ProductSalesOrder::class);
    }
    
                  public function Governrate()
    {
        return $this->belongsTo(Governrate::class,'Governrate');
    }
    
                   public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
                   public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }
    
                       public function InstallCompany()
    {
        return $this->belongsTo(InstallmentCompanies::class,'InstallCompany');
    }
    
    
        
        
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StoresDefaultData;
use App\Models\UsersMoves;
use App\Models\CostCenter;
use App\Models\Stores;
use App\Models\Coins;
use App\Models\ProductsQty;
use App\Models\Products;
use App\Models\Measuerments;
use App\Models\ProductsPurchases;
use App\Models\ProductSales;
use App\Models\ProductsStartPeriods;
use App\Models\ProductMoves;
use App\Models\Branches;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\AcccountingManual;
use App\Models\ManufacturingHalls;
use App\Models\ManufacturingModel;
use App\Models\IncomManufacturingModel;
use App\Models\OutcomManufacturingModel;
use App\Models\ExecutingReceiving;
use App\Models\ManuStoreCount;
use App\Models\ManufacturingOrder;
use App\Models\Employess;
use App\Models\ExaminationsTypes;
use App\Models\ProductsManufacturingOrder;
use App\Models\ProductsExecutingReceiving;
use App\Models\ManufacturingRequest;
use App\Models\ProductsManufacturingRequest;
use App\Models\Customers;
use App\Models\DefaultDataShowHide;
use App\Models\ProductsStoresTransfers;
use App\Models\ManufacturingExecution;
use App\Models\Quality;
use App\Models\QualityDetails;
use App\Models\ProductUnits;
use App\Models\StoresMoves;
use App\Models\ProductManufacturingExecution;
use App\Models\ManufacturingDefaultData;
use App\Models\CustomersGroup;
use App\Models\Event;
use App\Models\FifoQty;
use DB;
use Str; 
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
 
class ManufacturingController extends Controller
{
        
    function __construct()
{

$this->middleware('permission:صالات التصنيع', ['only' => ['ManufacturingHallsPage']]);
$this->middleware('permission:اضافه صاله تصنيع', ['only' => ['AddManufacturingHalls']]);
$this->middleware('permission:حذف صاله تصنيع', ['only' => ['DeleteManufacturingHalls']]);
$this->middleware('permission:تعديل صاله تصنيع', ['only' => ['EditManufacturingHalls']]);
$this->middleware('permission:نموذج التصنيع', ['only' => ['ManufacturingModelPage','AddManufacturingModel']]);
$this->middleware('permission:جدول نماذج التصنيع', ['only' => ['ManufacturingModelSechdule']]);
$this->middleware('permission:حذف نموذج تصنيع', ['only' => ['DeleteManufacturingModel']]);
$this->middleware('permission:تعديل نموذج تصنيع', ['only' => ['EditManufacturingModel','PostEditManufacturingModel']]);
$this->middleware('permission:التنفيذ و الاستلام', ['only' => ['ExecutingandReceiving','AddExecutingReceiving']]);
$this->middleware('permission:طلب تصنيع', ['only' => ['Manufacturing_RequestPage','AddManufacturingRequest']]);        
$this->middleware('permission:جدول طلبات التصنيع', ['only' => ['Manufacturing_Request_Sechdule']]);        
$this->middleware('permission:حذف طلب تصنيع', ['only' => ['DeleteManufacturingRequest']]);        
$this->middleware('permission:تعديل طلب تصنيع', ['only' => ['EditManufacturingRequest','PostEditManufacturingRequest']]);
$this->middleware('permission:تأكيد طلب تصنيع', ['only' => ['ApproveManufacturingRequest']]);        
$this->middleware('permission:تحويل الي آمر تصنيع', ['only' => ['ManufacturingRequestTransferToOrder','AddTransferManufacturingOrder']]);        
$this->middleware('permission:انهاء آمر تصنيع', ['only' => ['EndManufacturingRequest']]);        
$this->middleware('permission:آمر تصنيع', ['only' => ['ManufacturingOrderPage','AddManufacturingOrder']]);        
$this->middleware('permission:جدول اوامر التصنيع', ['only' => ['ManufacturingOrderSechdule']]);        
$this->middleware('permission:حذف آمر تصنيع', ['only' => ['DeleteManufacturingOrder']]);        
$this->middleware('permission:تعديل آمر تصنيع', ['only' => ['EditManufacturingOrderPage','PostEditManufacturingOrder']]);     
$this->middleware('permission:اذن صرف بضاعه تصنيع', ['only' => ['ExchangeGoodsManufacturingOrder']]);        
$this->middleware('permission:تنفيذ التصنيع', ['only' => ['ManufacturingExecutionPage','AddManufacturingExecution']]);        
$this->middleware('permission:جدول اذن صرف بضاعه تصنيع', ['only' => ['ExchangeManufacturingGoodsSechdule']]);        
$this->middleware('permission:تأكيد اذن صرف بضاعه تصنيع', ['only' => ['SureExchangeGoodsManufacturingOrder']]);        
$this->middleware('permission:جدول تنفيذ التصنيع', ['only' => ['ManufacturingExecutionSechdule']]);        
$this->middleware('permission:حذف تنفيذ التصنيع', ['only' => ['DeleteManufacturingExecution']]);        
$this->middleware('permission:الجوده', ['only' => ['ManufacturingExecutionQuality','AddQuality']]);        
$this->middleware('permission:انواع الفحوصات', ['only' => ['ExaminationsTypesPage']]);        
$this->middleware('permission:اضافه فحص', ['only' => ['AddExaminationsTypes']]);        
$this->middleware('permission:حذف فحص', ['only' => ['DeleteExaminationsTypes']]);        
$this->middleware('permission:تعديل فحص', ['only' => ['EditExaminationsTypes']]);        
$this->middleware('permission:جدول الجوده', ['only' => ['QualitySechdule']]);        
$this->middleware('permission:حذف الجوده', ['only' => ['DeleteQuality']]);        
$this->middleware('permission:اتمام الجوده', ['only' => ['QualityDone']]);        
        
}    
  
    
        //Mtwst Sa3r Sharaa 
       private function AverageCost($TotaNewCost,$newQty,$product,$code,$store,$date,$price){
      
    
                             $def=StoresDefaultData::orderBy('id','desc')->first();
           
           
           if($def->Cost_Price == 1){
               
                      
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
           
           
           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent + $TotaNewCost) /  ($lastOperation->Current + $newQty) ;
           }else{
           $AVERAGE = ($TotaNewCost) / $newQty ;     
           }
           
           
   }elseif($def->Cost_Price == 0){
               
     
              $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               
                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{
                       
                       
                       if(!empty($PROOStart)){
                           
                             $AVERAGE = $PROOStart->Price ; 
                       }else{
                          $AVERAGE = $rr->Price ;    
                       }
                       
                        
                   }
           
           
               
               
           }elseif($def->Cost_Price == 2){
               
               
                             $fifo =FifoQty::
                where('Store',$store)    
                ->where('Product',$product)    
                ->where('P_Code',$code)    
                ->where('Purchases_Date',$date)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
                ->where('Purchases_Date',$date)   
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
                ->where('Purchases_Date',$date)   
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
                ->where('Purchases_Date',$date)   
                ->first(); 

}

}

}
    
               
               if(!empty($fifo)){
                   
          $AVERAGE = $fifo->Cost_price ;               
               }else{
                   
                 $AVERAGE = $price ;         
                   
               }
               
    
               
               
           }
           
           
           
           return $AVERAGE ;
    }

        private function AverageCostGet($product,$code,$store){
      
           
                       $def=StoresDefaultData::orderBy('id','desc')->first();
           
           
           if($def->Cost_Price == 1){
               
               
                       $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
           
           if(!empty($lastOperation)){
               
               if($lastOperation->Current == 0){
                        $AVERAGE = ($lastOperation->CostCurrent) /  1 ;
               }else{
                       $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ; 
               }
               
      
           }else{
           $AVERAGE = $rr->Price ;     
           }
           
               
               
           }elseif($def->Cost_Price == 0){
               
             


          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               
                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{
                       
                       
                       if(!empty($PROOStart)){
                           
                             $AVERAGE = $PROOStart->Price ; 
                       }else{
                          $AVERAGE = $rr->Price ;    
                       }
                       
                        
                   }
           
             
               
           }elseif($def->Cost_Price == 2){
               
                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

                   $fifo =FifoQty::orderBy('id','asc') 
                 ->where('Store',$store)    
              ->where('Product',$product)  
    ->where('P_Code',$code)   
                ->where('Qty','!=',0)    
                ->first();      
               
           
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')  
                  ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
                   ->where('Qty','!=',0)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
                  ->where('Qty','!=',0)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
      ->where('Qty','!=',0)    
                ->first(); 

}

}

}
    
               
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
           
             
               
           }
           
             
   
          return number_format((float)$AVERAGE, 2, '.', '') ;
    }

    private function TestCost($store,$product,$code,$id,$Purchases_Date){
        
        
                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

            $fifo =FifoQty::
                where('Store',$store)    
                ->where('Product',$product)    
                ->where('P_Code',$code)    
                ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
             ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

}

}

}
    
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
        return $AVERAGE ;
        
        
    }

     private function AverageCostTwo($TotaNewCost,$newQty,$product,$code,$store){
      

           
                             $def=StoresDefaultData::orderBy('id','desc')->first();
           
           
           if($def->Cost_Price == 1){
          
               
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
           
          $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;  
               }else{
            $AVERAGE=1;       
               }
           }
               
  
               
   }elseif($def->Cost_Price == 0){
               
     
                 
                  $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               
                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{
                       
                       
                       if(!empty($PROOStart)){
                           
                             $AVERAGE = $PROOStart->Price ; 
                       }else{
                          $AVERAGE = $rr->Price ;    
                       }
                       
                        
                   }
           
               
               
           }elseif($def->Cost_Price == 2){
               
                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

                  $fifo =FifoQty::orderBy('id','asc') 
                 ->where('Store',$store)    
              ->where('Product',$product)  
    ->where('P_Code',$code)   
                ->where('Qty','!=',0)    
                ->first();      
               
           
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')  
                  ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
                   ->where('Qty','!=',0)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
                  ->where('Qty','!=',0)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
      ->where('Qty','!=',0)    
                ->first(); 

}

}

}
    
               
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
           
             
               
           }


           return $AVERAGE ;
    } 
    
            
    //Fifo
      private function FifoStoreQty($store,$product,$code,$id,$Purchases_Date,$Qty,$Unit){
        
               $fifo =FifoQty::
                where('Store',$store)    
                ->where('Product',$product)    
                ->where('P_Code',$code)    
                ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
             ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

}

}

}
    
    

    if(!empty($fifo)){
        
        if($fifo->Qty >= $Qty){
            
            
                   $unit=ProductUnits::where('Unit',$Unit)->where('Product',$product)->first();  
                
           $qq= $unit->Rate * $Qty ;
                
           $newqty=$fifo->Qty -  $qq ; 
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
            

        
            
        }else{
            
            
        $resdiualQty=$Qty - $fifo->Qty ;
            
         $unit=ProductUnits::where('Unit',$Unit)->where('Product',$product)->first();  
                
           $qq= $unit->Rate * $fifo->Qty ;
                
           $newqty=$fifo->Qty -  $qq ; 
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
 
            
     $ResdiualCost=$this->FifoStoreQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$resdiualQty,$Unit);          
            
            
            
            
            
        }
        
    
    }
    
    
          
        return  $newqty  ;
        
        
    }

        
      private function MoreThanQty($store,$product,$code,$id,$Purchases_Date,$Qty){
        
        
                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

          $totCost=0;
            $fifo =FifoQty::
                where('Store',$store)    
                ->where('Product',$product)    
                ->where('P_Code',$code)    
                ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
             ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

}

}

}
    
               if(!empty($fifo)){
                   
         
              if($fifo->Qty == 0){
                       
               
                       
         $Quntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
      
                    if($Quntatity == 0){
                           
               
         $Quntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
     
                        
                    }else{
                        
                       
                        
                            if($Quntatity >= $Qty){
                    
                    $totCost += $fifo->Cost_Price * $Qty ;
                    
                }else{
                
                $res=$Qty - $Quntatity ;       
                    
                    
                $totCost += $fifo->Cost_Price * $Quntatity ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }          
                        
                        
                    }        
                            
                            
                       
                   }else{    
                            
                            
                if($fifo->Qty >= $Qty){
                    
                    $totCost += $fifo->Cost_Price * $Qty ;
                    
                }else{
                
                $res=$Qty - $fifo->Qty ;       
                    
                    
                $totCost += $fifo->Cost_Price * $fifo->Qty ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }            
                            
                       
                            
                   }       
                   
                       
               }
           
        return  $totCost  ;
        
        
    }


             //====== Manufacturing Halls ======= 
        public function ManufacturingHallsPage(){
        $items=ManufacturingHalls::all();
         return view('admin.Manufacturing.ManufacturingHalls',['items'=>$items]);
    }
    
     public function AddManufacturingHalls(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         ManufacturingHalls::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='صالات التصنيع';
           $dataUser['ScreenEn']='Manufacturing Halls';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditManufacturingHalls($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           ManufacturingHalls::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='صالات التصنيع';
           $dataUser['ScreenEn']='Manufacturing Halls';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteManufacturingHalls($id){
                      
        $del=ManufacturingHalls::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='صالات التصنيع';
           $dataUser['ScreenEn']='Manufacturing Halls';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
             //====== Manufacturing Model ======= 
    public function ManufacturingModelPage(){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $res=ManufacturingModel::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
            
                 
     if(auth()->guard()->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard()->user()->store)){
         $Stores=Stores::where('id',auth()->guard()->user()->store)->get();
            }else{
                
                 $Stores=Stores::all();
            }
         
     }
            
           
         return view('admin.Manufacturing.ManufacturingModel',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Stores'=>$Stores,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
    function IncomManufacturingProductsFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

    $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
                  $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code',$search)        
            ->orWhere('PP_Code',$search)        
            ->orWhere('PPP_Code',$search)        
            ->orWhere('PPPP_Code',$search)        
       
          ->get(); 
            
                  $data =ProductUnits:: 
           where('P_Ar_Name',$search)                  
            ->orWhere('P_En_Name',$search)      
            ->orWhere('Barcode', $search)
          ->get(); 
            
 }else{
            
              $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code','ILIKE', "%{$search}%")        
            ->orWhere('PP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
          ->get();   
            
                  $data =ProductUnits::   
          where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")         
            ->orWhere('Barcode','ILIKE', "%{$search}%")                    
          ->get(); 
            
  } 
      
       
        
     }

         $total_row = $Prods->count();
         $total_row1 = $data->count();
            $total_row2=  $total_row + $total_row1;    
      if($total_row2 > 0) 
      { 
   
          
         foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Service'){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 
              
                
             $plow=ProductUnits::where('Product',$rows->Product)->where('Rate',1)->first();      
                
         $purchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total_Bf_Tax');
     $countPurchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

    $purchsStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total');
      $countStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

               $storesTransfer=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('SmallTrans_Qty');        
                
          
                $OUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;        
                
 
        if(!empty($x)){
        
         if(!empty($purchs) or !empty($purchsStart) or !empty($storesTransfer) or !empty($OUTCOME)){
           
               
   if($CollectCount != 0){
     $ty= ($Collect /  $CollectCount) * $rr->Rate ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '');
                       
                   }else{
                       
                       $pr=$Collect * $rr->Rate;       
                   }         
             
         }else{
          $pr=$x->Price;   
             
         }        
            
            
        }else{
         $pr=$rr->Price;    
        }
      
        $st=Stores::find($store);
                
                
          if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   


    $pr=$this->AverageCostGet($rows->Product,$plow->Barcode,$store);         
        
                
                
                
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">  '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
 
        </td>
        
              



        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                       if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  

          if(count($Prods) == 0){
           foreach($data as $rows){  

           $Stores=Stores::all();  
    if($rows->P_Type == 'Service' or $rows->P_Type == 'Completed' or $rows->P_Type == 'Raw' or $rows->P_Type == 'Industrial' ){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
           
         $pr=$rr->Price;    
      
        $st=Stores::find($store);
                
                        if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   

      
     
                  $pr=$this->AverageCostGet($rows->Product,$rr->Barcode,$store);   
                
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">    '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                   if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>
        

        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                         if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        
        }  
          }

      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     function OutcomManufacturingProductsFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

        
        
                                       $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
                        $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code',$search)        
            ->orWhere('PP_Code',$search)        
            ->orWhere('PPP_Code',$search)        
            ->orWhere('PPPP_Code',$search)        
            ->where('Store',$store)        
          ->take(100)        
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode',$search)
        ->take(100)         
          ->get();  
            
            
 }else{
            
                $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code','ILIKE', "%{$search}%")        
            ->orWhere('PP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
            ->where('Store',$store)        
          ->take(100)        
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode','ILIKE', "%{$search}%")
        ->take(100)         
          ->get();         
            
            
  } 
        
        

        
              $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get(); 
            
       
        
        
             
     }

         $total_row = $Prods->count();
         $total_row2 = $data->count();
         $total_row3 = $datas->count();
         $total_row4 = $total_row2 + $total_row3;
      if($total_row4 > 0) 
      { 
   
                     foreach($datas as $rows){  
         
             
           $Stores=Stores::all();  
        
          if($rows->P_Type == 'Industrial' or $rows->P_Type == 'Completed'){       
            if($rows->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->id)->first(); 

        $st=Stores::find($store);
                
                
            
     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                    
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
             
                       
                   }   
 
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        
                <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value="">    '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   } 
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
          }
        
          
        }  
          
          
           foreach($data as $rows){  
    
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
                
                
                     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                    
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
             
                       
                   }   

                
                
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        
               <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
               
           if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   } 
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
 
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
   
        }  
          
   
      }elseif($total_row > 0){
          

                foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial' or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
                
                
                                   if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                    
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
             
                       
                   }   

        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
                
           if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   } 
                
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  


          
      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
      
      public function AddManufacturingModel(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Name'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

          
                 if(!empty(request('NameEn'))){
         $NameEn=request('NameEn');
          }else{
               $NameEn=request('Name'); 
              
          }


          
          
         
           $ID = DB::table('manufacturing_models')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Name' => request('Name'),
            'NameEn' => $NameEn,
            'Time' => request('Time'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Discount' => request('Total_Discount'),
            'Total_BF_Taxes' => request('Total_BF_Taxes'),
            'Total_Taxes' => request('Total_Taxes'),
            'Total_Price' => request('Total_Price'),
            'Hall' => request('Hall'),
            'Coin' => request('Coin'),
            'Type' => 1,
            'Cost_Center' => request('Cost_Center'),
            'User' => auth()->guard('admin')->user()->id,

        )
    );  
        
    
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Cost']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total']=$Totaal[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Depreciation']=$DeprecPrecent[$i];
                $uu['Depreciation_Qty']=$Deprec[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Model']=$ID;
             

               IncomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                     $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first();    
                 $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Cost']=request('Total_Price');
                $uu['Model']=$ID;

               OutcomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='نموذج التصنيع';
           $dataUser['ScreenEn']='Manufacturing Model';
           $dataUser['Type']='اضاافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=$NameEn;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
          
          if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('ManuExecution/'.$ID); }  
            
        
    }
    
       public function ManufacturingModelSechdule(){
        $items=ManufacturingModel::paginate(100);
         return view('admin.Manufacturing.ManufacturingModelSechdule',['items'=>$items]);
    }
    
     public function PrintManufacturingModel($id){
        
    
                $item=ManufacturingModel::find($id);
            
          $Incoms=IncomManufacturingModel::where('Model',$item->id)->get();
          $Outcom=OutcomManufacturingModel::where('Model',$item->id)->orderBy('id','desc')->first();
           
         return view('admin.Manufacturing.PrintManufacturingModel',[
             'item'=>$item,
             'Incoms'=>$Incoms,
             'Outcom'=>$Outcom,
         ]);
    }
    
  
     public function DeleteManufacturingModel($id){
                      
        $del=ManufacturingModel::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='نموذج التصنيع';
           $dataUser['ScreenEn']='Manufacturing Model';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

      public function EditManufacturingModel($id){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $item=ManufacturingModel::find($id);
        
     if(auth()->guard()->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard()->user()->store)){
         $Stores=Stores::where('id',auth()->guard()->user()->store)->get();
            }else{
                
                 $Stores=Stores::all();
            }
         
     }
            
          $Incoms=IncomManufacturingModel::where('Model',$item->id)->get();
          $Outcoms=OutcomManufacturingModel::where('Model',$item->id)->get();
           
         return view('admin.Manufacturing.EditManufacturingModel',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'item'=>$item,
             'Incoms'=>$Incoms,
             'Outcoms'=>$Outcoms,
             'Stores'=>$Stores,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
        
      public function PostEditManufacturingModel($id){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

   
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Name'] = request('Name');
            $data['NameEn'] = request('NameEn');
            $data['Time'] = request('Time');
            $data['Draw'] = request('Draw');
            $data['Note'] = request('Note');
            $data['Type'] = 1;
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Discount'] = request('Total_Discount');
            $data['Total_BF_Taxes'] = request('Total_BF_Taxes');
            $data['Total_Taxes'] = request('Total_Taxes');
            $data['Total_Price'] = request('Total_Price');
            $data['Hall'] = request('Hall');
            $data['Coin'] = request('Coin');
            $data['Cost_Center'] = request('Cost_Center');
            $data['User'] = auth()->guard('admin')->user()->id;

          ManufacturingModel::where('id',$id)->update($data);
    
          if(!empty(request('Unit'))){
            
              IncomManufacturingModel::where('Model',$id)->delete();
              
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Cost']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total']=$Totaal[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Depreciation']=$DeprecPrecent[$i];
                $uu['Depreciation_Qty']=$Deprec[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Model']=$id;
             

               IncomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            OutcomManufacturingModel::where('Model',$id)->delete();
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                  $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first(); 
                       $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                  $uu['Cost']=request('Total_Price');
                $uu['Model']=$id;

               OutcomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='نموذج التصنيع';
           $dataUser['ScreenEn']='Manufacturing Model';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return redirect('ManufacturingModelSechdule');
            
        
    }
    
        
             //====== Manufacturing Model Precent ======= 
    public function ManufacturingModelPrecentPage(){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $res=ManufacturingModel::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
            
                 
     if(auth()->guard()->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard()->user()->store)){
         $Stores=Stores::where('id',auth()->guard()->user()->store)->get();
            }else{
                
                 $Stores=Stores::all();
            }
         
     }
            
           
         return view('admin.Manufacturing.ManufacturingModelPrecent',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Stores'=>$Stores,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
    function OutcomManufacturingProductsFilterPrecent(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

                                             $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
                        $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code',$search)        
            ->orWhere('PP_Code',$search)        
            ->orWhere('PPP_Code',$search)        
            ->orWhere('PPPP_Code',$search)        
            ->where('Store',$store)        
          ->take(100)        
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode',$search)
        ->take(100)         
          ->get();  
            
            
 }else{
            
                $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code','ILIKE', "%{$search}%")        
            ->orWhere('PP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
            ->where('Store',$store)        
          ->take(100)        
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode','ILIKE', "%{$search}%")
        ->take(100)         
          ->get();         
            
            
  } 
        
        

        
              $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get(); 
            
     
        
        
             
     }

         $total_row = $Prods->count();
         $total_row2 = $data->count();
         $total_row3 = $datas->count();
         $total_row4 = $total_row2 + $total_row3;
      if($total_row > 0) 
      { 
   
          
         foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial' or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
                
         

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 

                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    

                       
                   }   
   
                
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                 
       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }  
                
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  


      }elseif($total_row4 > 0){
          
           foreach($datas as $rows){  
         
             
           $Stores=Stores::all();  
        
          if($rows->P_Type == 'Industrial' or $rows->P_Type == 'Completed'){       
            if($rows->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->id)->first(); 

        $st=Stores::find($store);
                
           

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 

                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    

                       
                   }   
           
                
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        
                <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value="">    '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
                      
    if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }   
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
          }
        
          
        }  
          
          
           foreach($data as $rows){  
    
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
                
                
                 

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 

                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    

                       
                   }   
 
                
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        
               <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value="">   '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
                        
       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }  
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
 
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
   
        }  
          
      }else
      {
       $output = '
        <div class="col-md-3">  '.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
      
       function IncomManufacturingProductsFilterPrecent(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

                                      $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
                  $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code',$search)        
          ->take(100)        
          ->get(); 
            
                $data =ProductUnits:: 
           where('P_Ar_Name',$search)                  
            ->orWhere('P_En_Name',$search)      
            ->orWhere('Barcode', $search)
          ->get();     
            
 }else{
            
              $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('P_Code','ILIKE', "%{$search}%")        
          ->take(100)        
          ->get();     
                  $data =ProductUnits::   
          where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")         
            ->orWhere('Barcode','ILIKE', "%{$search}%")                    
          ->get();     
  } 
             
     }

         $total_row = $Prods->count();
         $total_row1 = $data->count();
         $total_row2= $total_row + $total_row1 ;
      if($total_row2  > 0) 
      { 
   
          
         foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Service'){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 
                         
         $plow=ProductUnits::where('Product',$rows->Product)->where('Rate',1)->first();      
                
         $purchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total_Bf_Tax');
     $countPurchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

    $purchsStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total');
      $countStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

               $storesTransfer=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('SmallTrans_Qty');        
                
          
                $OUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      

                
                     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;
                
 
        if(!empty($x)){
        
         if(!empty($purchs) or !empty($purchsStart) or !empty($storesTransfer) or !empty($OUTCOME)){
           
               
   if($CollectCount != 0){
     $ty= ($Collect /  $CollectCount) * $rr->Rate ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '');
                       
                   }else{
                       
                       $pr=$Collect  * $rr->Rate;       
                   }         
             
         }else{
          $pr=$x->Price;   
             
         }        
            
            
        }else{
         $pr=$rr->Price;    
        }
      
        $st=Stores::find($store);
                
                
         

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   
  
    $pr=$this->AverageCostGet($rows->Product,$plow->Barcode,$store); 
                
                
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">  '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                      
     if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
 

                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

      <td>
        <input type="number" id="Precent'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
        </td>

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" disabled > 
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>
        

        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                   if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  
          
             if(empty($Prods)){
          foreach($data as $rows){  

           $Stores=Stores::all();  
    if($rows->P_Type == 'Service' or $rows->P_Type == 'Completed' or $rows->P_Type == 'Raw' or $rows->P_Type == 'Industrial' ){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
           
         $pr=$rr->Price;    
      
        $st=Stores::find($store);
                
    

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   
  

            
                    $pr=$this->AverageCostGet($rows->Product,$rr->Barcode,$store); 
                
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);   
                if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

      <td>
        <input type="number" id="Precent'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
        </td>

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" disabled > 
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>
        
   
        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
               if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        
        }  
             }

      }else
      {
       $output = '
        <div class="col-md-3"> '.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     public function AddManufacturingModelPrecent(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
        'Name'=>'required',
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

         
                         if(!empty(request('NameEn'))){
         $NameEn=request('NameEn');
          }else{
               $NameEn=request('Name'); 
              
          }

         
           $ID = DB::table('manufacturing_models')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Name' => request('Name'),
            'NameEn' => $NameEn,
            'Time' => request('Time'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Discount' => request('Total_Discount'),
            'Total_BF_Taxes' => request('Total_BF_Taxes'),
            'Total_Taxes' => request('Total_Taxes'),
            'Total_Price' => request('Total_Price'),
            'Hall' => request('Hall'),
            'Coin' => request('Coin'),
            'Type' => 2,
            'Cost_Center' => request('Cost_Center'),
            'User' => auth()->guard('admin')->user()->id,

        )
    );  
        
    
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Precent=request('Precent');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Precent']=$Precent[$i];
                $uu['Cost']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total']=$Totaal[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Depreciation']=$DeprecPrecent[$i];
                $uu['Depreciation_Qty']=$Deprec[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Model']=$ID;
             

               IncomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                  $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first(); 
                       $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
 $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Model']=$ID;

               OutcomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='نموذج تصنيع بنسبه';
           $dataUser['ScreenEn']='Manufacturing Model Precent';
           $dataUser['Type']='اضاافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=$NameEn;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
              if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('ManuExecution/'.$ID); } 
            
        
    }
    
      public function EditManufacturingModelPrecent($id){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $item=ManufacturingModel::find($id);
        
     if(auth()->guard()->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard()->user()->store)){
         $Stores=Stores::where('id',auth()->guard()->user()->store)->get();
            }else{
                
                 $Stores=Stores::all();
            }
         
     }
            
          $Incoms=IncomManufacturingModel::where('Model',$item->id)->get();
          $Outcoms=OutcomManufacturingModel::where('Model',$item->id)->get();
           
         return view('admin.Manufacturing.EditManufacturingModelPrecent',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'item'=>$item,
             'Incoms'=>$Incoms,
             'Outcoms'=>$Outcoms,
             'Stores'=>$Stores,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
      public function PostEditManufacturingModelPrecent($id){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

   
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Name'] = request('Name');
            $data['NameEn'] = request('NameEn');
            $data['Time'] = request('Time');
            $data['Draw'] = request('Draw');
            $data['Note'] = request('Note');
            $data['Type'] = 2;
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Discount'] = request('Total_Discount');
            $data['Total_BF_Taxes'] = request('Total_BF_Taxes');
            $data['Total_Taxes'] = request('Total_Taxes');
            $data['Total_Price'] = request('Total_Price');
            $data['Hall'] = request('Hall');
            $data['Coin'] = request('Coin');
            $data['Cost_Center'] = request('Cost_Center');
            $data['User'] = auth()->guard('admin')->user()->id;

          ManufacturingModel::where('id',$id)->update($data);
    
          if(!empty(request('Unit'))){
            
              IncomManufacturingModel::where('Model',$id)->delete();
              
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Precent=request('Precent');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Precent']=$Precent[$i];
                $uu['Cost']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total']=$Totaal[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Depreciation']=$DeprecPrecent[$i];
                $uu['Depreciation_Qty']=$Deprec[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Model']=$id;
             

               IncomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            OutcomManufacturingModel::where('Model',$id)->delete();
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
             

            for($i=0 ; $i < count($UnitO) ; $i++){
  $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first(); 
               $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;        
 $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Model']=$id;

               OutcomManufacturingModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='نموذج تصنيع بنسبه';
           $dataUser['ScreenEn']='Manufacturing Model Precent';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return redirect('ManufacturingModelSechdule');
            
        
    }
    
      public function ManuExecutionPage($id){
       
         
                   $res=ExecutingReceiving::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
         $Model=ManufacturingModel::find($id);
         
         return view('admin.Manufacturing.ManuExecution',['Code'=>$Code,'Model'=>$Model]);
    }
 
    //==========  Executing and Receiving  ===============
    
     public function ExecutingandReceiving(){
        $items=ExecutingReceiving::paginate(100);
         
                   $res=ExecutingReceiving::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
         $Models=ManufacturingModel::all();
         $Branches=Branches::all();
             $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->get();
         return view('admin.Manufacturing.ExecutingandReceiving',['items'=>$items,'Code'=>$Code,'Models'=>$Models,'Branches'=>$Branches,'Vendors'=>$Vendors]);
    }
   
    function ModelExecutingFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
                                                  
    if($search != '')
    {

            $modal=ManufacturingModel::find($search);      
            $Def=ManufacturingDefaultData::orderBy('id','desc')->first();      
           
        $Out=OutcomManufacturingModel::where('Model',$search)->first();
        $Ins=IncomManufacturingModel::where('Model',$search)->get();
        $InsCount=IncomManufacturingModel::where('Model',$search)->count();
       $total_cost=0;
        
              if(app()->getLocale() == 'ar' ){ 
                      $OutName=$Out->P_Ar_Name; 

                   
                   }else{
                         $OutName=$Out->P_En_Name;  
  
                       
                   } 
 

     }

      if(!empty($modal)) 
      { 
  $i=1;
          
           $output .= '

             <input type="hidden"  name="Product_Code[]" value="'.$Out->Product_Code.'">
             <input type="hidden"  name="P_Ar_Name[]" value="'.$Out->P_Ar_Name.'">
             <input type="hidden"  name="P_En_Name[]" value="'.$Out->P_En_Name.'">
             <input type="hidden" step="any" class="form-control"  name="Qty[]" value="'.$Out->Qty.'">
             <input type="hidden"  name="Price[]" value="'.$modal->Total_Price.'">
             <input type="hidden"  name="Product[]" value="'.$Out->Product.'">
             <input type="hidden"  name="Store[]" value="'.$Out->Store.'">
             <input type="hidden"  name="Unit[]" value="'.$Out->Unit.'">
     
       
            ';
          
          
        foreach($Ins as $in){
            
            
              $Cost=$this->AverageCostGet($in->Product,$in->Product_Code,$in->Store);         
            $total_cost += $Cost * $in->Qty  ;
                   if($Def->Executing_Qty == 1){
            $RE='';
        }else{
            $RE='readonly'; 
            
        }
            
            if($in->Product()->first()->P_Type != 'Service'){
           $Qunta=ProductsQty::where('Product',$in->Product)
               ->where('P_Code',$in->Product_Code)
               ->where('Store',$in->Store)
               ->get()->sum('Qty');
                
                if($Qunta == 0){
                    
                      $Qunta=ProductsQty::where('Product',$in->Product)
               ->where('PP_Code',$in->Product_Code)
               ->where('Store',$in->Store)
               ->get()->sum('Qty');
                    
                    
                       if($Qunta == 0){
                           
                                    $Qunta=ProductsQty::where('Product',$in->Product)
               ->where('PPP_Code',$in->Product_Code)
               ->where('Store',$in->Store)
               ->get()->sum('Qty');   
                           
                       }
                    
                    
                                  if($Qunta == 0){
                           
                                    $Qunta=ProductsQty::where('Product',$in->Product)
               ->where('PPPP_Code',$in->Product_Code)
               ->where('Store',$in->Store)
               ->get()->sum('Qty');   
                           
                       }
                    
                }
                
                
            }else{
            $Qunta=1000;    
            }
            
            
                 if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$in->P_Ar_Name; 
                      $StoreNemo=$in->Store()->first()->Name; 
                      $UniiName=$in->Unit()->first()->Name; 
                   
                   }else{
                         $PrrroName=$in->P_En_Name;  
                       $StoreNemo=$in->Store()->first()->NameEn; 
                      $UniiName=$in->Unit()->first()->NameEn; 
                       
                   }   
               $output .= '
             <tr>
        <td>
        '.$in->Product_Code.'
             <input type="hidden"  name="Product_CodeI[]" value="'.$in->Product_Code.'">
        </td>
            <td>
        '.$PrrroName.' ('.$Cost.')
             <input type="hidden"  name="P_Ar_NameI[]" value="'.$in->P_Ar_Name.'">
             <input type="hidden"  name="P_En_NameI[]" value="'.$in->P_En_Name.'">
        </td>
        <td>'.$Qunta.'
                        <input type="hidden"  id="StoreQty'.$i.'" value="'.$Qunta.'"> 
        </td>
        <td id="HALK">
    <input type="number" step="any" id="DepreciationQty'.$i.'" class="form-control" name="QtyI[]"   value="'.$in->Depreciation_Qty.'" >
                <input type="hidden" id="OriginalDepreciationQty'.$i.'"   value="'.$in->Depreciation_Qty.'"> 
        </td>
        
            <td>
 <input type="number" step="any" id="Qtyy'.$i.'" class="form-control" name="QtyHide[]" onclick="IncomChange('.$i.')" onkeyup="IncomChange('.$i.')"  value="'.$in->Qty.'" '.$RE.'>
                <input type="hidden" id="OriginalQtyy'.$i.'"  value="'.$in->Qty.'">  
                <input type="hidden" class="MO" id="More'.$i.'"  value="0">  
        </td>
        
                  <td>
        '.$StoreNemo.'
             <input type="hidden"  name="PriceI[]" value="'.$Cost.'">
             <input type="hidden"  name="ProductI[]" value="'.$in->Product.'">
             <input type="hidden"  name="StoreI[]" value="'.$in->Store.'">
        </td>
        
                  <td>
        '.$UniiName.'
             <input type="hidden"  name="UnitI[]" value="'.$in->Unit.'">
        </td>
        
        <td id="ACTIONS">
        
        <label>'.trans('admin.DUWELTS').'</label>
   
<select class="select2 form-control" name="inStore[]">
<option value="0">'.trans('admin.No').'</option>
<option value="1">'.trans('admin.Yes').'</option>
</select>
</td>
          
        </tr>
        
       
            ';
            
      $i++;      
        }  
          


      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
       'qty'  => $Out->Qty,
       'tot'  => number_format((float)$total_cost, 2, '.', ''),
       'Outcome'  => $OutName,
       'count'  => $InsCount,
      );
      echo json_encode($data);
     }
    }
    
      public function AddExecutingReceiving(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Model'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      


         ]);

      $in=IncomManufacturingModel::where('Model',request('Model'))->orderBy('id','asc')->first();
$out=OutcomManufacturingModel::where('Model',request('Model'))->first();
    
 
           $ID = DB::table('executing_receivings')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Qty' => request('Qtyy'),
            'Total' => request('Totall')*request('Qtyy'),
            'Model' => request('Model'),
            'User' => auth()->guard('admin')->user()->id,
            'Time' =>date("h:i:s a", time()),
            'Branch' =>request('Branch'),
            'StoreIn' =>$in->Store,
            'StoreOut' =>$out->Store,
            'Sort' =>request('Sort'),
            'Vendor' =>request('Vendor'),
            'Cost_Workmentship' =>request('Cost_Workmentship'),
            'Total_Workmentship' =>request('Total_Workmentship'),

        )
    );  
          
          
          

$ins=IncomManufacturingModel::where('Model',request('Model'))->get();
$out=OutcomManufacturingModel::where('Model',request('Model'))->first();
    
          
                    $c= DB::select("SELECT last_value FROM executing_receivings_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
          
 $Tot=request('Totall')*request('Qtyy');
          if(!empty(request('Unit'))){
            
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Qty=request('Qty');
              $Price=request('Price');
              $Product=request('Product');
              $Store=request('Store');
              $Unit=request('Unit');

             
            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Price']=$Price[$i];
                $uu['Total']=$Price[$i];
                $uu['Store']=$Store[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['type']=1;
                $uu['Executing']=$ID;

               ProductsExecutingReceiving::create($uu); 
       
                
    
                
                 $Quantity =ProductsQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('P_Code',$Product_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$Product_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$Product_Code[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$Product_Code[$i])    
                ->first(); 

}
}
}

                
                
                   $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();
                
                    if(!empty($Quantity)){
                        
         
                
           $qq= $unit->Rate * request('Qtyy') ;
                
           $newqty=$Quantity->Qty +  $qq ; 
            
   $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();  
    $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();  

        $purchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('Total_Bf_Tax');     
        $countPurchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('SmallQty');
               $storesTransfer=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',$Store[$i])->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',$Store[$i])->get()->sum('SmallTrans_Qty');          
              
                
           $OUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',$Store[$i])->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',$Store[$i])->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;
                
                        
                        
                     if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                        }else{
                             $ty= $Collect /  1 ;    
                            
                        }
                
                if($ty != 0){
                   $in=$qq * $ty ;
         $out=0;     
         $current=$newqty * $ty ;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }
                        
                        
                                        $newQQty=$unit->Rate * request('Qtyy'); 
            $ty=$this->AverageCost($Tot,$newQQty,$Product[$i],$Product_Code[$i],$Store[$i],request('Date'),$plow->Price);    
                
        
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$Product_Code[$i])->where('Product',$Product[$i])->where('Store',$Store[$i])->first();
           
  
                
         $in=$Tot;
                        
                        
                        
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $Tot;  
                }else{
            $current= $Tot;            
                }
     
                
                                $cur=$newqty * $ty ;
               ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty,'Price'=>$ty , 'TotalCost'=>$cur]);   
              $prooooo=Products::find($Product[$i]);     
          $move['Date']=request('Date');
        $move['Type']='مخرجات تصنيع';
                           $move['TypeEn']='Manufacturing Outcome';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
         $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$Product_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=request('Qtyy');
          $move['Group']=$prooooo->Group;
          $move['Store']=$Store[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
                                   $Sro=Stores::find($Store[$i]);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move);                    
   
                    }else{
                    $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();      
                  $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();         
        $id_store = DB::table('products_stores')->insertGetId(
            
        array(
            
            'P_Ar_Name' => $P_Ar_Name[$i],
            'P_En_Name' => $P_En_Name[$i],
            'P_Code' =>   $Product_Code[$i],
            'Exp_Date' => null,
            'Product' => $Product[$i],
            'Store' =>$Store[$i],
            'V1' => null,
            'V2' => null,        
            'V_Name' => null,        
            'VV_Name' => null,        
     
        )
    );          
                

                        
                                   $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                        $pqty['P_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }    
                        
                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Qty']=request('Qtyy') * $pp->Rate;
                    $pqty['Price']=$Price[$i];
                    $pqty['TotalCost']=$Price[$i] * request('Qtyy');
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$Store[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Product[$i];
                    $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;
                          $prooooo=Products::find($Product[$i]); 
        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
                 
  $proooooStore=Stores::find($Store[$i]);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;
              ProductsQty::create($pqty);   
              
                        
                                 $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();  

         $purchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('Total_Bf_Tax');     
        $countPurchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$Store[$i])->get()->sum('SmallQty');
                                   $storesTransfer=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',$Store[$i])->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',$Store[$i])->get()->sum('SmallTrans_Qty');          
              
                
           $OUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',$Store[$i])->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',$Store[$i])->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');    

     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;
                                
                        
                        
                        if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                        }else{
                             $ty= $Collect /  1 ;    
                            
                        }
                if($ty != 0){
                   $in=(request('Qtyy') * $pp->Rate) * $ty ;
         $out=0;     
         $current=(request('Qtyy') * $pp->Rate) * $ty  ;  
                }else{
                  
             $in=(request('Qtyy') * $pp->Rate) * 1;
         $out=0;     
         $current=(request('Qtyy') * $pp->Rate) * 1;        
                    
                }
                        
                        
                        
                                             $newQQty=$unit->Rate * request('Qtyy'); 
            $ty=$this->AverageCost($Tot,$newQQty,$Product[$i],$Product_Code[$i],$Store[$i],request('Date'),$plow->Price);    
                
        
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$Product_Code[$i])->where('Product',$Product[$i])->where('Store',$Store[$i])->first();
           
  
                
         $in=$Tot;
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $Tot;  
                }else{
            $current= $Tot;            
                }
                        
                        
 
              $prooooo=Products::find($Product[$i]);     
          $move['Date']=request('Date');
          $move['Type']='مخرجات تصنيع';
                           $move['TypeEn']='Manufacturing Outcome';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=request('Qtyy') * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=request('Qtyy') * $pp->Rate;
         $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$Product_Code[$i];
          $move['Unit']=$Unit[$i];
         $move['QTY']=request('Qtyy');                
          $move['Group']=$prooooo->Group;
          $move['Store']=$Store[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
                            $Sro=Stores::find($Store[$i]);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move);                      
                        
                        
                        
                    }
   
                            //Fifo
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){              
                     $fifo =FifoQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('P_Code',$Product_Code[$i])    
                ->where('Purchases_Date',request('Date'))    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$Product_Code[$i])    
                      ->where('Purchases_Date',request('Date'))    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$Product_Code[$i])   
                      ->where('Purchases_Date',request('Date'))    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$Store[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$Product_Code[$i]) 
                      ->where('Purchases_Date',request('Date'))    
                ->first(); 

}

}

}
    
    

    if(!empty($fifo)){
        
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * request('Qtyy') ;
                
           $newqty=$fifo->Qty +  $qq ; 
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty,'Original_Qty'=>$newqty]);   
        
    }else{
        
 


      $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                 $pqty['Exp_Date']=null;
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Qty']=request('Qtyy') * $pp->Rate;
                    $pqty['Original_Qty']=request('Qtyy') * $pp->Rate;
                    $pqty['Cost_Price']=$Price[$i];
                    $pqty['Store']=$Store[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Product[$i];
              
           $prooooo=Products::find($Product[$i]); 
                        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
         
              $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;    
                    $pqty['P_Code']=$Product_Code[$i];  
                
  $proooooStore=Stores::find($Store[$i]);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;
                    $pqty['Purchases_Date']=request('Date');
              FifoQty::create($pqty);  
        
        

        
    }
    
    
    
    
    }
                  
          
                
                
            }  

              
          }

          if(!empty(request('UnitI'))){
            
              $Product_CodeI=request('Product_CodeI');
              $P_Ar_NameI=request('P_Ar_NameI');
              $P_En_NameI=request('P_En_NameI');
              $QtyI=request('QtyHide');
              $Dep=request('QtyI');
              $PriceI=request('PriceI');
              $ProductI=request('ProductI');
              $StoreI=request('StoreI');
              $UnitI=request('UnitI');
              $INSTORE=request('inStore');
             

            for($i=0 ; $i < count($UnitI) ; $i++){

                $uuz['Product_Code']=$Product_CodeI[$i];
                $uuz['P_Ar_Name']=$P_Ar_NameI[$i];
                $uuz['P_En_Name']=$P_En_NameI[$i];
                $uuz['Qty']=$QtyI[$i];
                $uuz['Dep']=$Dep[$i];
                $uuz['Price']=$PriceI[$i];
                $uuz['Total']=$PriceI[$i];
                $uuz['Store']=$StoreI[$i];
                $uuz['Product']=$ProductI[$i];
                $uuz['Unit']=$UnitI[$i];
                   $uu['type']=0;
                $uuz['Executing']=$ID;

               ProductsExecutingReceiving::create($uuz); 
                
                
            if($INSTORE[$i] == 1){
               
                $dd='0'.$Product_CodeI[$i];
      
             
                             $Quantity =ProductsQty::
                where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('P_Code',$dd)    
                 ->where('Original','Damage')          
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
              where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PP_Code',$dd)    
                 ->where('Original','Damage')          
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
          where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PPP_Code',$dd)    
                 ->where('Original','Damage')            
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
          where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PPPP_Code',$dd)    
                 ->where('Original','Damage')             
                ->first(); 

}
}
}
    
                
                
                
                 $pp=ProductUnits::where('Product',$ProductI[$i])->where('Unit',$UnitI[$i])->first();  
                
            if(!empty($Quantity)){

               $xx= $Quantity->Qty + ($QtyI[$i] * $pp->Rate)  ;
            ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$xx]);    
             
                    $cur=$xx * 0 ;
               ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$xx,'Price'=>0 , 'TotalCost'=>0]); 
                
          $prooooo=Products::find($ProductI[$i]);     
          $move['Date']=request('Date');
          $move['Type']='هوالك';
          $move['TypeEn']='Perished';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$QtyI[$i] ;
          $move['Outcom']=0;
          $move['Current']=$xx;
         $move['CostIn']=0;
          $move['CostOut']=0;
          $move['CostCurrent']=0;         
          $move['P_Ar_Name']=$P_Ar_NameI[$i];
          $move['P_En_Name']=$P_En_NameI[$i];
          $move['P_Code']=$Product_CodeI[$i];
          $move['Unit']=$UnitI[$i];
          $move['QTY']=$QtyI[$i];
          $move['Group']=$prooooo->Group;
          $move['Store']=$StoreI[$i];
          $move['Product']=$ProductI[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
                    $Sro=Stores::find($StoreI[$i]);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move);                    
  
                        
            }else{
          

                 
                                      $prooooo =ProductsQty::
                where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('P_Code',$Product_CodeI[$i])          
                ->first(); 

if(empty($prooooo)){

  $prooooo =ProductsQty::
              where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PP_Code',$Product_CodeI[$i])            
                ->first(); 

if(empty($prooooo)){

  $prooooo =ProductsQty::
          where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PPP_Code',$Product_CodeI[$i])             
                ->first(); 


if(empty($prooooo)){

  $prooooo =ProductsQty::
          where('Store',$StoreI[$i])    
                ->where('Product',$ProductI[$i])    
                ->where('PPPP_Code',$Product_CodeI[$i])             
                ->first(); 

}
}
}
        
                
                
        $id_store = DB::table('products_stores')->insertGetId(    
        array(
            
            'P_Ar_Name' => $prooooo->P_Ar_Name.'(هالك)',
            'P_En_Name' => $prooooo->P_En_Name.'(Damage)',
            'P_Code' =>   '0'.$prooooo->P_Code,
            'Exp_Date' => $prooooo->Exp_Date,
            'Product' => $prooooo->Product,
            'Store' =>$prooooo->Store,
            'V1' => $prooooo->V1,
            'V2' => $prooooo->V2,        
            'V_Name' => $prooooo->V_Name,        
            'VV_Name' => $prooooo->VV_Name,        
     
        )
    );          
                

                    $pqty['P_Ar_Name']=$prooooo->P_Ar_Name.'(هالك)';
                    $pqty['P_En_Name']=$prooooo->P_En_Name.'(Damage)';
                    $pqty['P_Code']='0'.$prooooo->P_Code;
                    $pqty['Qty']=$QtyI[$i] * $pp->Rate;
                    $pqty['Price']=0;
                    $pqty['TotalCost']=0;
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$StoreI[$i];
                    $pqty['Unit']=$UnitI[$i];
                    $pqty['Low_Unit']=$prooooo->Low_Unit;
                    $pqty['Product']=$prooooo->Product;
                    $pqty['V1']=$prooooo->V1;
                    $pqty['V2']= $prooooo->V2;
                    $pqty['V_Name']=$prooooo->V_Name;
                    $pqty['VV_Name']=$prooooo->VV_Name;
                    $pqty['Original']='Damage';

                                  $prooooo=Products::find($prooooo->Product); 
        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
                   $proooooStore=Stores::find($StoreI[$i]);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;

              ProductsQty::create($pqty);   
              
         
          $move['Date']=request('Date');
          $move['Type']='هوالك';
                 $move['TypeEn']='Perished';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$QtyI[$i] * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=$QtyI[$i] * $pp->Rate;
         $move['CostIn']=0;
          $move['CostOut']=0;
          $move['CostCurrent']=0;         
          $move['P_Ar_Name']=$prooooo->P_Ar_Name.'(هالك)';
          $move['P_En_Name']=$prooooo->P_En_Name.'(Damage)';
          $move['P_Code']='0'.$Product_CodeI[$i];
          $move['Unit']=$UnitI[$i];
          $move['QTY']=$QtyI[$i];
          $move['Group']=$prooooo->Group;
          $move['Store']=$StoreI[$i];
          $move['Product']=$ProductI[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
                    $Sro=Stores::find($StoreI[$i]);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move);                            
                
                
                
                
            }    
                
                
                
            }    
                
                
                
                 
            }  

              
          }

          ManuStoreCount::truncate();
          
          
          
          foreach($ins as $in){
 
             $Quantity =ProductsQty::
                where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('P_Code',$in->Product_Code)    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                   where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PP_Code',$in->Product_Code)    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                 where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPP_Code',$in->Product_Code)   
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                 where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPPP_Code',$in->Product_Code)   
                ->first(); 

}
}
}
  
              
              
                   $unit=ProductUnits::where('Unit',$in->Unit)->where('Product',$in->Product)->first();  
                $plow=ProductUnits::where('Product',$in->Product)->where('Rate',1)->first();  
 $purchs=ProductsPurchases::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('Store',$in->Store)->get()->sum('Total_Bf_Tax');     
              
  $countPurchs=ProductsPurchases::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('Store',$in->Store)->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('Store',$in->Store)->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('Store',$in->Store)->get()->sum('SmallQty');
 
              
                $storesTransfer=ProductsStoresTransfers::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$in->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$in->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$in->Store)->get()->sum('SmallTrans_Qty');                  
                
               $OUTCOME=OutcomManufacturingModel::where('Product',$in->Product)->where('Store',$in->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$in->Product)->where('Store',$in->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;          
              
              
              if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
              }else{
                  
                 $ty= $Collect ;   
              }
                          if($in->Product()->first()->P_Type != 'Service'){  
              $Qeqo=request('Qtyy') * $in->Qty ; 
           $qq= $unit->Rate *  $Qeqo;
                
           $newqty=$Quantity->Qty -  $qq ; 
                
        
                
            $cur=$newqty * $ty ;
               ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty,'Price'=>$ty , 'TotalCost'=>$cur]);         
            }
                        
             if($in->Product()->first()->P_Type == 'Service'){  
             
           $qq=0;
                
           $newqty=0; 
                
        
                
            $cur=0 ;
           
            }
                  
           
              
              if(empty($qq)){
                  $qq=0;
              }
                 
              
                  if($ty != 0){
                     $inn=0;
         $out=$qq * 1;     
         $current=$newqty * 1;  
              }else{
              
                        $inn=0;
         $out=$qq * $ty ;    
         $current=$newqty * $ty;
                  
              }

              
              
              
              
              
                              
                                $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){ 
    
    
       $totCost=0;
         
            
                

         
         $rr = ProductUnits::where("Unit",$in->Unit)->where('Product',$in->Product)->first();
  

            $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('P_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)     
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)   
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPPP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)      
                ->first(); 

}

}

}
    
               if(!empty($fifo)){
                   
                        if($fifo->Qty == 0){
                       
               
                       
         $NNQuntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
      
                    if($NNQuntatity == 0){
                           
               
         $NNQuntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
     
                        
                    }else{
                        
                       
                        
                            if($NNQuntatity >= $Qeqo){
                    
                    $totCost += $fifo->Cost_Price * $Qeqo;
                    
                }else{
                
                $res=$Qeqo - $NNQuntatity ;       
                    
                    
                $totCost += $fifo->Cost_Price * $NNQuntatity ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }          
                        
                        
                    }        
                            
                            
                       
                   }else{    
                            
                            
                if($fifo->Qty >= $Qeqo){
                    
                    $totCost += $fifo->Cost_Price * $Qeqo ;
                    
                }else{
                
                $res=$Qeqo - $fifo->Qty ;       
                    
                    
                $totCost += $fifo->Cost_Price * $fifo->Qty ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }            
                            
                       
                            
                   }
                   
                   
               }

    
    
           $newQQty=$unit->Rate *  $Qeqo;
      $ty=$this->AverageCostTwo($in->Total,$newQQty,$in->Product,$plow->Barcode,$in->Store);    
                       
              
                $CostTotalSale=$totCost;
        
                
        
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$in->Product)->where('Store',$in->Store)->first();
           
  
                
         $inn=0;
         $out=$CostTotalSale;  
            $current=($lastOperation->Current - $newQQty) *  $ty;      



         
    
    
}else{
  
    
           $newQQty=$unit->Rate *  $Qeqo;
      $ty=$this->AverageCostTwo($in->Total,$newQQty,$in->Product,$plow->Barcode,$in->Store);    
                       
              
                $CostTotalSale=$ty * $newQQty;
        
                
        
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$in->Product)->where('Store',$in->Store)->first();
           
  
                
         $inn=0;
         $out=$CostTotalSale;  
            $current=($lastOperation->Current - $newQQty) *  $ty;      




}
    
              
              
              
                         

              $prooooo=Products::find($in->Product);     
          $move['Date']=request('Date');
          $move['Type']='مدخلات تصنيع';
                            $move['TypeEn']='Manufacturing Income';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=0;
          $move['Outcom']=$qq;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($inn), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$in->P_Ar_Name;
          $move['P_En_Name']=$in->P_En_Name;
          $move['P_Code']=$in->Product_Code;
          $move['Unit']=$in->Unit;
          $move['QTY']=request('Qtyy');
          $move['Group']=$prooooo->Group;
          $move['Store']=$in->Store;
          $move['Product']=$in->Product; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
                  $Sro=Stores::find($in->Store);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move); 
              
              
              
              
                            
                                //Fifo
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){       
    
                     $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('P_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)   
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)   
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$in->Store)    
                ->where('Product',$in->Product)    
                ->where('PPPP_Code',$in->Product_Code)    
                  ->where('Qty','!=',0)    
                ->first(); 

}

}

}
    
    

    if(!empty($fifo)){
        
        if($fifo->Qty >= $Qeqo){
            
     
            
                   $unit=ProductUnits::where('Unit',$in->Unit)->where('Product',$in->Product)->first();  
                
           $qq= $unit->Rate * $Qeqo ;
                
           $newqty=$fifo->Qty -  $qq ; 
            
        
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
        
     
            
        }else{
            
            
        $resdiualQty=$Qeqo - $fifo->Qty ;
            
              $unit=ProductUnits::where('Unit',$in->Unit)->where('Product',$in->Product)->first();  
                
           $qq= $unit->Rate * $fifo->Qty ;
                
           $newqty=$fifo->Qty -  $qq ; 
            
        
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
            

        
     $ResdiualCost=$this->FifoStoreQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$resdiualQty,$in->Unit);          
            
            
            
        }
        
    
    }
    
    
    
    
    }
                  
       
              
              
              
              
              
              
              
 
                $Mkhazns=ManuStoreCount::all();
                
                 if(count($Mkhazns) == 0){
                  
                    $s['Store']=$in->Store; 
                    $s['Total']=$in->Total * request('Qtyy'); 
                 ManuStoreCount::create($s);     
                     
                 }else{
                     
                     
           $m=ManuStoreCount::where('Store',$in->Store)->first();           
  
                        if(!empty($m)){
                           
                            
                            $newTot=$m->Total + ($in->Total * request('Qtyy') ) ;
                            
                             ManuStoreCount::where('id',$m->id)->update(['Total'=>$newTot]); 
                            
                        }else{
                            
                         
                    $s['Store']=$in->Store; 
                    $s['Total']=$in->Total * request('Qtyy'); 
                 ManuStoreCount::create($s);      
                            
                        }
  
                 }
   
          }
          
        //==================================  
          
          $model=ManufacturingModel::find(request('Model'));

                $NewMkhazns=ManuStoreCount::all();

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'التصنيع',
            'TypeEn' => 'Manufacturing',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => $model->Draw,
            'Coin' =>$model->Coin,
            'Cost_Center' => $model->Cost_Center,
            'Total_Debaitor' => $model->Total_Price * request('Qtyy'),
            'Total_Creditor' => $model->Total_Price * request('Qtyy'),
            'Note' => null,
  
        )
    );
           
          foreach($NewMkhazns as $new){
           $out=OutcomManufacturingModel::where('Model',request('Model'))->first();   
         $store=Stores::find($new->Store);         
         $storeOut=Stores::find($out->Store);         

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='التصنيع';
        $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * 0;
        $Gen['Creditor_Coin']=$model->Draw * $new->Total;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']=$model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$storeOut->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
               $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * $new->Total;
        $Gen['Creditor_Coin']=$model->Draw * 0;
        $Gen['Account']=$storeOut->Account;
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']= $model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
              
          }
    
          
          
          if(request('Sort') == 2){
              
              
                 $account=AcccountingManual::where('Name','تكلفه مصنعيه')->first();
    
            $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Workmentship');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
            $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=request('Total_Workmentship');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * request('Total_Workmentship');
        $Gen['Creditor_Coin']=$model->Draw * 0;
        $Gen['Account']=$account->id;
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']=$model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Workmentship');
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Workmentship');
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * 0;
        $Gen['Creditor_Coin']=$model->Draw * request('Total_Workmentship');
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']= $model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      


              
          }
          
          
          
             $in=IncomManufacturingModel::where('Model',request('Model'))->orderBy('id','asc')->first();
                    $store=Stores::find($in->Store);
    
        $dataStMove['Date']=date('Y-m-d');
           $dataStMove['Code']=$CodeT;
           $dataStMove['Time']=date("h:i:s a", time());
           $dataStMove['Branch']=$store->Branch;
           $dataStMove['Store']=$in->Store;
           $dataStMove['Safe']=null;
           $dataStMove['Type']='مدخلات تصنيع';
           $dataStMove['TypeEn']='Income Manfcturing';
           $dataStMove['Cost_Center']=$model->Cost_Center;
           $dataStMove['User']=auth()->guard('admin')->user()->id;
           $dataStMove['Coin']=$model->Coin;
           $dataStMove['Note']=null;
           $dataStMove['Total_Qty']=request('Qtyy');
           $dataStMove['Total_Price']=request('Totall')*request('Qtyy');
           $dataStMove['Account']=null;
           $dataStMove['Ship']=null;
           $dataStMove['ID']=$ID;
           StoresMoves::create($dataStMove);   

                 $store=Stores::find($out->Store);
    
        $dataStMove['Date']=date('Y-m-d');
           $dataStMove['Code']=$CodeT;
           $dataStMove['Time']=date("h:i:s a", time());
           $dataStMove['Branch']=$store->Branch;
           $dataStMove['Store']=$out->Store;
           $dataStMove['Safe']=null;
           $dataStMove['Type']='مخرجات تصنيع';
           $dataStMove['TypeEn']='Outcome Manfcturing';
           $dataStMove['Cost_Center']=$model->Cost_Center;
           $dataStMove['User']=auth()->guard('admin')->user()->id;
           $dataStMove['Coin']=$model->Coin;
           $dataStMove['Note']=null;
           $dataStMove['Total_Qty']=request('Qtyy');
           $dataStMove['Total_Price']=request('Totall')*request('Qtyy');
           $dataStMove['Account']=null;
           $dataStMove['Ship']=null;
           $dataStMove['ID']=$ID;
           StoresMoves::create($dataStMove);   


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='تنفيذ و استلام';
           $dataUser['ScreenEn']='Executing and Receiving';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return redirect('ExecutingandReceiving');
            
        
    }
    
    
    //====   Manufacturing Order  =======
     public function ManufacturingOrderPage(){

    $res=ManufacturingOrder::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               $month = date("m",strtotime($res->Date));
               if($month == date('m')){
                   
                 $Code=$res->Code + 1 ; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;   
                   
               }else{
               
                    $Code= 1; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;      
                   
               }
 
           }else{
                $Year=date('Y');
               $Month=date('m');
              $Code=1; 
                 $NewCode=$Year.$Month.'0'.$Code;   
           }

             $Models=ManufacturingModel::all(); 
         
              $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
           
        $Employess = Employess::
             where('Emp_Type','Saller')
             ->orWhere('Emp_Type','Buyer')
                 
                 ->where("EmpSort",1)->where('Active',1) 
              ->get();
         
         
         return view('admin.Manufacturing.ManufacturingOrder',[
             'Code'=>$Code,
             'NewCode'=>$NewCode,
             'Models'=>$Models,
             'Clients'=>$Clients,
             'Employess'=>$Employess,
         ]);
    }
    
     function ManufacturingOrderFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $Model = $request->get('Model');             
      $i=1;
                               
    if($Model != '')
    {

        
     $Outcome=OutcomManufacturingModel::where('Model',$Model)->first();
        $Ins=IncomManufacturingModel::where('Model',$Model)->get();
        
        
                  if(app()->getLocale() == 'ar' ){ 
                      $OutName=$Outcome->P_Ar_Name; 

                   
                   }else{
                         $OutName=$Outcome->P_En_Name;  

                       
                   }  
             
     }

         $total_row = $Ins->count();
      if($total_row > 0) 
      { 
   
          
         foreach($Ins as $in){  
           
             
            if(auth()->guard('admin')->user()->emp == 0){
                $prec='<td>'.$in->Precent.'</td>';                                   
            }else{                   
        if(auth()->guard('admin')->user()->manu_order_precent == 1){                
              $prec='<td>'.$in->Precent.'</td>';     
                }else{
            
                $prec="";       
            
        }
                                             
            }    
       
             
           $Qunta=ProductsQty::where('Product',$in->Product)->where('P_Code',$in->Product_Code)->where('Store',$in->Store)->get()->sum('Qty');

             if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$in->P_Ar_Name; 
                      $UniiName=$in->Unit()->first()->Name; 
                      $StoreNemo=$in->Store()->first()->Name; 
                   
                   }else{
                         $PrrroName=$in->P_En_Name;  
                       $UniiName=$in->Unit()->first()->NameEn;    
                        $StoreNemo=$in->Store()->first()->NameEn; 
                       
                   }        
             
             
             $output .='
             <tr id="ROW'.$i.'">
             <td>'.$in->Product_Code.'
        <input type="hidden" name="Product[]" value="'.$in->Product.'">     
        <input type="hidden" name="Product_Code[]" value="'.$in->Product_Code.'">     
        <input type="hidden" name="P_Ar_Name[]" value="'.$in->P_Ar_Name.'">     
        <input type="hidden" name="P_En_Name[]" value="'.$in->P_En_Name.'">     
        <input type="hidden" name="V_Name[]" value="">     
        <input type="hidden" name="VV_Name[]" value="">     
        <input type="hidden" name="V1[]" value="">     
        <input type="hidden" name="V2[]" value="">     
        <input type="hidden" name="Unit[]" value="'.$in->Unit.'">     
        <input type="hidden" name="Store[]" value="'.$in->Store.'">     
        <input type="hidden" id="Store_Qty'.$i.'" name="Store_Qty[]" value="'.$Qunta.'">     
             </td>
             <td>'.$PrrroName.'</td>
             <td>'.$UniiName.'</td>
             <td>'.$Qunta.'</td>
             
             '.$prec.'
             
             <td>
             '.$in->Qty.'
             <input type="hidden" name="Qty[]" id="Qty'.$i.'" value="'.$in->Qty.'">     
             <input type="hidden" name="Precent[]"  value="'.$in->Precent.'">     
             </td>
             
             <td>
           <input type="number" step="any" class="form-control"  id="Required_Qty'.$i.'" value="'.$in->Qty.'" disabled>
           <input class="RQTY" type="hidden" name="Required_Qty[]" id="Required_QtyHide'.$i.'"  value="'.$in->Qty.'" >
             </td>
             <td>'.$StoreNemo.'</td>
             </tr>
             ';
     
             $i++;
        }  


      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
       'Outcome'  => $OutName,
       'Model'  => $Model,
      );
      echo json_encode($data);
     }
    }
    
    public function AddManufacturingOrder(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Model'=>'required',
             'Name_Outcome'=>'required',
             'Except_Qty'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
         ]);

         
           $ID = DB::table('manufacturing_orders')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'NewCode' => request('NewCode'),
            'Date' => request('Date'),
            'Model' => request('Model'),
            'Name_Outcome' => request('Name_Outcome'),
            'Except_Qty' => request('Except_Qty'),
            'Total_Required_Qty' => request('Total_Required_Qty'),
            'Status' => 0,
            'For_Client' => request('For_Client'),
            'Client' => request('Client'),
            'Client_Phone' => request('Client_Phone'),
            'Client_Address' => request('Client_Address'),
            'Delegate' => request('Delegate'),
            'Delegate_Phone' => request('Delegate_Phone'),
            'Recived_Date' => request('Recived_Date'),
            'Manufacture_Request_Code' => request('Manufacture_Request_Code'),
            'Recipient' => null,
     

        )
    );  
        
        
        if(!empty(request('Recived_Date'))){
                 $event['Start_Date']=request('Recived_Date');
         $event['End_Date']=request('Recived_Date');
         $event['Event_Ar_Name']='تسليم امر تصنيع';
         $event['Event_En_Name']='Recived Manufacturing Order';
         $event['Type']='امر تصنيع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Delegate');
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
        }


 



    
          if(!empty(request('Unit'))){
            
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $Store_Qty=request('Store_Qty');
              $Required_Qty=request('Required_Qty');
              $Qty=request('Qty');
              $Precent=request('Precent');
              $Store=request('Store');
              $Product=request('Product');
              $V1=request('V1');
              $V2=request('V2');
              $Unit=request('Unit');


            for($i=0 ; $i < count($Unit) ; $i++){


                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Store_Qty']=$Store_Qty[$i];
                $uu['Required_Qty']=$Required_Qty[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Precent']=$Precent[$i];
                $uu['Store']=$Store[$i];
                $uu['Product']=$Product[$i];
                $uu['V1']=$V1[$i];
                $uu['V2']=$V2[$i];
                $uu['Unit']=$Unit[$i];
                $uu['ManuOrder']=$ID;

               ProductsManufacturingOrder::create($uu); 
                
                 
            }  

              
          }

     
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='آمر تصنيع';
           $dataUser['ScreenEn']='Manufacturing Order';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
                return back(); 
            
        
    }
    
    public function ManufacturingOrderSechdule(){

    $items=ManufacturingOrder::whereIn('Status',[0,2,3])->paginate(100);
   $Employess = Employess::
             whereIn('Emp_Type',['Manager','Techinical','StoreKeeper','ObserverQuality','Worker'])
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
         return view('admin.Manufacturing.ManufacturingOrderSechdule',[
             'items'=>$items,
             'Employess'=>$Employess,

         ]);
    }
    
    public function DeleteManufacturingOrder($id){
                      
         $del=ManufacturingOrder::find($id);
        
    
  Event::where('Type_Code',$del->Code)->where('Type','امر تصنيع')->delete();
    
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='آمر تصنيع';
           $dataUser['ScreenEn']='Manufacturing Order';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
       public function EditManufacturingOrderPage($id){

    $item=ManufacturingOrder::find($id);
    $Prods=ProductsManufacturingOrder::where('ManuOrder',$id)->get();
    $Models=ManufacturingModel::all(); 
                 $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
           
        $Employess = Employess::
             where('Emp_Type','Saller')
             ->orWhere('Emp_Type','Buyer')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
            
           
                                            if($item->Status != 0){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('ManufacturingOrderSechdule');
        
                    }
         return view('admin.Manufacturing.EditManufacturingOrder',[
             'item'=>$item,
             'Prods'=>$Prods,
             'Models'=>$Models,
             'Clients'=>$Clients,
             'Employess'=>$Employess,
         ]);
    }
    
     public function PostEditManufacturingOrder($id){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Model'=>'required',
             'Name_Outcome'=>'required',
             'Except_Qty'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
         ]);

            $data['Code'] = request('Code');
            $data['NewCode'] = request('NewCode');
            $data['Date'] = request('Date');
            $data['Model'] = request('Model');
            $data['Name_Outcome'] = request('Name_Outcome');
            $data['Except_Qty'] = request('Except_Qty');
            $data['Total_Required_Qty'] = request('Total_Required_Qty');
            $data['Status'] = 0;
            $data['For_Client'] = request('For_Client');
            $data['Client'] = request('Client');
            $data['Client_Phone'] = request('Client_Phone');
            $data['Client_Address'] = request('Client_Address');
            $data['Delegate'] = request('Delegate');
            $data['Delegate_Phone'] = request('Delegate_Phone');
            $data['Recived_Date'] = request('Recived_Date');
            $data['Manufacture_Request_Code'] = request('Manufacture_Request_Code');
            $data['Recipient'] = null;
     
            ManufacturingOrder::where('id',$id)->update($data);
    
                 
        if(!empty(request('Recived_Date'))){
             $del=ManufacturingOrder::find($id);
  Event::where('Type_Code',$del->Code)->where('Type','امر تصنيع')->delete();
    
                 $event['Start_Date']=request('Recived_Date');
         $event['End_Date']=request('Recived_Date');
         $event['Event_Ar_Name']='تسليم امر تصنيع';
         $event['Event_En_Name']='Recived Manufacturing Order';
         $event['Type']='امر تصنيع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Delegate');
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
        }


          if(!empty(request('Unit'))){
            
              ProductsManufacturingOrder::where('ManuOrder',$id)->delete();
              
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $Store_Qty=request('Store_Qty');
              $Required_Qty=request('Required_Qty');
              $Qty=request('Qty');
              $Store=request('Store');
              $Product=request('Product');
              $V1=request('V1');
              $V2=request('V2');
              $Unit=request('Unit');
     $Precent=request('Precent');

            for($i=0 ; $i < count($Unit) ; $i++){


                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Store_Qty']=$Store_Qty[$i];
                $uu['Required_Qty']=$Required_Qty[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Store']=$Store[$i];
                $uu['Product']=$Product[$i];
                $uu['V1']=$V1[$i];
                $uu['V2']=$V2[$i];
                $uu['Unit']=$Unit[$i];
                $uu['ManuOrder']=$id;
              $uu['Precent']=$Precent[$i];
               ProductsManufacturingOrder::create($uu); 
                
                 
            }  

              
          }

     
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='آمر تصنيع';
           $dataUser['ScreenEn']='Manufacturing Order';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
                     return redirect('ManufacturingOrderSechdule');
            
        
    }
    
       public function ExchangeGoodsManufacturingOrder(){
                   
        $id=request('ID');   
         ManufacturingOrder::where('id',$id)->update(['Status'=>1,'Recipient'=>request('Recipient')]);
       
        session()->flash('error',trans('admin.Done'));
           
        if(request('SP') == 0){
                  return back(); 
        }elseif(request('SP') == 1){
        
         return redirect('ManufacturingExchangeGoodsPrint/'.$id);
            
        }   
           



           }
    
    public function ManufacturingExchangeGoodsPrint($id){

            $Prods=ProductsManufacturingOrder::where('ManuOrder',$id)->get();
         return view('admin.Manufacturing.ManufacturingExchangeGoodsPrint',[
             'Prods'=>$Prods,

         ]);
    }
    
    
    //=========  Manufacturing_Request =========
       public function Manufacturing_RequestPage(){

    $res=ManufacturingRequest::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               

               
              $Code=$res->Code + 1  ;
               
 
                          
           }else{
               
              $Code=1; 
        
               
           }

           $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
           
        $Employess = Employess::
             where('Emp_Type','Saller')
             ->orWhere('Emp_Type','Buyer')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
         
                if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
           
           
         return view('admin.Manufacturing.ManufacturingRequest',[
             'Code'=>$Code,
             'Clients'=>$Clients,
             'Employess'=>$Employess,
             'Stores'=>$Stores,
         ]);
    }
 
           public function ClientPhoneAndAddress($id){

$states=[];
          
$cust=Customers::where('Account',$id)->first();

 
            $states +=['phone'=>$cust->Phone,'address'=>$cust->Address];
           return response()->json($states);
        
    }
    
      public function DelegatePhone($id){

$states=[];
          
$cust=Employess::find($id);

 
            $states +=['phone'=>$cust->Phone];
           return response()->json($states);
        
    }
 
         function ManufacturingRequestFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             
      $client = $request->get('Client');             
  
         
    if($search != '' and $store != '' and $client != '')
    {

                              $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
                 $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
           ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('P_Code',$search)        
           ->orWhere('PP_Code',$search)        
           ->orWhere('PPP_Code',$search)        
           ->orWhere('PPPP_Code',$search)        
           ->take(500)        
           ->get(); 
            
            
 }else{
            
         $Prods=ProductsQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
           ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('P_Code','ILIKE', "%{$search}%")        
           ->orWhere('PP_Code','ILIKE', "%{$search}%")        
           ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
           ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
           ->take(500)        
           ->get();         
            
            
  } 
        
       

                   
     }

         $total_row = $Prods->count();
      if($total_row > 0) 
      { 
           
       
            $showw=DefaultDataShowHide::orderBy('id','desc')->first();
          
         foreach($Prods as $rows){  
             
           if($showw->Expire_Date == 1){
                
                $EXPIRE='<td><input type="date" id="ExpDate'.$rows->id.'"   class="form-control"></td>';
            }else{
                
                $EXPIRE='<input type="hidden" id="ExpDate'.$rows->id.'"   class="form-control">';
            } 
             
        if($rows->Store == $store){     
             
                  $Stores=Stores::all();  
    $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 
        
             $Cli=Customers::where('Account',$client)->first();  
                
        if(!empty($Cli)){
            
            if($Cli->Price_Level == 1){
                
            $pr= $rr->Price ; 
                
            }elseif($Cli->Price_Level == 2){
                
            if(!empty($rr->Price_Two) and $rr->Price_Two != 0){
                
                $pr= $rr->Price_Two ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }    
 
            }elseif($Cli->Price_Level == 3){
                
              if(!empty($rr->Price_Three) and $rr->Price_Three != 0 ){
                
                $pr= $rr->Price_Three ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }           
                
            }
            
        }else{
            
         $Vend=Vendors::where('Account',$client)->first();   
          
            if(!empty($Vend)){
           if($Vend->Price_Level == 1){
                
            $pr= $rr->Price ; 
                
            }elseif($Vend->Price_Level == 2){
                
            if(!empty($rr->Price_Two) and $rr->Price_Two != 0){
                
                $pr= $rr->Price_Two ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }    
 
            }elseif($Vend->Price_Level == 3){
                
              if(!empty($rr->Price_Three) and $rr->Price_Three != 0){
                
                $pr= $rr->Price_Three ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }           
                
            }    
            }else{
             
                   $EMPO=Employess::where('Account_Emp',$client)->first();   
                
                  if($EMPO->Price_Level == 1){
                
            $pr= $rr->Price ; 
                
            }elseif($EMPO->Price_Level == 2){
                
            if(!empty($rr->Price_Two) and $rr->Price_Two != 0){
                
                $pr= $rr->Price_Two ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }    
 
            }elseif($EMPO->Price_Level == 3){
                
              if(!empty($rr->Price_Three) and $rr->Price_Three != 0){
                
                $pr= $rr->Price_Three ;
                
            }else{
             
                 $pr= $rr->Price ;
                
            }           
                
            }    
                
                
            }
            
            
        }
                        
            if($rows->Original != null){
                
              $pr=0;  
            }    
 
        $st=Stores::find($store);
                
              if(!empty($Cli)){
                  
                  if(!empty($Cli->Group)){
                  $nwD=CustomersGroup::find($Cli->Group);
                           
                      $DES= $nwD->Discount ;
               
                  }else{
                      $DES=0;   
                      
                  }
                  
              }else{
                $DES=0; 
              }   
                
          if(!empty($rows->Product()->first()->Group()->first()->Discount)){
             $Higher=$rows->Product()->first()->Group()->first()->Discount;
          }else{
            
            $Higher=0;  
          }      
  
      if(auth()->guard('admin')->user()->emp == 0){

          $PErc='<td> <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'"></td>';
          
          
          $DErc='<td> <input type="number" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$DES.'" ></td>';
     
      }else{
        
            if(auth()->guard('admin')->user()->cost_price_purch == 1){
    
    
          $PErc='<td> <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'"></td>';
          
          
          $DErc='<td> <input type="number" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$DES.'" ></td>';            
                
            }else{
                
                
        
          $PErc='<input type="hidden" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'">';
          
          
          $DErc='<input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$DES.'" >';        
                
                
            } 
          
      }    
  

        
            
        if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'){
        

            if($rows->Product()->first()->Status == 0){
              
                   if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   
  
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        <input type="hidden"  id="serial'.$rows->id.'" value="0">
        <input type="hidden"  id="HighDisc'.$rows->id.'" value="'.$Higher.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                     if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>

       
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
        '.$PErc.'
        '.$DErc.'
        
   

        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                      if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
       '.$EXPIRE.'

        <td>
        <input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
          </td>
        </tr>
        
       
            ';
        }
        
        }elseif($rows->Product()->first()->P_Type == 'Serial'){
        
                         
              if($rows->Product()->first()->Status == 0){
        
                  
                              if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                       
                   }   
  

   
                  
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'  
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
         <input type="hidden"  id="serial'.$rows->id.'" value="1">
           <input type="hidden"  id="HighDisc'.$rows->id.'" value="'.$Higher.'">     
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
     if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>

       
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
     '.$PErc.'
        '.$DErc.'
        

<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 


        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                         if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
       '.$EXPIRE.'

        
        <td>
           <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
     
          
          
          </td>
        </tr>
        
       
            ';
        }
                     
        }elseif($rows->Product()->first()->P_Type == 'Single_Variable'){
        
       if($rows->Product()->first()->Status == 0){
                
              if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                                         $PrrroVName=$rows->V1()->first()->Name; 
                 
                    
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                           $PrrroVName=$rows->V1()->first()->NameEn;  
                    
                   }   
  

         
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' ('.$PrrroVName.') 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="'.$rows->V1()->first()->Name.'">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
         <input type="hidden"  id="serial'.$rows->id.'" value="0">
           <input type="hidden"  id="HighDisc'.$rows->id.'" value="'.$Higher.'">   
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value=""> '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
               
     if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
  
       
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
            '.$PErc.'
        '.$DErc.'
        
               
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
   

        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                   if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
       '.$EXPIRE.'


        <td>
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
       
          
          
          </td>
        </tr>
        
       
            ';
        }
          
         }elseif($rows->Product()->first()->P_Type == 'Duble_Variable'){
        
       if($rows->Product()->first()->Status == 0){
                
        if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rows->Unit()->first()->Name; 
                      $StoreNemo=$st->Name; 
                                         $PrrroVName=$rows->V1()->first()->Name; 
                      $PrrroVVName=$rows->V2()->first()->Name; 
                    
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rows->Unit()->first()->NameEn;    
                       $StoreNemo=$st->NameEn;    
                           $PrrroVName=$rows->V1()->first()->NameEn;  
                         $PrrroVVName=$rows->V2()->first()->NameEn;   
                   }  
  

     
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.'  ('.$PrrroVName.') ('.$PrrroVVName.') '.$ShowGrBr.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="'.$PrrroVName.'">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="'.$PrrroVVName.'">
         <input type="hidden"  id="serial'.$rows->id.'" value="0">
             <input type="hidden"  id="HighDisc'.$rows->id.'" value="'.$Higher.'">   
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
     if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->P_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
         '.$PErc.'
        '.$DErc.'
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
          

        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                       if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Name;
                   }else{
                       
                       $StorNamme=$stor->NameEn; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
       '.$EXPIRE.'

        
        <td>
          <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
  
          </td>
        </tr>
        
       
            ';
        }
          
         }

        }  
             
             
        }  


      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').' </div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
        public function AddManufacturingRequest(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Payment_Method'=>'required',
             'Client'=>'required',
             'Store'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),           
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Client.required' => trans('admin.ClientRequired'),      

         ]);

           $ID = DB::table('manufacturing_requests')->insertGetId(
        array(

            'Code' => request('Code'),
            'Date' => request('Date'),
            'Recived_Date' => request('Recived_Date'),
            'Client_Phone' => request('Client_Phone'),
            'Client_Address' => request('Client_Address'),
            'Payment_Method' => request('Payment_Method'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Qty' => request('Total_Qty'),
            'Total_Discount' => request('Total_Discount'),
            'Total_BF_Taxes' => request('Total_BF_Taxes'),
            'Total_Taxes' => request('Total_Taxes'),
            'Total_Price' => request('Total_Price'),
            'Pay' => null,
            'Status' => 0,
            'Client' => request('Client'),
            'Delegate' => request('Delegate'),
            'Delegate_Phone' => request('Delegate_Phone'),
            'Store' => request('Store'),
            'Later_Due' => request('Later_Due'),
            'User' => auth()->guard('admin')->user()->id,
     
        )
    );  

            
            
                 if(!empty(request('Recived_Date'))){

                 $event['Start_Date']=request('Recived_Date');
         $event['End_Date']=request('Recived_Date');
         $event['Event_Ar_Name']='تسليم طلب تصنيع';
         $event['Event_En_Name']='Recived Manufacturing Request';
         $event['Type']='طلب تصنيع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Delegate');
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
        }

            
            
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $PurchTax=request('PurchTax');
              $Total=request('Total');
              $StorePurch=request('StorePurch');
              $Exp_Date=request('Exp_Date');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $Exp_Date=request('Exp_Date');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Price']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Total']=$Total[$i];
                $uu['Exp_Date']=$Exp_Date[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['V1']=$VOne[$i]; 
                $uu['V2']=$VTwo[$i]; 
                $uu['Unit']=$Unit[$i];
                $uu['Request']=$ID;

               ProductsManufacturingRequest::create($uu); 
   
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='طلب تصنيع';
           $dataUser['ScreenEn']='Manufacturing Request';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));

        if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('ManufacturingRequestPrint/'.$ID); } 
            
        
    }
    
         public function ManufacturingRequestPrint($id){

            
           $item=ManufacturingRequest::find($id);
          
         return view('admin.Manufacturing.ManufacturingRequestPrint',[
             'item'=>$item,

         ]);
    }
    
      public function Manufacturing_Request_Sechdule(){

    $items=ManufacturingRequest::orderBy('id','desc')->paginate(100);

         return view('admin.Manufacturing.ManufacturingRequestSechdule',[
             'items'=>$items,
         ]);
    }
    
     public function ApproveManufacturingRequest($id){
                      
         ManufacturingRequest::where('id',$id)->update(['Status'=>1]);
       
        session()->flash('error',trans('admin.Approved'));
        return back();

           }
    
       public function EndManufacturingRequest($id){
                      
         ManufacturingRequest::where('id',$id)->update(['Status'=>2]);
       
        session()->flash('error',trans('admin.Finshed'));
        return back();

           }
    
     public function DeleteManufacturingRequest($id){
                      
         $del=ManufacturingRequest::find($id);
           Event::where('Type_Code',$del->Code)->where('Type','طلب تصنيع')->delete();
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='طلب تصنيع';
           $dataUser['ScreenEn']='Manufacturing Request';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
      public function EditManufacturingRequest(){

          $id=request('ID');
    $item=ManufacturingRequest::find($id);
        $Prods=ProductsManufacturingRequest::where('Request',$id)->get();   


           $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
           
        $Employess = Employess::
             where('Emp_Type','Saller')
             ->orWhere('Emp_Type','Buyer')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
         
                if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
           
                     if($item->Status != 0){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Manufacturing_Request_Sechdule');
    
                    }
           
         return view('admin.Manufacturing.EditManufacturingRequest',[
             'item'=>$item,
             'Clients'=>$Clients,
             'Employess'=>$Employess,
             'Stores'=>$Stores,
             'Prods'=>$Prods,
         ]);
    }

        public function PostEditManufacturingRequest(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Payment_Method'=>'required',
             'Client'=>'required',
             'Store'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),           
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Client.required' => trans('admin.ClientRequired'),      

         ]);

              $ID =request('ID');

            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Recived_Date'] = request('Recived_Date');
            $data['Client_Phone'] = request('Client_Phone');
            $data['Client_Address'] = request('Client_Address');
            $data['Payment_Method'] = request('Payment_Method');
            $data['Note'] = request('Note');
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Qty'] = request('Total_Qty');
            $data['Total_Discount'] = request('Total_Discount');
            $data['Total_BF_Taxes'] = request('Total_BF_Taxes');
            $data['Total_Taxes'] = request('Total_Taxes');
            $data['Total_Price'] = request('Total_Price');
            $data['Pay'] = null;
            $data['Status'] = 0;
            $data['Client'] = request('Client');
            $data['Delegate'] = request('Delegate');
            $data['Delegate_Phone'] = request('Delegate_Phone');
            $data['Store'] = request('Store');
            $data['Later_Due'] = request('Later_Due');
            $data['User'] = auth()->guard('admin')->user()->id;

            ManufacturingRequest::where('id',$ID)->update($data);
            
                 if(!empty(request('Recived_Date'))){
             $del=ManufacturingOrder::find($id);
  Event::where('Type_Code',$del->Code)->where('Type','طلب تصنيع')->delete();
    
                 $event['Start_Date']=request('Recived_Date');
         $event['End_Date']=request('Recived_Date');
         $event['Event_Ar_Name']='تسليم طلب تصنيع';
         $event['Event_En_Name']='Recived Manufacturing Request';
         $event['Type']='طلب تصنيع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Delegate');
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
        }

          if(!empty(request('Unit'))){
            ProductsManufacturingRequest::where('Request',$ID)->delete();
                
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $PurchTax=request('PurchTax');
              $Total=request('Total');
              $StorePurch=request('StorePurch');
              $Exp_Date=request('Exp_Date');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $Exp_Date=request('Exp_Date');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Price']=$Price[$i];
                $uu['Discount']=$Discount[$i];
                $uu['Tax']=$PurchTax[$i];
                $uu['Total_Bf_Tax']=$TotalBFTax[$i];
                $uu['Total_Tax']=$TotalTax[$i];
                $uu['Total']=$Total[$i];
                $uu['Exp_Date']=$Exp_Date[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['V1']=$VOne[$i]; 
                $uu['V2']=$VTwo[$i]; 
                $uu['Unit']=$Unit[$i];
                $uu['Request']=$ID;

               ProductsManufacturingRequest::create($uu); 
   
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='طلب تصنيع';
           $dataUser['ScreenEn']='Manufacturing Request';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));

     return   redirect('Manufacturing_Request_Sechdule');  
            
        
    }
    
     public function ManufacturingRequestTransferToOrder(){
         $id=request('ID');
         $item=ManufacturingRequest::find($id);
         
          $res=ManufacturingOrder::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               $month = date("m",strtotime($res->Date));
               if($month == date('m')){
                   
                 $Code=$res->Code + 1 ; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;   
                   
               }else{
               
                    $Code= 1; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;      
                   
               }
 
           }else{
                $Year=date('Y');
               $Month=date('m');
              $Code=1; 
                 $NewCode=$Year.$Month.'0'.$Code;   
           }

                      $Models=ManufacturingModel::all(); 
         
              $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
           
        $Employess = Employess::
             where('Emp_Type','Saller')
             ->orWhere('Emp_Type','Buyer')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
         
                       if($item->Status != 1){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Manufacturing_Request_Sechdule');
    
                    }
           
         
         return view('admin.Manufacturing.TransferManufacturingRequestToOrder',[
             
             'Code'=>$Code,
             'NewCode'=>$NewCode,
             'Models'=>$Models,
             'Clients'=>$Clients,
             'Employess'=>$Employess,
             'item'=>$item,
             
         ]);
    }

      public function AddTransferManufacturingOrder(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Model'=>'required',
             'Name_Outcome'=>'required',
             'Except_Qty'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
         ]);

         
           $ID = DB::table('manufacturing_orders')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'NewCode' => request('NewCode'),
            'Date' => request('Date'),
            'Model' => request('Model'),
            'Name_Outcome' => request('Name_Outcome'),
            'Except_Qty' => request('Except_Qty'),
            'Total_Required_Qty' => request('Total_Required_Qty'),
            'Status' => 0,
            'For_Client' => request('For_Client'),
            'Client' => request('Client'),
            'Client_Phone' => request('Client_Phone'),
            'Client_Address' => request('Client_Address'),
            'Delegate' => request('Delegate'),
            'Delegate_Phone' => request('Delegate_Phone'),
            'Recived_Date' => request('Recived_Date'),
            'Manufacture_Request_Code' => request('Manufacture_Request_Code'),
            'Recipient' => null,
     

        )
    );  
        
    
          if(!empty(request('Unit'))){
            
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $Store_Qty=request('Store_Qty');
              $Required_Qty=request('Required_Qty');
              $Qty=request('Qty');
              $Precent=request('Precent');
              $Store=request('Store');
              $Product=request('Product');
              $V1=request('V1');
              $V2=request('V2');
              $Unit=request('Unit');


            for($i=0 ; $i < count($Unit) ; $i++){


                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Store_Qty']=$Store_Qty[$i];
                $uu['Required_Qty']=$Required_Qty[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Precent']=$Precent[$i];
                $uu['Store']=$Store[$i];
                $uu['Product']=$Product[$i];
                $uu['V1']=$V1[$i];
                $uu['V2']=$V2[$i];
                $uu['Unit']=$Unit[$i];
                $uu['ManuOrder']=$ID;

               ProductsManufacturingOrder::create($uu); 
                
                 
            }  

              
          }

     
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
       $dataUser['Screen']='آمر تصنيع';
           $dataUser['ScreenEn']='Manufacturing Order';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
                         return redirect('ManufacturingOrderSechdule');
            
        
    }
    
    
    
    //======  ExchangeManufacturingGoodsSechdule ===
 
     public function ExchangeManufacturingGoodsSechdule(){

    $items=ManufacturingOrder::whereIn('Status',[1])->paginate(100);

         return view('admin.Manufacturing.ExchangeManufacturingGoodsSechdule',[
             'items'=>$items,


         ]);
    }
    
      public function SureExchangeGoodsManufacturingOrder(){
                   
        $id=request('ID'); 
          $CODE=ManufacturingOrder::find($id);
          
         ManufacturingOrder::where('id',$id)->update(['Status'=>2]);
       
              $Prods=ProductsManufacturingOrder::where('ManuOrder',$id)->get();
          
          foreach($Prods as $pro){

   
              
               $Quantity =ProductsQty::
                where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('P_Code',$pro->Product_Code)    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                 where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PP_Code',$pro->Product_Code)     
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                   where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPP_Code',$pro->Product_Code)     
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
              where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPPP_Code',$pro->Product_Code)    
                ->first(); 

}
}
}

              
              
                    if(!empty($Quantity)){
                        
        $unit=ProductUnits::where('Unit',$pro->Unit)->where('Product',$pro->Product)->first();  
                
           $qq= $unit->Rate * $pro->Required_Qty ;
                
           $newqty=$Quantity->Qty -  $qq ; 
                
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                  
               $plow=ProductUnits::where('Product',$pro->Product)->where('Rate',1)->first();           
 $purchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total_Bf_Tax');     
  $countPurchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
                        
                        
       $storesTransfer=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('SmallTrans_Qty');                  
                
               $OUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;                          
                        
                     if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                        }else{
                             $ty= $Collect /  1 ;    
                            
                        }
                
                if($ty != 0){
                   $in=0;
         $out=$qq * $ty ;     
         $current=$newqty * $ty ;  
                }else{
                  
             $in=0;
         $out=$qq * 1;     
         $current=$newqty * 1;        
                    
                }
                        
                        
                        
       
                        
                                  
                                $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){ 
    
    
       $totCost=0;
         
            
                

         
         $rr = ProductUnits::where("Unit",$pro->Unit)->where('Product',$pro->Product)->first();
  

            $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('P_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)      
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)      
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)       
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPPP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)       
                ->first(); 

}

}

}
    
               if(!empty($fifo)){
                   
                   
            $Qeqo=$pro->Required_Qty ;      
                   
                        if($fifo->Qty == 0){
                       
               
                       
         $NNQuntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
      
                    if($NNQuntatity == 0){
                           
               
         $NNQuntatity=$this->FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
     
                        
                    }else{
                        
                       
                        
                            if($NNQuntatity >= $Qeqo){
                    
                    $totCost += $fifo->Cost_Price * $Qeqo;
                    
                }else{
                
                $res=$Qeqo - $NNQuntatity ;       
                    
                    
                $totCost += $fifo->Cost_Price * $NNQuntatity ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }          
                        
                        
                    }        
                            
                            
                       
                   }else{    
                            
                            
                if($fifo->Qty >= $Qeqo){
                    
                    $totCost += $fifo->Cost_Price * $Qeqo ;
                    
                }else{
                
                $res=$Qeqo - $fifo->Qty ;       
                    
                    
                $totCost += $fifo->Cost_Price * $fifo->Qty ;        
                 
            $ResdiualCost=$this->MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);          
                
                    $totCost +=$ResdiualCost;
                    
                }            
                            
                       
                            
                   }
                   
                   
               }

    
                            $newQQty=$unit->Rate * $pro->Required_Qty;
      $ty=$this->AverageCostTwo($pro->Store_Qty,$newQQty,$pro->Product,$plow->Barcode,$pro->Store);    
              
                   $CostTotalSale=$totCost;

           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$pro->Product)->where('Store',$pro->Store)->first();
    
         $inn=0;
         $out=$CostTotalSale;  
            $current=($lastOperation->Current - $newQQty) *  $ty;      



         
    
    
}else{
  
    
    
                            $newQQty=$unit->Rate * $pro->Required_Qty;
      $ty=$this->AverageCostTwo($pro->Store_Qty,$newQQty,$pro->Product,$plow->Barcode,$pro->Store);    
              
                $CostTotalSale=$ty * $newQQty;

           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$pro->Product)->where('Store',$pro->Store)->first();
    
         $inn=0;
         $out=$CostTotalSale;  
            $current=($lastOperation->Current - $newQQty) *  $ty;      





}
    
              
                                 
                        
                        
                        
                        
 
              $prooooo=Products::find($pro->Product);     
          $move['Date']=date('Y-m-d');
          $move['Type']='تصنيع';
          $move['TypeEn']='Manufacturing';
          $move['Bill_Num']=$CODE->Code;
          $move['Incom']=0;
          $move['Outcom']=$qq;
          $move['Current']=$newqty;
         $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$pro->P_Ar_Name;
          $move['P_En_Name']=$pro->P_En_Name;
          $move['P_Code']=$pro->Product_Code;
          $move['Unit']=$pro->Unit;
          $move['Group']=$prooooo->Group;
          $move['Store']=$pro->Store;
          $move['Product']=$pro->Product; 
          $move['V1']=$pro->V1;  
          $move['V2']=$pro->V2;   
          $move['User']=auth()->guard('admin')->user()->id;
                         $Sro=Stores::find($pro->Store);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;  
              ProductMoves::create($move);         
                        
                        
                        
                        
                        
                        
                        
   
                    }
              
              

                            
                                //Fifo
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){       
    
           $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('P_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)      
                ->first();      
            
                  if(empty($fifo)){
                      

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)      
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)       
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc') 
                ->where('Store',$pro->Store)    
                ->where('Product',$pro->Product)    
                ->where('PPPP_Code',$pro->Product_Code)    
                 ->where('Qty','!=',0)       
                ->first(); 

}

}

}
    

    if(!empty($fifo)){
        
        $Qeqo=$pro->Required_Qty;
        if($fifo->Qty >= $Qeqo){
            
     
            
                   $unit=ProductUnits::where('Unit',$pro->Unit)->where('Product',$pro->Product)->first();  
                
           $qq= $unit->Rate * $Qeqo ;
                
           $newqty=$fifo->Qty -  $qq ; 
            
        
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
        
     
            
        }else{
            
            
        $resdiualQty=$Qeqo - $fifo->Qty ;
            
              $unit=ProductUnits::where('Unit',$pro->Unit)->where('Product',$pro->Product)->first();  
                
           $qq= $unit->Rate * $fifo->Qty ;
                
           $newqty=$fifo->Qty -  $qq ; 
            
        
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);   
            

        
     $ResdiualCost=$this->FifoStoreQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$resdiualQty,$pro->Unit);          
            
            
            
        }
        
    
    }
    
    
    
    
    }
                  
       
              
              
          }

       $Incom=IncomManufacturingModel::where('Model',$CODE->Model)->get()->sum('Total');
          $result= $Incom * $CODE->Except_Qty;
                    
          
                 $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
                  'Type' => 'التصنيع',             'TypeEn' => 'Manufacturing',
            'Code_Type' => $CODE->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $CODE->Model()->first()->Draw,
            'Coin' =>$CODE->Model()->first()->Coin,
            'Cost_Center' =>null,
            'Total_Debaitor' => $result,
            'Total_Creditor' => $result,
            'Note' => null,
  
        )
    );
           
           $In=IncomManufacturingModel::orderBy('id','desc')->where('Model',$CODE->Model)->first();
            $st=Stores::find($pro->Store);

          
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$result;
        $PRODUCTSS['Account']=$st->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CODE->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$result;
        $Gen['Statement']=null;
        $Gen['Draw']=$CODE->Model()->first()->Draw;
        $Gen['Debitor_Coin']= $CODE->Model()->first()->Draw * 0;
        $Gen['Creditor_Coin']=$CODE->Model()->first()->Draw * $result;
        $Gen['Account']=$st->Account;
        $Gen['Coin']= $CODE->Model()->first()->Coin;
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
      $Emp=Employess::find($CODE->Recipient);

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$result;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Covenant;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
       $Gen['Code_Type']=$CODE->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=$result;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$CODE->Model()->first()->Draw;
        $Gen['Debitor_Coin']= $CODE->Model()->first()->Draw * $result;
        $Gen['Creditor_Coin']=$CODE->Model()->first()->Draw  * 0;
        $Gen['Account']=$Emp->Covenant;
        $Gen['Coin']= $CODE->Model()->first()->Coin;
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
              
        session()->flash('success',trans('admin.Done'));
         return redirect('ManufacturingOrderSechdule');
   

           }
    
    //ManufacturingExecution
    public function ManufacturingExecutionPage($id){

    $res=ManufacturingExecution::orderBy('id','desc')->first();
      
           if(!empty($res->Code)){
               
               
               $month = date("m",strtotime($res->Date));
               $day = date("d",strtotime($res->Date));
               if($month == date('m')){
                   
                 $Code=$res->Code + 1 ; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;   
                   
               }else{
               
                    $Code= 1; 
               $Year=date('Y');
               $Month=date('m');
              $NewCode=$Year.$Month.'0'.$Code;      
                   
               }

               if($day == date('d')){
                   
          $COUNT=ManufacturingExecution::where('Date',date('Y-m-d'))->count();       
                   $Recipe=$COUNT+1;
               }else{
                   
                  $Recipe=1; 
               }
            
           
           }else{
                          
                 $Recipe=1;
                $Year=date('Y');
               $Month=date('m');
              $Code=1; 
                 $NewCode=$Year.$Month.'0'.$Code;   
           }

             $Order=ManufacturingOrder::find($id); 
         
           $Incoms=IncomManufacturingModel::where('Model',$Order->Model)->get();
           $Outcom=OutcomManufacturingModel::where('Model',$Order->Model)->first();
  
        $Productions = Employess::
             where('Emp_Type','Production_Manager')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();
         
                   $Qualites = Employess::
             where('Emp_Type','ObserverQuality')
              ->get();
        
        
               $item=ManufacturingOrder::find($id);
                   
                     if($item->Status != 2){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('ManufacturingOrderSechdule');
        
                    }
         
        
         
         return view('admin.Manufacturing.ManufacturingExecution',[
             'Code'=>$Code,
             'NewCode'=>$NewCode,
             'Productions'=>$Productions,
             'Qualites'=>$Qualites,
             'Order'=>$Order,
             'Incoms'=>$Incoms,
             'Outcom'=>$Outcom,
             'Recipe'=>$Recipe,

         ]);
    }
    
     public function AddManufacturingExecution(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Production_Manager'=>'required',
             'Quality_Manager'=>'required',

               ],[

         ]);

        $empP=Employess::find(request('Production_Manager'));
        $empQ=Employess::find(request('Quality_Manager'));
        $Recipe=request('Recipe');
        $year=date('Y') - 2000;
        $month=date('m');
        $day=date('d');
        $prodmanger=$empP->Code;
        $qualitymanager=$empQ->Code;
             $PATCH=$year.$month.$day.'0'.$Recipe.'0'.$prodmanger.'0'.$qualitymanager.request('Outcome_Code'); 

           $ID = DB::table('manufacturing_executions')->insertGetId(
        array(

            'Code' => request('Code'),
            'NewCode' => request('NewCode'),
            'Date' => request('Date'),
            'Manu_Order_ID' => request('Manu_Order_ID'),
            'Manu_Order_Code' => request('Manu_Order_Code'),
            'Manu_Order_Date' => request('Manu_Order_Date'),
            'Production_Manager' => request('Production_Manager'),
            'Quality_Manager' => request('Quality_Manager'),
            'Recived_Date' => request('Recived_Date'),
            'Manu_Request_Code' => request('Manu_Request_Code'),
            'Note' => request('Note'),
            'Status' => 0,
            'Outcome_Name' => request('Outcome_Name'),
            'Outcome_Code' => request('Outcome_Code'),
            'Outcome_Qty' => request('Outcome_Qty'),
            'Except_Qty' => request('Except_Qty'),
            'Outcome_Unit' => request('Outcome_Unit'),
            'Outcome_Store' => request('Outcome_Store'),
            'Patch_Number' => $PATCH,
            'Model' => request('Model'),
            'User' => auth()->guard('admin')->user()->id,
     
        )
    );  

           ManufacturingOrder::where('id',request('Manu_Order_ID'))->update(['Status'=>3]);
                
          if(!empty(request('Unit'))){
            
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Precent=request('Precent');
              $Qty=request('Qty');
              $RequiredQty=request('RequiredQty');
              $Store=request('Store');
              $Product=request('Product');
              $Unit=request('Unit');
              

            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Precent']=$Precent[$i];
                $uu['Qty']=$Qty[$i];
                $uu['RequiredQty']=$RequiredQty[$i];
                $uu['Store']=$Store[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['ManuExecution']=$ID;

               ProductManufacturingExecution::create($uu); 
   
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='تنفيذ التصنيع';
           $dataUser['ScreenEn']='Manufacturing Execution';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));

        if(request('SP') == 0){  return redirect('ManufacturingExecutionSechdule'); }elseif(request('SP') == 1){  return redirect('ManufacturingExecutionPrint/'.$ID); } 
            
        
    }
   
     public function ManufacturingExecutionPrint($id){

            
           $item=ManufacturingExecution::find($id);
          $Prods=ProductManufacturingExecution::where('ManuExecution',$id)->get();

         return view('admin.Manufacturing.ManufacturingExecutionPrint',[
             'item'=>$item,
             'Prods'=>$Prods,

         ]);
    }
    
     public function ManufacturingExecutionSechdule(){

    $items=ManufacturingExecution::orderBy('id','desc')->paginate(100);

         return view('admin.Manufacturing.ManufacturingExecutionSechdule',[
             'items'=>$items,
         ]);
    }
    
     public function DeleteManufacturingExecution($id){
                      
         $del=ManufacturingRequest::find($id);
         
           ManufacturingOrder::where('id',$del->Manu_Order_ID)->update(['Status'=>2]); 
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='تنفيذ التصنيع';
           $dataUser['ScreenEn']='Manufacturing Execution';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


             //====== Examinations Types  ======= 
    public function ExaminationsTypesPage(){
        $items=ExaminationsTypes::all();
        $Units=Measuerments::all();
         return view('admin.Manufacturing.ExaminationsTypes',['items'=>$items,'Units'=>$Units]);
    }
    
     public function AddExaminationsTypes(){
        
        $data= $this->validate(request(),[
             'Name'=>'required',
             'Allow_From'=>'required',
             'Allow_To'=>'required',
             'Unit'=>'required',

             
               ],[
 

         ]);
   
         
           
         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['Allow_From']=request('Allow_From');
         $data['Allow_To']=request('Allow_To');
         $data['Unit']=request('Unit');
   
         ExaminationsTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='انواع الفحوصات';
           $dataUser['ScreenEn']='Examinations Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditExaminationsTypes($id){ 
         
        $data= $this->validate(request(),[
             'Name'=>'required',
             'Allow_From'=>'required',
             'Allow_To'=>'required',
             'Unit'=>'required',

             
               ],[
 

         ]);
   
         
           
         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['Allow_From']=request('Allow_From');
         $data['Allow_To']=request('Allow_To');
         $data['Unit']=request('Unit');
   

           ExaminationsTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='انواع الفحوصات';
           $dataUser['ScreenEn']='Examinations Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteExaminationsTypes($id){
                      
        $del=ExaminationsTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='انواع الفحوصات';
           $dataUser['ScreenEn']='Examinations Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
   

// ===  Qualites =====
    
      public function ManufacturingExecutionQuality(){
            $id=request('ID');
            $Exmines=ExaminationsTypes::all();
            $Exe=ManufacturingExecution::find($id);
            
            $res=Quality::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){

              $Code=$res->Code + 1  ;
          
           }else{
               
              $Code=1; 
        
               
           }

           $item=ManufacturingExecution::find($id);
                     if($item->Status != 0){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('ManufacturingExecutionSechdule');
        
                    }
         return view('admin.Manufacturing.ManufacturingExecutionQuality',[
             'Code'=>$Code,
             'Exmines'=>$Exmines,
             'Exe'=>$Exe,
  

         ]);
    }
    
      public function ExmineFilter($id){

$states=[];
          
$X=ExaminationsTypes::find($id);

$states +=['Name'=>$X->Name,'Unit'=>$X->Unit,'UnitName'=>$X->Unit()->first()->Name,'ALlowF'=>$X->Allow_From,'ALlowT'=>$X->Allow_To];
           return response()->json($states);
        
    }
 
      public function AddQuality(){

           $ID = DB::table('qualities')->insertGetId(
        array(

            'Code' => request('Code'),
            'Execution_Code' => request('Execution_Code'),
            'Date' => request('Date'),
            'Outcome_Name' => request('Outcome_Name'),
            'Outcome_Code' => request('Outcome_Code'),
            'Outcome_Qty' => request('Outcome_Qty'),
            'Except_Qty' => request('Except_Qty'),
            'Outcome_Unit' => request('Outcome_Unit'),
            'Outcome_Store' => request('Outcome_Store'),
            'Production_Manager' => request('Production_Manager'),
            'Quality_Manager' => request('Quality_Manager'),
            'ManuExecution' => request('ManuExecution'),
            'Note' => request('Note'),
            'Status' => 0,
            'User' => auth()->guard('admin')->user()->id,
     
        )
    );  
     ManufacturingExecution::where('id',request('ManuExecution'))->update(['Status'=>1]); 
          if(!empty(request('Exmine_Unit'))){
            
              $Exmine_Type=request('Exmine_Type');
              $Exmine_Unit=request('Exmine_Unit');
              $Exmine_Allow_From=request('Exmine_Allow_From');
              $Exmine_Allow_To=request('Exmine_Allow_To');
              $Result=request('Result');
              $Accept=request('Accept');
              $Exmine=request('Exmine');
              $Quality=request('Quality');


            for($i=0 ; $i < count($Exmine_Unit) ; $i++){

                $uu['Exmine_Type']=$Exmine_Type[$i];
                $uu['Exmine_Unit']=$Exmine_Unit[$i];
                $uu['Exmine_Allow_From']=$Exmine_Allow_From[$i];
                $uu['Exmine_Allow_To']=$Exmine_Allow_To[$i];
                $uu['Result']=$Result[$i];
                $uu['Accept']=$Accept[$i];
                $uu['Exmine']=$Exmine[$i];
                $uu['Quality']=$ID;

               QualityDetails::create($uu); 
   
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='جوده تصنيع';
           $dataUser['ScreenEn']='Quality Manfacturing';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));

        if(request('SP') == 0){  return redirect('QualitySechdule'); }elseif(request('SP') == 1){  return redirect('QualityPrint/'.$ID); } 
            
        
    }

      public function QualityPrintPage($id){

            
           $item=Quality::find($id);
          $Prods=QualityDetails::where('Quality',$id)->get();

         return view('admin.Manufacturing.QualityPrint',[
             'item'=>$item,
             'Prods'=>$Prods,

         ]);
    }
    
      public function QualitySechdule(){

    $items=Quality::orderBy('id','desc')->paginate(100);

         return view('admin.Manufacturing.QualitySechdule',[
             'items'=>$items,
         ]);
    }
    
      public function DeleteQuality($id){
                      
         $del=Quality::find($id);
         

           ManufacturingExecution::where('id',$del->ManuExecution)->update(['Status'=>0]); 
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='جوده تصنيع';
           $dataUser['ScreenEn']='Quality Manfacturing';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
        
      public function QualityDone($id){
                   

          $Quality=Quality::find($id);
          
         Quality::where('id',$id)->update(['Status'=>1]);
       
          
       $Exe=ManufacturingExecution::find($Quality->ManuExecution);

      $Out=OutcomManufacturingModel::orderBy('id','desc')->where('Model',$Exe->Model)->first();    

 
          
           $Quantity =ProductsQty::
                where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('P_Code',$Out->Product_Code)    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
              where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PP_Code',$Out->Product_Code)      
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
               where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PPP_Code',$Out->Product_Code)      
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PPPP_Code',$Out->Product_Code)     
                ->first(); 

}
}
}

          
                  
                    if(!empty($Quantity)){
                        
        $unit=ProductUnits::where('Unit',$Out->Unit)->where('Product',$Out->Product)->first();  
                
           $qq= $unit->Rate * $Exe->Except_Qty ;
                
           $newqty=$Quantity->Qty + $qq ; 
                
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                  
                        $plow=ProductUnits::where('Product',$Out->Product)->where('Rate',1)->first();   
 $purchs=ProductsPurchases::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('Total_Bf_Tax');     
  $countPurchs=ProductsPurchases::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('SmallQty');
                        
           $storesTransfer=ProductsStoresTransfers::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$Out->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$Out->Store)->get()->sum('SmallTrans_Qty');                  
                
               $OUTCOME=OutcomManufacturingModel::where('Product',$Out->Product)->where('Store',$Out->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Out->Product)->where('Store',$Out->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');    
                        
                 $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;            
                        
                 
                               if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }
                        
      
                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }
                        
             

                                        $newQQty= $unit->Rate * $Exe->Except_Qty ; 
            $ty=$this->AverageCost($Out->Cost,$newQQty,$Out->Product,$plow->Barcode,$Out->Store,date('Y-m-d'),$plow->Price);    
                
        
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$Out->Product)->where('Store',$Out->Store)->first();
           
  
                
         $in=$Out->Cost;
                        
                        
                        
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $Out->Cost;  
                }else{
            $current= $Out->Cost;            
                }


                        
 
              $prooooo=Products::find($Out->Product);     
          $move['Date']=date('Y-m-d');
          $move['Type']='تصنيع';
          $move['TypeEn']='Manufacturing';
          $move['Bill_Num']=$Quality->Code;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
         $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$Out->P_Ar_Name;
          $move['P_En_Name']=$Out->P_En_Name;
          $move['P_Code']=$Out->Product_Code;
          $move['Unit']=$Out->Unit;
          $move['Group']=$prooooo->Group;
          $move['Store']=$Out->Store;
          $move['Product']=$Out->Product; 
          $move['V1']=$Out->V1;  
          $move['V2']=$Out->V2;   
          $move['User']=auth()->guard('admin')->user()->id;
          $Sro=Stores::find($Out->Store);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;                  
              ProductMoves::create($move);                    
   
                    }else{
                        
           $pp=ProductUnits::where('Product',$Out->Product)->where('Unit',$Out->Unit)->first();      
                  $plow=ProductUnits::where('Product',$Out->Product)->where('Rate',1)->first();         
        $id_store = DB::table('products_stores')->insertGetId(
            
        array(
            
            'P_Ar_Name' => $Out->P_Ar_Name,
            'P_En_Name' => $Out->P_En_Name,
            'P_Code' =>   $Out->Product_Code,
            'Exp_Date' => null,
            'Product' => $Out->Product,
            'Store' =>$Out->Store,
            'V1' => $Out->V1,
            'V2' => $Out->V2,        
            'V_Name' => $Out->V_Name,        
            'VV_Name' => $Out->VV_Name,        

        )
    );          
                

                    $pqty['P_Ar_Name']=$Out->P_Ar_Name;
                    $pqty['P_En_Name']=$Out->P_En_Name;
                    $pqty['Qty']=$Exe->Except_Qty * $pp->Rate;
                    $pqty['Price']=0;
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$Out->Store;
                    $pqty['Unit']=$Out->Unit;
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Out->Product;
                    $pqty['V1']=$Out->V1;
                    $pqty['V2']=$Out->V2;
                    $pqty['V_Name']=$Out->V_Name;
                    $pqty['VV_Name']=$Out->VV_Name;
                           $prooooo=Products::find($Out->Product); 
        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
                        
                     $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Out->Product)->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                        $pqty['P_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }    
      
              ProductsQty::create($pqty);   
           
                        
               $plow=ProductUnits::where('Product',$Out->Product)->where('Rate',1)->first();   
 $purchs=ProductsPurchases::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('Total_Bf_Tax');     
  $countPurchs=ProductsPurchases::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('SmallQty');
                
    $purchsStart=ProductsStartPeriods::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('Store',$Out->Store)->get()->sum('SmallQty');
                        
           $storesTransfer=ProductsStoresTransfers::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$Out->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Out->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$Out->Store)->get()->sum('SmallTrans_Qty');                  
                
               $OUTCOME=OutcomManufacturingModel::where('Product',$Out->Product)->where('Store',$Out->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Out->Product)->where('Store',$Out->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');    
                        
                 $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;            
                        
                 
                               if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }
                                    
                        
                if($ty != 0){
                   $in=($Exe->Except_Qty * $pp->Rate) * $ty ;
         $out=0;     
         $current=($Exe->Except_Qty * $pp->Rate) * $ty;  
                }else{
                  
             $in=($Exe->Except_Qty * $pp->Rate) * 1;
         $out=0;     
         $current=($Exe->Except_Qty * $pp->Rate) * 1;        
                    
                }
                        
                        
                        
             $newQQty= $unit->Rate * $Exe->Except_Qty ; 
            $ty=$this->AverageCost($Out->Cost,$newQQty,$Out->Product,$plow->Barcode,$Out->Store,date('Y-m-d'),$plow->Price);    
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$Out->Product)->where('Store',$Out->Store)->first();      
         $in=$Out->Cost;          
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $Out->Cost;  
                }else{
            $current= $Out->Cost;            
                }                
                        
 
              $prooooo=Products::find($Out->Product);     
          $move['Date']=date('Y-m-d');
          $move['Type']='تصنيع';
          $move['TypeEn']='Manufacturing';
          $move['Bill_Num']=request('Code');
          $move['Incom']=$Exe->Except_Qty * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=$Exe->Except_Qty * $pp->Rate;
         $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$Out->P_Ar_Name;
          $move['P_En_Name']=$Out->P_En_Name;
          $move['P_Code']=$Out->Product_Code;
          $move['Unit']=$Out->Unit;
          $move['Group']=$prooooo->Group;
          $move['Store']=$Out->Store;
          $move['Product']=$Out->Product; 
          $move['V1']=$Out->V1;  
          $move['V2']=$Out->V2;   
          $move['User']=auth()->guard('admin')->user()->id;
              $Sro=Stores::find($Out->Store);
                
                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;                           
              ProductMoves::create($move);                                     
                        
                        
                        
                    }
          
          



                            //Fifo
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){              
                     $fifo =FifoQty::
                where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('P_Code',$Out->Product_Code)    
                ->where('Purchases_Date',date('Y-m-d'))    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                   where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PP_Code',$Out->Product_Code)    
                ->where('Purchases_Date',date('Y-m-d'))    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
                   where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PPP_Code',$Out->Product_Code)    
                ->where('Purchases_Date',date('Y-m-d'))    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
               where('Store',$Out->Store)    
                ->where('Product',$Out->Product)    
                ->where('PPPP_Code',$Out->Product_Code)    
                ->where('Purchases_Date',date('Y-m-d'))    
                ->first(); 

}

}

}
    
    

    if(!empty($fifo)){
        
           $unit=ProductUnits::where('Unit',$Out->Unit)->where('Product',$Out->Product)->first();  
                
           $qq= $unit->Rate * $Exe->Except_Qty ;
                
           $newqty=$fifo->Qty +  $qq ; 
       
               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty,'Original_Qty'=>$newqty]);   
        
    }else{
        
 


      $pqty['P_Ar_Name']=$Out->P_Ar_Name;
                 $pqty['Exp_Date']=null;
                    $pqty['P_En_Name']=$Out->P_En_Name;
                    $pqty['Qty']=$Exe->Except_Qty * $pp->Rate;
                    $pqty['Original_Qty']=$Exe->Except_Qty * $pp->Rate;
                    $pqty['Cost_Price']=0;
                    $pqty['Store']=$Out->Store;
                    $pqty['Unit']=$Out->Unit;
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Out->Product;
              
           $prooooo=Products::find($Out->Product); 
                        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
         
              $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;    
                    $pqty['P_Code']=$Out->Product_Code;  
                
  $proooooStore=Stores::find($Out->Store);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;
                    $pqty['Purchases_Date']=date('Y-m-d');
              FifoQty::create($pqty);  
        
        

        
    }
    
    
    
    
    }
                  
          
          
          
          
          
              

             $CODE=ManufacturingOrder::find($Exe->Manu_Order_ID);
    $Incom=IncomManufacturingModel::where('Model',$CODE->Model)->get()->sum('Total');
          $result= $Incom * $CODE->Except_Qty;
                    
          
                 $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
                  'Type' => 'التصنيع',             'TypeEn' => 'Manufacturing',
            'Code_Type' => $CODE->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $CODE->Model()->first()->Draw,
            'Coin' =>$CODE->Model()->first()->Coin,
            'Cost_Center' =>null,
            'Total_Debaitor' => $result,
            'Total_Creditor' => $result,
            'Note' => null,
  
        )
    );
           
           $In=OutcomManufacturingModel::orderBy('id','desc')->where('Model',$CODE->Model)->first();
            $st=Stores::find($In->Store);

          
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$result;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$st->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CODE->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=$result;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$CODE->Model()->first()->Draw;
        $Gen['Debitor_Coin']= $CODE->Model()->first()->Draw * $result;
        $Gen['Creditor_Coin']=$CODE->Model()->first()->Draw * 0;
        $Gen['Account']=$st->Account;
        $Gen['Coin']= $CODE->Model()->first()->Coin;
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
      $Emp=Employess::find($CODE->Recipient);

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$result;
        $PRODUCTSS['Account']=$Emp->Covenant;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
       $Gen['Code_Type']=$CODE->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='التصنيع';         $Gen['TypeEn']='Manufacturing';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$result;
        $Gen['Statement']=null;
        $Gen['Draw']=$CODE->Model()->first()->Draw;
        $Gen['Debitor_Coin']= $CODE->Model()->first()->Draw * 0;
        $Gen['Creditor_Coin']=$CODE->Model()->first()->Draw  * $result;
        $Gen['Account']=$Emp->Covenant;
        $Gen['Coin']= $CODE->Model()->first()->Coin;
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen); 
          
          
      
        session()->flash('success',trans('admin.Done'));
         return back();
      



           }
    
    
  

                  
    
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;

class AttendImport implements ToCollection, WithChunkReading , WithBatchInserts
{

    public function collection(Collection $collection)
    {
        
          DB::table('attend_departure_import')->truncate();
        
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
            DB::table('attend_departure_import')->insert([

            'In_Time'	   =>$value[1]
            ,'Out_Time'  =>$value[2]
            ,'Date'  =>$value[3]
            ,'Month'  =>$value[4]
            ,'Note'  =>$value[5]
            ,'Attend'  =>$value[6]
            ,'Emp'  =>$value[7]
            ,'created_at'  =>$value[8]
            ,'updated_at'  =>$value[9]    

          

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

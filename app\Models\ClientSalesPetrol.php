<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientSalesPetrol extends Model
{
    use HasFactory;
      protected $table = 'client_sales_petrols';
      protected $fillable = [
        'Customer_Amount',
        'Customer',       
        'SalesPetrol',       
    ];


            public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }
    
             public function SalesPetrol()
    {
        return $this->belongsTo(SalesPetrol::class,'SalesPetrol');
    }
}

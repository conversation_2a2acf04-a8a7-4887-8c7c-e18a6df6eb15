<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDefaultDataShowHidesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('default_data_show_hides', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Status')->nullable();
            $table->string('Shipping_Company')->nullable();
            $table->string('Vendor_Date')->nullable();
            $table->string('Expire_Date')->nullable();
            $table->string('Total_BF_Taxes')->nullable();
            $table->string('Total_Taxes')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Delegate_Sale')->nullable();
            $table->string('Delegate_Purchase')->nullable();
            $table->string('Note')->nullable();
            $table->string('Refrence_Number')->nullable();
            $table->timestamps();
            $table->string('Cost_Center')->nullable();
            $table->string('Branch')->nullable();
            $table->string('Serial_Num')->nullable();
            $table->string('Pass')->nullable();
            $table->string('Pattern_Image')->nullable();
            $table->string('Barcode_Print')->nullable();
            $table->string('Unit_Print')->nullable();
            $table->string('Total_BF_Print')->nullable();
            $table->string('Discount_Print')->nullable();
            $table->string('Tax_Print')->nullable();
            $table->string('A5')->nullable();
            $table->string('A4')->nullable();
            $table->string('CM8')->nullable();
            $table->string('Group_Brand')->nullable();
            $table->string('Patch_Number')->nullable();
            $table->string('Manufacturing_Model_Shortcomings')->nullable();
            $table->string('Search_Typical')->nullable();
            $table->string('Validity_Product')->nullable();
            $table->string('Executor_Sale')->nullable();
            $table->string('Totuch_Screen')->nullable();
            $table->string('Tax_POS')->nullable();
            $table->string('TotalDiscountPrint')->nullable();
            $table->string('TotalTaxPrint')->nullable();
            $table->string('ProductsNumber')->nullable();
            $table->string('TotalQtyPrint')->nullable();
            $table->string('Credit')->nullable();
            $table->string('Barcode')->nullable();
            $table->string('Taknet')->nullable();
            $table->string('Address')->nullable();
            $table->string('Phone1')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Phone3')->nullable();
            $table->string('Phone4')->nullable();
            $table->string('Text')->nullable();
            $table->string('Seal')->nullable();
            $table->string('Code_Report')->nullable();
            $table->string('Unit')->nullable();
            $table->string('Refrence_Number_Print')->nullable();
            $table->string('Icon_Payment_Recipt')->nullable();
            $table->string('SearchCode')->nullable();
            $table->string('TaxOnTotal')->nullable();
            $table->string('TotalBfTax')->nullable();
            $table->string('AvQty')->nullable();
            $table->string('Disc')->nullable();
            $table->string('Tax')->nullable();
            $table->string('Store')->nullable();
            $table->string('TaxBill')->nullable();
            $table->string('Change_Way_Stores_Transfer')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('default_data_show_hides');
    }
}
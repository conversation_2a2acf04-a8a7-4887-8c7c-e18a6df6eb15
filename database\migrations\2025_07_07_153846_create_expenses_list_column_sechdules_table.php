<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExpensesListColumnSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('expenses_list_column_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date')->nullable();
            $table->string('Code_Type')->nullable();
            $table->string('Statement')->nullable();
            $table->string('Debitor')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Coin')->nullable();
            $table->string('User')->nullable();
            $table->string('Account')->nullable();
            $table->string('Branch')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('expenses_list_column_sechdules');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingExecution extends Model
{
    use HasFactory;
                 protected $table = 'manufacturing_executions';
      protected $fillable = [
       'Code',
       'NewCode',
       'Date',  
       'Manu_Order_ID',
       'Manu_Order_Code',
       'Manu_Order_Date',
       'Production_Manager',
       'Quality_Manager',
       'Recived_Date',
       'Manu_Request_Code',
       'Note',
       'Status',
       'Outcome_Name',
       'Outcome_Code',
       'Outcome_Qty',
       'Except_Qty',
       'Outcome_Unit',
       'Outcome_Store',
       'Patch_Number',
       'Model',
       'User',
                 
   
    ];
    
        public function Model()
    {
        return $this->belongsTo(ManufacturingModel::class,'Model');
    }
    
    
          public function Manu_Order_ID()
    {
        return $this->belongsTo(ManufacturingOrder::class,'Manu_Order_ID');
    }
    
              public function Production_Manager()
    {
        return $this->belongsTo(Employess::class,'Production_Manager');
    }
    
    
        public function Quality_Manager()
    {
        return $this->belongsTo(Employess::class,'Quality_Manager');
    }
    
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

             public function Outcome_Store()
    {
        return $this->belongsTo(Stores::class,'Outcome_Store');
    }
         public function Outcome_Unit()
    {
        return $this->belongsTo(Measuerments::class,'Outcome_Unit');
    }
    
    
 
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsStoresTransfers extends Model
{
    use HasFactory;
      protected $table = 'products_stores_transfers';
      protected $fillable = [
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'P_Code',
        'Price',
        'OldPrice',
        'Av_Qty',
        'SmallCode',  
        'Trans_Qty',
        'Original_Trans_Qty',
        'SmallTrans_Qty',
        'Total',
        'ST_ID',
        'Product',
        'V1',
        'V2',
        'Unit',
        'To_Store',
        'CostPrice',
       
    ];
    
        public function ST_ID()
    {
        return $this->belongsTo(StorsTransfers::class,'ST_ID');
    }
    
    
         public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
         public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
         public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
         public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
}

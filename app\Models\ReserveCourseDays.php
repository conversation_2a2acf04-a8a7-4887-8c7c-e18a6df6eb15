<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReserveCourseDays extends Model
{
    use HasFactory;
      protected $table = 'reserve_course_days';
      protected $fillable = [
        'Day',
        'From_Time',
        'To_Time',
        'Reserve',
      
    ];

         public function Reserve()
    {
        return $this->belongsTo(ReserveCourse::class,'Reserve');
    }
 
    
}

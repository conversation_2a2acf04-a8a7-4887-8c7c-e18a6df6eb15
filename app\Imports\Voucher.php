<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class Voucher implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('vouchers')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('vouchers')->insert([

     
            'Draw'	   =>$value[1]
            ,'Coin'  =>$value[2]
            ,'Safe'  =>$value[3]
            ,'Debitor'  =>$value[4]
            ,'Creditor'  =>$value[5]
            ,'Account'  =>$value[6]
            ,'Statement'  =>$value[7]
            ,'created_at'  =>$value[8]
            ,'updated_at'  =>$value[9]
           

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}
	

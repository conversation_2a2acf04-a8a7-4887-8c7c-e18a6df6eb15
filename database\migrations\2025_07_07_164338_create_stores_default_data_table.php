<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stores_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('Group');
            $table->integer('Unit');
            $table->integer('Tax');
            $table->integer('Coin');
            $table->integer('Account_Excess');
            $table->integer('Account_Dificit');
            $table->integer('Store');
            $table->timestamps();
            $table->string('Type');
            $table->string('Style');
            $table->string('StoresTarnsferPrice');
            $table->string('Guide_Product_Cost');
            $table->string('Client_Store_Account');
            $table->string('Show_Ship')->nullable();
            $table->string('StoresTarnsferHide')->nullable();
            $table->string('CodeType')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stores_default_data');
    }
}
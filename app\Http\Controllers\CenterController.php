<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\CoursesCategory;
use App\Models\ScientificMaterial;
use App\Models\StudentGroup;
use App\Models\CoursesType;
use App\Models\SpecialCases;
use App\Models\CoursesHalls;
use App\Models\Teachers;
use App\Models\TeachersSubjects;
use App\Models\TeachersGroups;
use App\Models\Countris;
use App\Models\Governrate;
use App\Models\AcccountingManual;
use App\Models\Students;
use App\Models\Courses;
use App\Models\ReserveCourse;
use App\Models\ReserveCourseDays;
use App\Models\ReserveCourseStudents;
use App\Models\Coins;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\SafesBanks;
use App\Models\RegCourses;
use App\Models\RegCoursesStudents;
use App\Models\StudentImportant;
use DB ;
use Str ;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use SpamProtector;
use Storage;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CenterController extends Controller
{
    
    function __construct()
{

$this->middleware('permission:فئات الكورسات', ['only' => ['CoursesCategory','AddCoursesCategory','EditCoursesCategory','DeleteCoursesCategory']]);
$this->middleware('permission:المواد العلمية', ['only' => ['ScientificMaterial','AddScientificMaterial','EditScientificMaterial','DeleteScientificMaterial']]);
$this->middleware('permission:مجموعة الطلاب', ['only' => ['StudentGroup','AddStudentGroup','EditStudentGroup','DeleteStudentGroup']]);
$this->middleware('permission:انواع الكورسات', ['only' => ['CoursesType','AddCoursesType','EditCoursesType','DeleteCoursesType']]); 
$this->middleware('permission:الحالات الخاصة', ['only' => ['SpecialCases','AddSpecialCases','EditSpecialCases','DeleteSpecialCases']]);
$this->middleware('permission:القاعات', ['only' => ['CoursesHalls','AddCoursesHalls','EditCoursesHalls','DeleteCoursesHalls']]);
$this->middleware('permission:المدربين', ['only' => ['Teachers','EditTeacher','AddTeachers','PostEditTeachers','DeleteTeachers']]);
$this->middleware('permission:جدول المدربين', ['only' => ['TeachersSechdule']]);
$this->middleware('permission:الطلاب', ['only' => ['Students','EditStudent','AddStudents','PostEditStudent','DeleteStudents']]);
$this->middleware('permission:جدول الطلاب', ['only' => ['StudentsSechdule']]);
$this->middleware('permission:الكورسات', ['only' => ['Courses','AddCourses','EditCourses','DeleteCourses']]);
$this->middleware('permission:حجز كورس', ['only' => ['ReserveCourse','AddReserveCourse','DeleteReserveCourse','EditResreveCourse']]);
$this->middleware('permission:جدول حجوزات الكورسات', ['only' => ['ReserveCourseSechdule']]);
$this->middleware('permission:تسجيل حضور كورسات', ['only' => ['RegCourses','AddRegCourses','DeleteRegCourses','EditRegCourses']]);
$this->middleware('permission:جدول تسجيل حضور كورسات', ['only' => ['RegCoursesSechdule']]);



}
    
    
    //CoursesCategory
            public function CoursesCategory(){
        $items=CoursesCategory::all();
         return view('admin.Centers.CoursesCategory',['items'=>$items]);
    }
    
     public function AddCoursesCategory(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
               $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CoursesCategoryImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         CoursesCategory::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='فئات الكورسات';
           $dataUser['ScreenEn']='Courses Category';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCoursesCategory($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
          $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CoursesCategoryImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           CoursesCategory::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='فئات الكورسات';
           $dataUser['ScreenEn']='Courses Category';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCoursesCategory($id){
                      
        $del=CoursesCategory::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
        $dataUser['Screen']='فئات الكورسات';
           $dataUser['ScreenEn']='Courses Category';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    //StudentGroup
            public function StudentGroup(){
        $items=StudentGroup::all();
         return view('admin.Centers.StudentGroup',['items'=>$items]);
    }
    
     public function AddStudentGroup(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
               $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CoursesCategoryImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         StudentGroup::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='مجموعات الطلاب';
           $dataUser['ScreenEn']='Student Group';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditStudentGroup($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
          $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CoursesCategoryImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=request('Images');
             }
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           StudentGroup::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                     $dataUser['Screen']='مجموعات الطلاب';
           $dataUser['ScreenEn']='Student Group';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteStudentGroup($id){
                      
        $del=StudentGroup::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='مجموعات الطلاب';
           $dataUser['ScreenEn']='Student Group';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    

        //ScientificMaterial
            public function ScientificMaterial(){
        $items=ScientificMaterial::all();
         return view('admin.Centers.ScientificMaterial',['items'=>$items]);
    }
    
     public function AddScientificMaterial(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         ScientificMaterial::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='المواد العلمية';
           $dataUser['ScreenEn']='Scientific Material';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditScientificMaterial($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           ScientificMaterial::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='المواد العلمية';
           $dataUser['ScreenEn']='Scientific Material';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteScientificMaterial($id){
                      
        $del=ScientificMaterial::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='المواد العلمية';
           $dataUser['ScreenEn']='Scientific Material';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

        //CoursesType
            public function CoursesType(){
        $items=CoursesType::all();
         return view('admin.Centers.CoursesType',['items'=>$items]);
    }
    
     public function AddCoursesType(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         CoursesType::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='انواع الكورسات';
           $dataUser['ScreenEn']='Courses Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCoursesType($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           CoursesType::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='انواع الكورسات';
           $dataUser['ScreenEn']='Courses Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCoursesType($id){
                      
        $del=CoursesType::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='انواع الكورسات';
           $dataUser['ScreenEn']='Courses Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    


        //SpecialCases
            public function SpecialCases(){
        $items=SpecialCases::all();
         return view('admin.Centers.SpecialCases',['items'=>$items]);
    }
    
     public function AddSpecialCases(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
      'Discount'=>'required',
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Discount']=request('Discount');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         SpecialCases::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='الحالات الخاصة';
           $dataUser['ScreenEn']='Special Cases';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditSpecialCases($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Discount'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Discount']=request('Discount');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           SpecialCases::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='الحالات الخاصة';
           $dataUser['ScreenEn']='Special Cases';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteSpecialCases($id){
                      
        $del=SpecialCases::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='الحالات الخاصة';
           $dataUser['ScreenEn']='Special Cases';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    


    
        //CoursesHalls
            public function CoursesHalls(){
        $items=CoursesHalls::all();
         return view('admin.Centers.CoursesHalls',['items'=>$items]);
    }
    
     public function AddCoursesHalls(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
   
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);

       
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Chairs_Num']=request('Chairs_Num');
         $data['Wiifii']=request('Wiifii');
         $data['Air_Condition']=request('Air_Condition');
         $data['Place']=request('Place');
         $data['Number']=request('Number');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         CoursesHalls::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']='القاعات';
           $dataUser['ScreenEn']='Courses Halls';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCoursesHalls($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         
       
         $data['Arabic_Name']=request('Arabic_Name');
       $data['Chairs_Num']=request('Chairs_Num');
         $data['Wiifii']=request('Wiifii');
         $data['Air_Condition']=request('Air_Condition');
         $data['Place']=request('Place');
         $data['Number']=request('Number');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
    
           CoursesHalls::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='القاعات';
           $dataUser['ScreenEn']='Courses Halls';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCoursesHalls($id){
                      
        $del=CoursesHalls::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='القاعات';
           $dataUser['ScreenEn']='Courses Halls';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

    
    
    //Teachers
         public function Teachers(){
             $Countris=Countris::all();
             $Governrates=Governrate::all();
             $ScientificMaterial=ScientificMaterial::all();
             $StudentGroup=StudentGroup::all();
               $res=Teachers::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
             
            
                  $wf=60;
 $Accounts=AcccountingManual::orderBy('Code','asc')->where('Code','like', $wf.'%')->where('Type',0)->get(); 
             
         return view('admin.Centers.Teachers',[
             'Countris'=>$Countris,
             'Governrates'=>$Governrates,
             'Code'=>$Code,
             'Accounts'=>$Accounts,
             'ScientificMaterial'=>$ScientificMaterial,
             'StudentGroup'=>$StudentGroup,
         
         ]);
    }
    
           public function TeachersSechdule(){
        $items=Teachers::paginate(20);
         return view('admin.Centers.TeachersSechdule',['items'=>$items]);
    }
    
           public function EditTeacher($id){
             $Countris=Countris::all();
             $Governrates=Governrate::all();
             $ScientificMaterial=ScientificMaterial::all();
             $StudentGroup=StudentGroup::all();
               $item=Teachers::find($id);
                  $wf=60;
 $Accounts=AcccountingManual::orderBy('Code','asc')->where('Code','like', $wf.'%')->where('Type',0)->get(); 
             
               
               
                 $Subjs=TeachersSubjects::where('Teacher',$item->id)->get();
             $Grops=TeachersGroups::where('Teacher',$item->id)->get();
         return view('admin.Centers.EditTeachers',[
             'Countris'=>$Countris,
             'Governrates'=>$Governrates,
             'item'=>$item,
             'Accounts'=>$Accounts,
             'ScientificMaterial'=>$ScientificMaterial,
             'StudentGroup'=>$StudentGroup,
             'Subjs'=>$Subjs,
             'Grops'=>$Grops,
         
         ]);
    }
        
           public function AddTeachers(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Account'=>'required',
     'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Arabic_Name.required' => trans('admin.NameRequired'),         
            'Account.required' => trans('admin.AccountRequired'),      
    
         ]);

            $count=AcccountingManual::orderBy('id','desc')->where('Parent',request('Account'))->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',request('Account'))->first();    
            $codee=AcccountingManual::find(request('Account'));   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                          $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Arabic_Name');
              if(!empty(request('English_Name'))){
         $dataX['NameEn']=request('English_Name');
          }else{
               $dataX['NameEn']=request('Arabic_Name'); 
              
          }

         $dataX['Type']=1;
         $dataX['Parent']=request('Account');
         $dataX['Note']=request('Note');
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

         $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='EmployeesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }

                    $image1=request()->file('CV');
          if($image1){            
            $image_name1=Str::random(20);
            $ext1=strtolower($image1->getClientOriginalExtension());
            $image_full_name1=$image_name1 .'.' . $ext1 ;
            $upload_path1='EmployeesImages/';
            $image_url1=$upload_path1.$image_full_name1;
            $success1=$image1->move($upload_path1,$image_full_name1);            
                   }
        
    
             if(!empty($image_url1)){
       
                 $data['CV']=$image_url1; 
                 
             }else{
                 $data['CV']=null;
             }
           
       
                        $image2=request()->file('Qualification_Attach');
          if($image2){            
            $image_name2=Str::random(20);
            $ext2=strtolower($image2->getClientOriginalExtension());
            $image_full_name2=$image_name2 .'.' . $ext2 ;
            $upload_path2='EmployeesImages/';
            $image_url2=$upload_path2.$image_full_name2;
            $success2=$image2->move($upload_path2,$image_full_name2);            
                   }
        
    
             if(!empty($image_url2)){
       
                 $data['Qualification_Attach']=$image_url2; 
                 
             }else{
                 $data['Qualification_Attach']=null;
             }

     
                           $image3=request()->file('National_ID_Attach');
          if($image3){            
            $image_name3=Str::random(20);
            $ext3=strtolower($image3->getClientOriginalExtension());
            $image_full_name3=$image_name3 .'.' . $ext3 ;
            $upload_path3='EmployeesImages/';
            $image_url3=$upload_path3.$image_full_name3;
            $success3=$image3->move($upload_path3,$image_full_name3);            
                   }
        
    
             if(!empty($image_url3)){
       
                 $data['National_ID_Attach']=$image_url3; 
                 
             }else{
                 $data['National_ID_Attach']=null;
             }

    


        $data['Code']=request('Code');
        $data['Arabic_Name']=request('Arabic_Name');
            if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
          }else{
               $data['English_Name']=request('Arabic_Name'); 
              
          }

        $data['Work_Type']=request('Work_Type');
        $data['Hour_Price']=request('Hour_Price');
        $data['Qualification']=request('Qualification');
        $data['National_ID']=request('National_ID');
        $data['Nationality']=request('Nationality');
        $data['Gov']=request('Gov');
        $data['City']=request('City');
        $data['Place']=request('Place');
        $data['Address']=request('Address');
        $data['Age']=request('Age');
        $data['Phone1']=request('Phone1');
        $data['Phone2']=request('Phone2');
        $data['Whatsapp']=request('Whatsapp');
        $data['Facebook']=request('Facebook');
        $data['Instagram']=request('Instagram');
        $data['Linked']=request('Linked');
        $data['Telegram']=request('Telegram');
        $data['Arabic_Bio']=request('Arabic_Bio');
        $data['English_Bio']=request('English_Bio');
        $data['Marital_Status']=request('Marital_Status');
        $data['Account']=request('Account');
        $data['Account_Emp']=$Acc->id;

          Teachers::create($data);

          $IDD= Teachers::orderBy('id','desc')->first();
    
                   
           if(!empty(request('ScientificMaterial'))){

            $ScientificMaterial=request('ScientificMaterial');

            for ($i=0; $i < count($ScientificMaterial); $i++) { 
    
    $uux['Subject']=$ScientificMaterial[$i];
     $uux['Teacher']=$IDD->id;
TeachersSubjects::create($uux);

            }

           }
               
               
                  if(!empty(request('StudentGroup'))){

            $TeachersGroups=request('StudentGroup');

            for ($i=0; $i < count($TeachersGroups); $i++) { 
    
    $uu['Group']=$TeachersGroups[$i];
     $uu['Teacher']=$IDD->id;
TeachersGroups::create($uu);

            }

           }

            $counttt=AcccountingManual::orderBy('id','desc')->where('Parent',164)->count();        
            $codeCt=AcccountingManual::orderBy('id','desc')->where('Parent',164)->first();    
            $codeeCt=AcccountingManual::find(164);   
 
                if($counttt == 0){
                    
                $xxt=$codeeCt->Code.'01';    
             $dataXYt['Code']=(int) $xxt ;
                      
                }else{
                    
         $y=substr($codeCt->Code, strlen($codeeCt->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xxt= $codeeCt->Code.$NewXY; 
                    
                  $dataXYt['Code']=(int) $xxt;  
       
                }
                
         $dataXYt['Name']=request('Arabic_Name').' '.' عموله ';
           
                  if(!empty(request('English_Name'))){
           $dataXYt['NameEn']=request('English_Name').' '.' Commission ';
          }else{
             $dataXYt['NameEn']=request('Arabic_Name').' '.' Commission ';
              
          }

          
 
     
         $dataXYt['Type']=1;
         $dataXYt['Parent']=164;
         $dataXYt['Note']=request('Note');
         $dataXYt['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXYt);
           
           $mola=AcccountingManual::orderBy('id','desc')->first(); 
           
           
           Teachers::where('id',$IDD->id)->update(['Commission'=>$mola->id]);

 
                   $countt=AcccountingManual::orderBy('id','desc')->where('Parent',121)->count();        
            $codeC=AcccountingManual::orderBy('id','desc')->where('Parent',121)->first();    
            $codeeC=AcccountingManual::find(121);   
 
                if($countt == 0){
                    
                $xx=$codeeC->Code.'01';    
             $dataXY['Code']=(int) $xx ;
                      
                }else{
                    
             $y=substr($codeC->Code, strlen($codeeC->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xx= $codeeC->Code.$NewXY; 
                  $dataXY['Code']=(int) $xx;  
       
                }
                
         $dataXY['Name']=request('Arabic_Name').' '.' عهده ';
           
                         if(!empty(request('English_Name'))){
             $dataXY['NameEn']=request('English_Name').' '.' Custody ';
          }else{
            $dataXY['NameEn']=request('Arabic_Name').' '.' Custody ';
              
          }
           
      
         $dataXY['Type']=1;
         $dataXY['Parent']=121;
         $dataXY['Note']=request('Note');
         $dataXY['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXY);
           
             $ohda=AcccountingManual::orderBy('id','desc')->first(); 
           Teachers::where('id',$IDD->id)->update(['Covenant'=>$ohda->id]);

           
           
           
                 $countt=AcccountingManual::orderBy('id','desc')->where('Parent',43)->count();        
            $codeC=AcccountingManual::orderBy('id','desc')->where('Parent',43)->first();    
            $codeeC=AcccountingManual::find(43);   
 
                if($countt == 0){
                    
                $xx=$codeeC->Code.'01';    
             $dataXYY['Code']=(int) $xx ;
                      
                }else{
                    
        
                    
                $y=substr($codeC->Code, strlen($codeeC->Code));
          
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xx= $codeeC->Code.$NewXY; 
                  $dataXYY['Code']=(int) $xx;  
       
                }

                
         $dataXYY['Name']=request('Arabic_Name').' '.' استحقاق ';
           
                                  if(!empty(request('English_Name'))){
              $dataXYY['NameEn']=request('English_Name').' '.' Merit ';
          }else{
              $dataXYY['NameEn']=request('Arabic_Name').' '.' Merit ';
              
          }
           
           
     
         $dataXYY['Type']=1;
         $dataXYY['Parent']=43;
         $dataXYY['Note']=request('Note');
         $dataXYY['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXYY);
           
             $esthkak=AcccountingManual::orderBy('id','desc')->first(); 
           Teachers::where('id',$IDD->id)->update(['Merit'=>$esthkak->id]);

           

               

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المدربين';
           $dataUser['ScreenEn']='Teachres';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
                   if(!empty(request('English_Name'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
           session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

           public function PostEditTeachers(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Account'=>'required',
     'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Arabic_Name.required' => trans('admin.NameRequired'),         
            'Account.required' => trans('admin.AccountRequired'),      
    
         ]);

               $IDD= Teachers::find(request('ID'));


                  AcccountingManual::where('id',$IDD->Account_Emp)->update(['Name'=>request('Arabic_Name'),'NameEn'=>request('English_Name')]);
         
 
         
         AcccountingManual::where('id',$IDD->Covenant)->update(['Name'=>request('Arabic_Name').' '.' عهده ','NameEn'=>request('English_Name').' '.' Custody ']);
         
      
         AcccountingManual::where('id',$IDD->Commission)->update(['Name'=>request('Arabic_Name').' '.' عموله ','NameEn'=>request('English_Name').' '.' Commission ']);
         
        
         
         AcccountingManual::where('id',$IDD->Merit)->update(['Name'=>request('Arabic_Name').' '.' استحقاق ','NameEn'=>request('English_Name').' '.' Merit ']);



         $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='EmployeesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=request('Images');
             }

                    $image1=request()->file('CV');
          if($image1){            
            $image_name1=Str::random(20);
            $ext1=strtolower($image1->getClientOriginalExtension());
            $image_full_name1=$image_name1 .'.' . $ext1 ;
            $upload_path1='EmployeesImages/';
            $image_url1=$upload_path1.$image_full_name1;
            $success1=$image1->move($upload_path1,$image_full_name1);            
                   }
        
    
             if(!empty($image_url1)){
       
                 $data['CV']=$image_url1; 
                 
             }else{
                 $data['CV']=request('CVS');
             }
           
       
                        $image2=request()->file('Qualification_Attach');
          if($image2){            
            $image_name2=Str::random(20);
            $ext2=strtolower($image2->getClientOriginalExtension());
            $image_full_name2=$image_name2 .'.' . $ext2 ;
            $upload_path2='EmployeesImages/';
            $image_url2=$upload_path2.$image_full_name2;
            $success2=$image2->move($upload_path2,$image_full_name2);            
                   }
        
    
             if(!empty($image_url2)){
       
                 $data['Qualification_Attach']=$image_url2; 
                 
             }else{
                 $data['Qualification_Attach']=request('Qualification_AttachS');
             }

     
                           $image3=request()->file('National_ID_Attach');
          if($image3){            
            $image_name3=Str::random(20);
            $ext3=strtolower($image3->getClientOriginalExtension());
            $image_full_name3=$image_name3 .'.' . $ext3 ;
            $upload_path3='EmployeesImages/';
            $image_url3=$upload_path3.$image_full_name3;
            $success3=$image3->move($upload_path3,$image_full_name3);            
                   }
        
    
             if(!empty($image_url3)){
       
                 $data['National_ID_Attach']=$image_url3; 
                 
             }else{
                 $data['National_ID_Attach']=request('National_ID_AttachS');
             }

    


        $data['Code']=request('Code');
        $data['Arabic_Name']=request('Arabic_Name');
            if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
          }else{
               $data['English_Name']=request('Arabic_Name'); 
              
          }

        $data['Work_Type']=request('Work_Type');
        $data['Hour_Price']=request('Hour_Price');
        $data['Qualification']=request('Qualification');
        $data['National_ID']=request('National_ID');
        $data['Nationality']=request('Nationality');
        $data['Gov']=request('Gov');
        $data['City']=request('City');
        $data['Place']=request('Place');
        $data['Address']=request('Address');
        $data['Age']=request('Age');
        $data['Phone1']=request('Phone1');
        $data['Phone2']=request('Phone2');
        $data['Whatsapp']=request('Whatsapp');
        $data['Facebook']=request('Facebook');
        $data['Instagram']=request('Instagram');
        $data['Linked']=request('Linked');
        $data['Telegram']=request('Telegram');
        $data['Arabic_Bio']=request('Arabic_Bio');
        $data['English_Bio']=request('English_Bio');
        $data['Marital_Status']=request('Marital_Status');
        $data['Account']=request('Account');
   

          Teachers::where('id',$IDD->id)->update($data);

          
    
                   
           if(!empty(request('ScientificMaterial'))){
TeachersSubjects::where('Teacher',$IDD->id)->delete();
            $ScientificMaterial=request('ScientificMaterial');

            for ($i=0; $i < count($ScientificMaterial); $i++) { 
    
    $uux['Subject']=$ScientificMaterial[$i];
     $uux['Teacher']=$IDD->id;
TeachersSubjects::create($uux);

            }

           }
               
               
                  if(!empty(request('StudentGroup'))){
TeachersGroups::where('Teacher',$IDD->id)->delete();
            $TeachersGroups=request('StudentGroup');

            for ($i=0; $i < count($TeachersGroups); $i++) { 
    
    $uu['Group']=$TeachersGroups[$i];
     $uu['Teacher']=$IDD->id;
TeachersGroups::create($uu);

            }

           }

               

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المدربين';
           $dataUser['ScreenEn']='Teachres';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
                   if(!empty(request('English_Name'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
          
               
                  session()->flash('success',trans('admin.Updated'));
            return redirect('TeachersSechdule');
        
    }
    
          public function DeleteTeachers($id){
                      

            $del=Teachers::find($id);
      
         AcccountingManual::where('id',$del->Account_Emp)->delete();
         AcccountingManual::where('id',$del->Covenant)->delete();
         AcccountingManual::where('id',$del->Commission)->delete();
         AcccountingManual::where('id',$del->Merit)->delete();
        

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='المدربين';
           $dataUser['ScreenEn']='Teachres';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
    
    //Students
    
           public function StudentsSechdule(){
        $items=Students::paginate(20);
         return view('admin.Centers.StudentsSechdule',['items'=>$items]);
    }
    
             public function Students(){
             $Countris=Countris::all();
             $Courses=Courses::all();
             $Governrates=Governrate::all();
             $StudentGroup=StudentGroup::all();
             $SpecialCases=SpecialCases::all();
   
               $res=Students::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
             

         return view('admin.Centers.Students',[
             'Countris'=>$Countris,
             'Governrates'=>$Governrates,
             'Code'=>$Code,
             'Courses'=>$Courses,
             'StudentGroup'=>$StudentGroup,
             'SpecialCases'=>$SpecialCases,
         
         ]);
    }
    
  
            public function EditStudent($id){
             $Countris=Countris::all();
             $Governrates=Governrate::all();
             $StudentGroup=StudentGroup::all();
             $SpecialCases=SpecialCases::all();
             $StudentGroup=StudentGroup::all();
               $item=Students::find($id);
        
              $Courses=Courses::all();
                
                $StudentImportant=StudentImportant::where('Student',$id)->get();

         return view('admin.Centers.EditStudent',[
             'Countris'=>$Countris,
             'Governrates'=>$Governrates,
             'item'=>$item,
             'StudentGroup'=>$StudentGroup,
             'SpecialCases'=>$SpecialCases,
             'Courses'=>$Courses,
             'StudentImportant'=>$StudentImportant,
         
         ]);
    }

    
           public function AddStudents(Request $request){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.NameRequired'),      

         ]);

            $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();    
            $codee=AcccountingManual::find(24);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                
        $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 

                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Arabic_Name');
             if(!empty(request('English_Name'))){
         $dataX['NameEn']=request('English_Name');
          }else{
               $dataX['NameEn']=request('Arabic_Name'); 
              
          }

         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 



        $data['Code']=request('Code');
        $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
          }else{
               $data['English_Name']=request('Arabic_Name'); 
              
          }

    
        $data['Account']=$Acc->id;
    $data['Group']=request('Group'); 
    $data['Phone1']=request('Phone1'); 
    $data['Phone2']=request('Phone2'); 
    $data['Dad_Phone']=request('Dad_Phone'); 
    $data['Mom_Phone']=request('Mom_Phone'); 
    $data['Special_Case']=request('Special_Case'); 
    $data['Case']=request('Case'); 
    $data['Email']=request('Email'); 
    $data['Whatsapp']=request('Whatsapp'); 
    $data['Age']=request('Age'); 
    $data['Gov']=request('Gov'); 
    $data['City']=request('City'); 
    $data['Place']=request('Place'); 
    $data['Nationality']=request('Nationality'); 
    $data['Address']=request('Address'); 
    $data['Profession']=request('Profession'); 
    $data['Ntional_ID']=request('Ntional_ID'); 
          Students::create($data);
            
            $last=Students::orderBy('id','desc')->first();
         
               
               if(!empty(request('Important'))){
                   
                   
                   $imortant=request('Important');
                   
                   
                   for($i=0 ; $i < count($imortant) ; $i++){
                       
                     $stu['Student']=$last->id;
                     $stu['Course']=$imortant[$i];
                       
                         StudentImportant::create($stu);
                       
                   }
                   
                   
                   
               }
               
             

            
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الطلاب';
           $dataUser['ScreenEn']='Students';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
     
              if(!empty(request('English_Name'))){
          $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
    
            public function PostEditStudent(Request $request){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.NameRequired'),      

         ]);




        $data['Code']=request('Code');
        $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
          }else{
               $data['English_Name']=request('Arabic_Name'); 
              
          }

    
        $data['Account']=$Acc->id;
    $data['Group']=request('Group'); 
    $data['Phone1']=request('Phone1'); 
    $data['Phone2']=request('Phone2'); 
    $data['Dad_Phone']=request('Dad_Phone'); 
    $data['Mom_Phone']=request('Mom_Phone'); 
    $data['Special_Case']=request('Special_Case'); 
    $data['Case']=request('Case'); 
    $data['Email']=request('Email'); 
    $data['Whatsapp']=request('Whatsapp'); 
    $data['Age']=request('Age'); 
    $data['Gov']=request('Gov'); 
    $data['City']=request('City'); 
    $data['Place']=request('Place'); 
    $data['Nationality']=request('Nationality'); 
    $data['Address']=request('Address'); 
    $data['Profession']=request('Profession'); 
    $data['Ntional_ID']=request('Ntional_ID'); 
          Students::where('id',request('ID'))->update($data);
            
            
            
          
               if(!empty(request('Important'))){
                   
                   StudentImportant::where('Student',$id)->delete();
                   $imortant=request('Important');
                   
                   
                   for($i=0 ; $i < count($imortant) ; $i++){
                       
                     $stu['Student']=$id;
                     $stu['Course']=$imortant[$i];
                       
                         StudentImportant::create($stu);
                       
                   }
                   
                   
                   
               }
               
             

            
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الطلاب';
           $dataUser['ScreenEn']='Students';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
     
              if(!empty(request('English_Name'))){
          $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
           UsersMoves::create($dataUser);
         
                       session()->flash('success',trans('admin.Updated'));
            return redirect('StudentsSechdule');
        
    }
    
    
            public function DeleteStudents($id){
                  $del=Students::find($id);        
            
    
         AcccountingManual::where('id',$del->Account)->delete();
       

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='الطلاب';
           $dataUser['ScreenEn']='Students';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
    
      public function AddNewStudent(Request $request){

       

            $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();    
            $codee=AcccountingManual::find(24);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                
        $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 

                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Arabic_Name');
               $dataX['NameEn']=request('Arabic_Name'); 
         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

                   $res=Students::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }


        $data['Code']=$Code;
        $data['Arabic_Name']=request('Arabic_Name');
               $data['English_Name']=request('Arabic_Name'); 
        $data['Account']=$Acc->id;
    $data['Phone1']=request('Phone1'); 
    $data['Dad_Phone']=request('Dad_Phone'); 
    $data['Whatsapp']=request('Whatsapp'); 
    $data['Age']=request('Age'); 
    $data['Address']=request('Address'); 
    $data['Profession']=request('Profession'); 
    $data['Ntional_ID']=request('Ntional_ID'); 
          Students::create($data);
            

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الطلاب';
           $dataUser['ScreenEn']='Students';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
        $dataUser['ExplainEn']=request('Arabic_Name');

           UsersMoves::create($dataUser);
         
           $states=['SUCEESS'=>'SUCEESS'];
           return response()->json($states);
        
    }
    
    
        public function AllStudent() {
          
            
           if(app()->getLocale() == 'ar' ){     
   $states = Students::orderBy('id','asc')
              ->pluck("Arabic_Name","id");
           }else{
           $states = Students::orderBy('id','asc')
              ->pluck("English_Name","id");       
           }
   
       return response()->json($states);
           
    } 
    
     public function AllStudentJ($id) {
          
         
        if(app()->getLocale() == 'ar' ){   
          
        
               $states = Students::
                where('Arabic_Name','ILIKE', "%{$id}%")
             ->pluck("Arabic_Name","id");
    
        }else{

               $states = Students::
                where('English_Name','ILIKE', "%{$id}%")
             ->pluck("English_Name","id");
               
            
            
        }
          
    
   
       return response()->json($states);
           
    }
    


    
    //Courses
             public function Courses(){

             $ScientificMaterial=ScientificMaterial::all();
             $CoursesCategory=CoursesCategory::all();
               $res=Courses::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
             
                 $items=Courses::paginate(20);
    
         return view('admin.Centers.Courses',[
             'items'=>$items,
             'Code'=>$Code,
             'ScientificMaterial'=>$ScientificMaterial,
             'CoursesCategory'=>$CoursesCategory,
         
         ]);
    }
               public function CoursesDetails(){


             
                 $items=Courses::all();
    
         return view('admin.Centers.CoursesDetails',[
             'items'=>$items,
    
         
         ]);
    }
    
         public function AddCourses(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         

         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
               $data['Code']=request('Code');
               $data['Category']=request('Category');
               $data['Lec_Num']=request('Lec_Num');
               $data['Hours']=request('Hours');
               $data['Subject']=request('Subject');
               $data['Note']=request('Note');
         Courses::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                 $dataUser['Screen']=' الكورسات';
           $dataUser['ScreenEn']='Courses ';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCourses($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   

         $data['Arabic_Name']=request('Arabic_Name');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
             $data['Code']=request('Code');
               $data['Category']=request('Category');
               $data['Lec_Num']=request('Lec_Num');
               $data['Hours']=request('Hours');
               $data['Subject']=request('Subject');
               $data['Note']=request('Note');
    
           Courses::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']=' الكورسات';
           $dataUser['ScreenEn']='Courses ';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCourses($id){
                      
        $del=Courses::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
        $dataUser['Screen']=' الكورسات';
           $dataUser['ScreenEn']='Courses ';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
//ReserveCourse
    
      public function ReserveCourseSechdule(){
        $items=ReserveCourse::paginate(20);
         return view('admin.Centers.ReserveCourseSechdule',['items'=>$items]);
    }
       public function ReserveCourse(){
            
             $CoursesType=CoursesType::all();
             $Teachers=Teachers::all();
             $Courses=Courses::all();
               $res=ReserveCourse::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
             
    $CoursesHalls=CoursesHalls::all();
         return view('admin.Centers.ReserveCourse',[
             'CoursesType'=>$CoursesType,
             'Teachers'=>$Teachers,
             'Courses'=>$Courses,
             'Code'=>$Code,
             'CoursesHalls'=>$CoursesHalls,
           
         
         ]);
    }
    
            public function AddReserveCourse(Request $request){

        $data= $this->validate(request(),[
             'Start_Date'=>'required',
             'End_Date'=>'required',

               ],[
       

         ]);



        $data['Code']=request('Code');
        $data['Course']=request('Course');
    $data['Start_Date']=request('Start_Date'); 
    $data['End_Date']=request('End_Date'); 
    $data['Course_Type']=request('Course_Type'); 
    $data['Teacher']=request('Teacher'); 
    $data['Required_Number']=request('Required_Number'); 
    $data['Certificate']=request('Certificate'); 
    $data['Cost']=request('Cost'); 
    $data['Total_Required']=request('Total_Required'); 
    $data['Status']=0; 
    $data['Time']=request('Time'); 
    $data['Hall']=request('Hall'); 
  
          ReserveCourse::create($data);
            
    $R=ReserveCourse::orderBy('id','desc')->first();    
    
          if(!empty(request('Days'))){

            $Days=request('Days');
            $From_Time=request('From_Time');
            $To_Time=request('To_Time');

            for ($i=0; $i < count($Days); $i++) { 
    
    $uux['Day']=$Days[$i];
    $uux['From_Time']=$From_Time[$i];
    $uux['To_Time']=$To_Time[$i];
     $uux['Reserve']=$R->id;
ReserveCourseDays::create($uux);

            }

           }
            

            
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='حجز كورس';
           $dataUser['ScreenEn']='Reserve Course';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
          $dataUser['ExplainEn']=request('Code');
   
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function DeleteReserveCourse($id){
                  $del=ReserveCourse::find($id);        

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='حجز كورس';
           $dataUser['ScreenEn']='Reserve Course';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
    public function EditResreveCourse($id){
            
             $CoursesType=CoursesType::all();
             $Teachers=Teachers::all();
             $Courses=Courses::all();
               $item=ReserveCourse::find($id);
               $Days=ReserveCourseDays::where('Reserve',$id)->get();
             $CoursesHalls=CoursesHalls::all();
        

         return view('admin.Centers.EditReserveCourse',[
             'CoursesType'=>$CoursesType,
             'Teachers'=>$Teachers,
             'Courses'=>$Courses,
             'item'=>$item,
             'Days'=>$Days,
             'CoursesHalls'=>$CoursesHalls,
           
         
         ]);
    }

    
            public function PostEditReserveCourse(Request $request){

        $data= $this->validate(request(),[
             'Start_Date'=>'required',
             'End_Date'=>'required',

               ],[
       

         ]);



        $data['Code']=request('Code');
        $data['Course']=request('Course');
    $data['Start_Date']=request('Start_Date'); 
    $data['End_Date']=request('End_Date'); 
    $data['Course_Type']=request('Course_Type'); 
    $data['Teacher']=request('Teacher'); 
    $data['Required_Number']=request('Required_Number'); 
    $data['Certificate']=request('Certificate'); 
    $data['Cost']=request('Cost'); 
    $data['Total_Required']=request('Total_Required'); 
    $data['Status']=0; 
    $data['Time']=request('Time'); 
    $data['Hall']=request('Hall'); 
          ReserveCourse::where('id',request('ID'))->update($data);
            
    $R=ReserveCourse::find(request('ID'));    
    
          if(!empty(request('Days'))){
ReserveCourseDays::where('Reserve',request('ID'))->delete();
          $Days=request('Days');
            $From_Time=request('From_Time');
            $To_Time=request('To_Time');

            for ($i=0; $i < count($Days); $i++) { 
    
    $uux['Day']=$Days[$i];
    $uux['From_Time']=$From_Time[$i];
    $uux['To_Time']=$To_Time[$i];
     $uux['Reserve']=$R->id;
ReserveCourseDays::create($uux);

            }

           }
            

            
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='حجز كورس';
           $dataUser['ScreenEn']='Reserve Course';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
          $dataUser['ExplainEn']=request('Code');
   
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
   return redirect('ReserveCourseSechdule');
        
    }
    
    
         public function ComplitedReserveCourse($id){
             
        ReserveCourse::where('id',$id)->update(['Status'=>1]);        
             
        session()->flash('success',trans('admin.Updated'));
        return back();

           } 
    
    
             public function UnComplitedReserveCourse($id){
             
        ReserveCourse::where('id',$id)->update(['Status'=>0]);        
             
        session()->flash('success',trans('admin.Updated'));
        return back();

           } 

        public function StudentReserveCourse($id){
            
             $CoursesType=CoursesType::all();
             $Teachers=Teachers::all();
             $Courses=Courses::all();
             $Students=Students::all();
               $item=ReserveCourse::find($id);
               $Days=ReserveCourseDays::where('Reserve',$id)->get();
           
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            
                      $Coins = Coins::get();
            
             $RSts=ReserveCourseStudents::where('Reserve',$id)->get();
  $CoursesHalls=CoursesHalls::all();
         return view('admin.Centers.StudentReserveCourse',[
             'CoursesType'=>$CoursesType,
             'Teachers'=>$Teachers,
             'Courses'=>$Courses,
             'item'=>$item,
             'Days'=>$Days,
             'Students'=>$Students,
             'Safes'=>$Safes,
             'Coins'=>$Coins,
             'RSts'=>$RSts,
             'CoursesHalls'=>$CoursesHalls,
           
         
         ]);
    }

    
      public function PostStudentReserveCourse(Request $request){



 
    $data['Total_Paid']=request('Total_Paid'); 
    $data['Total_Num']=request('Total_Num'); 
    $data['Total_Cost']=request('Total_Cost'); 
    $data['Safe']=request('Safe'); 
    $data['Draw']=request('Draw'); 
    $data['Coin']=request('Coin'); 
  
          ReserveCourse::where('id',request('ID'))->update($data);
            
          
          
    $R=ReserveCourse::find(request('ID'));    
    
           $CodeT=request('Code');   
          if(!empty(request('Student'))){

              ReserveCourseStudents::where('Reserve',request('ID'))->delete();
            $Student=request('Student');
            $Pay=request('Pay');
            $Cost=request('Cost');
            $Residual=request('Residual');
            $Constraint=request('Constraint');
            $Dollar_Value=request('Dollar_Value');

            for ($i=0; $i < count($Student); $i++) { 
    
    $uux['Student']=$Student[$i];
    $uux['Pay']=$Pay[$i];
    $uux['Cost']=$Cost[$i];
    $uux['Residual']=$Residual[$i];
    $uux['Constraint']=$Constraint[$i];
    $uux['Dollar_Value']=$Dollar_Value[$i];
     $uux['Reserve']=$R->id;
ReserveCourseStudents::create($uux);

                
                if($Constraint[$i] == 0){
                 
                    
                if($Residual[$i] == 0){
                    
                      $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $Pay[$i],
            'Total_Creditor' => $Pay[$i],
            'Note' => null,
  
        )
    );
         
 $stu=Students::find($Student[$i]);
        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Pay[$i];
        $PRODUCTSS['Account']=$stu->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Pay[$i];
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Pay[$i];
        $Gen['Account']=$stu->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=$Pay[$i];
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=$Pay[$i];
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * $Pay[$i];
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
                    
                }else{
                    
                          $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => date('Y-m-d'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $Pay[$i],
            'Total_Creditor' => $Pay[$i],
            'Note' => null,
  
        )
    );
         
 $stu=Students::find($Student[$i]);
        
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Cost[$i];
        $PRODUCTSS['Account']=48;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Cost[$i];
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Cost[$i];
        $Gen['Account']=48;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=$Cost[$i];
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$stu->Account;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=$Cost[$i];
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * $Cost[$i];
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=$stu->Account;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);    
                    
              $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Pay[$i];
        $PRODUCTSS['Account']=$stu->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Pay[$i];
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Pay[$i];
        $Gen['Account']=$stu->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=$Pay[$i];
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);   
                    
                    
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=$Pay[$i];
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * $Pay[$i];
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);             
                    
                    
                    
                    
                    
                }    
                    
                    
                    
                    
                }
                

            }

           }
            

            
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='حجز  طالب كورس';
           $dataUser['ScreenEn']='Reserve Student Course';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
          $dataUser['ExplainEn']=request('Code');
   
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
    
      public function StudentPrintBill($id){
            
        $student=ReserveCourseStudents::find($id);
           $item=ReserveCourse::find($student->Reserve);

         return view('admin.Centers.PrintStudentBill',[
             'student'=>$student,
             'item'=>$item,
         
           
         
         ]);
    }
   
        

      public function OtherReserv(Request $request){

          
                $Day=request('Day');
                $From_Time=request('From_Time');
                $To_Time=request('To_Time');
                $Hall=request('Hall');
                $Start_Date=request('Start_Date');
                $End_Date=request('End_Date');
          
          
          $finds=ReserveCourse::
          where('Hall',$Hall)
          ->where('Start_Date','>=',$Start_Date)
          ->where('End_Date','>=',$End_Date)
              ->get();
          
        if(!empty($finds)){  
          foreach($finds as $find){
           
              
            $days=ReserveCourseDays::where('Reserve',$find->id)->get();
              
               if(!empty($days)){  
                    foreach($days as $day){
                        
                        
                    if($day == 1){
                        
                        
                    }   
                        
                        
   
                        
                        
                    }
               }else{
                   
               $av=0;       
               }
              
              
          }

        }else{
       $av=0;     
        }
           $states=['avliable'=>$av];
           return response()->json($states);
        
    }
    
    
    
    
    
    //RegCourses
    
       public function RegCoursesSechdule(){
        $items=RegCourses::paginate(20);
         return view('admin.Centers.RegCoursesSechdule',['items'=>$items]);
    }
    
    public function RegCourses(){
            
 
            
             $CoursesHalls=CoursesHalls::all();
             $ReserveCourse=ReserveCourse::where('Status',1)->get();
        
               $res=RegCourses::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
             

         return view('admin.Centers.RegCourses',[
             'CoursesHalls'=>$CoursesHalls,
   
             'ReserveCourse'=>$ReserveCourse,
             'Code'=>$Code,
           
         
         ]);
    }

  function Reserve_CourseFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $Reserve_Course = $request->get('Reserve_Course');                       
                               
    if($Reserve_Course != '')
    {
            
               
                $Fi =ReserveCourse::find($Reserve_Course); 
                $Prods =ReserveCourseStudents::where('Reserve',$Reserve_Course)->get(); 
    

if(app()->getLocale() == 'ar'){
    $tech= $Fi->Teacher()->first()->Arabic_Name;
}else{
        $tech= $Fi->Teacher()->first()->English_Name;
    
}
        
  
                       
     }

         $total_row = $Prods->count();
      if($total_row > 0) 
      { 
          
 


      foreach($Prods as $pro){
          
          if(app()->getLocale() == 'ar'){
    $name= $pro->Student()->first()->Arabic_Name;
}else{
        $name= $pro->Student()->first()->English_Name;
    
}
           $output .= '
           <tr>
          <td> '.$name.' 
          
          <input type="hidden" name="Student[]" value="'.$pro->Student.'">
          </td> 
          
        <td> 
          
          <input type="checkbox" class="form-control" name="Attend[]" value="1">
          </td>       
           </tr>
           ';
          
      }
  
      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
       'teacherName'  =>$tech,
       'teacher'  => $Fi->Teacher,
      );
      echo json_encode($data);
     }
    }

    
         public function AddRegCourses(Request $request){

        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',

               ],[
       

         ]);



        $data['Code']=request('Code');
        $data['Date']=request('Date');
    $data['Hall']=request('Hall'); 
    $data['Reserve_Course']=request('Reserve_Course'); 
    $data['Teacher']=request('Teacher'); 
    $data['Teacher_Attend']=request('Teacher_Attend'); 

          RegCourses::create($data);
            
    $R=RegCourses::orderBy('id','desc')->first();    
    
          if(!empty(request('Student'))){

            $Student=request('Student');
            $Attend=request('Attend');

            for ($i=0; $i < count($Student); $i++) { 
    
    $uux['Student']=$Student[$i];
                if(!empty($Attend[$i])){
    $uux['Attend']=$Attend[$i];
                }else{
               $uux['Attend']=0;         
                    }
    $uux['Date']=request('Date');
     $uux['Reg']=$R->id;
RegCoursesStudents::create($uux);





            }

           }
            


         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
        
     public function DeleteRegCourses($id){
                  $del=RegCourses::find($id);        
               RegCoursesStudents::where('Reg',$id)->delete();        


         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
        public function EditRegCourses($id){
            
 
            
             $CoursesHalls=CoursesHalls::all();
             $ReserveCourse=ReserveCourse::where('Status',1)->get();
        
               $item=RegCourses::find($id);
               $students=RegCoursesStudents::where('Reg',$id)->get();
     
             

         return view('admin.Centers.EditRegCourses',[
             'CoursesHalls'=>$CoursesHalls,
   
             'ReserveCourse'=>$ReserveCourse,
             'item'=>$item,
             'students'=>$students,
           
         
         ]);
    }


    public function PostEditRegCourses(Request $request){

        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',

               ],[
       

         ]);



        $data['Code']=request('Code');
        $data['Date']=request('Date');
    $data['Hall']=request('Hall'); 
    $data['Reserve_Course']=request('Reserve_Course'); 
    $data['Teacher']=request('Teacher'); 
    $data['Teacher_Attend']=request('Teacher_Attend'); 

          RegCourses::where('id',request('ID'))->update($data);
            
    $R=RegCourses::orderBy('id','desc')->first();    
    
          if(!empty(request('Student'))){

              RegCoursesStudents::where('Reg',request('ID'))->delete();
            $Student=request('Student');
            $Attend=request('Attend');

            for ($i=0; $i < count($Student); $i++) { 
    
    $uux['Student']=$Student[$i];
                if(!empty($Attend[$i])){
    $uux['Attend']=$Attend[$i];
                }else{
               $uux['Attend']=0;         
                    }
    $uux['Date']=request('Date');
     $uux['Reg']=$R->id;
RegCoursesStudents::create($uux);





            }

           }
            


         
             session()->flash('success',trans('admin.Updated'));
        return redirect('RegCoursesSechdule');
        
    }
    
    

    



    

    
}

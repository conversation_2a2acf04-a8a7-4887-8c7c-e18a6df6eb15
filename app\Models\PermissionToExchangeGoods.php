<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PermissionToExchangeGoods extends Model
{
    use HasFactory;
     protected $table = 'permission_to_exchange_goods';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Note',  
        'Account',
        'Store',
        'To_Store',
        'Coin',
        'Cost_Center',
        'User', 
             'Status', 

    ];


          public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
   
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function To_Store()
    {
        return $this->belongsTo(Stores::class,'To_Store');
    }
    
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
              public function ProductsPermissionToExchangeGoods()
    {
        return $this->hasOne(ProductsPermissionToExchangeGoods::class);
    }
    
       
}

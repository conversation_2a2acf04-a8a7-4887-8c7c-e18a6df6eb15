<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\AdminPeriority;

class Periorty_Icon_Logo
{
    public function handle($request, Closure $next)
    {
        $items = AdminPeriority::all();
        foreach($items as $item){
       
                if($item->Icon_Logo == 0 &&  $item->admin_id  == auth()->guard('admin')->user()->id)
                {
                   return redirect('OstAdmin');
                }            
    
         
        }
        
        return $next($request);
    }
}




<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpassyReserveDate extends Model
{
    use HasFactory;
              protected $table = 'empassy_reserve_dates';
      protected $fillable = [
        'Code',
        'Date',
        'Client',
        'Purpose',
        'Empassy',
        'Booking_Date',
        'Cost',
        'Pay',
        'Residual',
        'Safe',
        'Draw',
        'Coin',
     
   
    ];
    
               public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
   
               public function Purpose()
    {
        return $this->belongsTo(PurposeTravel::class,'Purpose');
    }
  
               public function Empassy()
    {
        return $this->belongsTo(Empassies::class,'Empassy');
    }  
               public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }  
               public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductMovesColumnsSechdule extends Model
{
    use HasFactory;
          protected $table = 'product_moves_columns_sechdules';
      protected $fillable = [

        'Date',                         
        'Product_Code',               
        'Product_Name',               
        'Unit',               
        'Type',               
        'Bill_Num',               
        'Incom',               
        'Outcom',               
        'Credit',               
        'Group',               
        'Brand',               
        'Store',               
        'User',               
        'Safe',               
        'Branch',                          


    ];
}

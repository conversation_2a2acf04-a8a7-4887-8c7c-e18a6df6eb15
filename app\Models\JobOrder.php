<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobOrder extends Model
{
    use HasFactory;
     protected $table = 'job_orders';
      protected $fillable = [

        'Code',                         
        'Date',
        'Draw',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'ShipStatus',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'Later_Due',
        'Later_Collection',
        'TaxBill',
        'TaxCode',
        'ProfitPrecent',
        'TaxOnTotal',
        'TaxOnTotalType',
        'ProfitTax',
        'Time',
        'Branch',
        'CustomerGroup',
        'Total_Cost',
        'DiscountTax',
        'RecivedDate',
        'Recipient',
        'RecivedVoucherCode',
        'TransferOrder',
        'ExecuteOrder',

          
    ];

    
    

    public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }         
    
    
         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
    
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
              public function ProductJobOrder()
    {
        return $this->hasOne(ProductJobOrder::class);
    }
    
        
   
 
    
               public function TaxOnTotalType()
    {
        return $this->belongsTo(Taxes::class,'TaxOnTotalType');
    }
    

      
    
    
        public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
    
}

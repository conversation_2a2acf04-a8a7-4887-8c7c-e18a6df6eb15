<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintainceDefaultData extends Model
{
    use HasFactory;
          protected $table = 'maintaince_default_data';
      protected $fillable = [
        'Company',
        'Device_Type',
        'Device_Case',
        'Coin',
        'Cost_Center',
        'Draw',
        'Client',
        'Sure',
        'Eng',
        'Recipient',
        'Store',
       
    ];
    
    
    
               public function Company()
    {
        return $this->belongsTo(ManufactureCompany::class,'Company');
    }
            public function Device_Type()
    {
        return $this->belongsTo(DevicesTypesy::class,'Device_Type');
    }
    
               public function Device_Case()
    {
        return $this->belongsTo(DesviceCases::class,'Device_Case');
    }
    
               public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
               public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
   
               public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
    public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }
    public function Eng()
    {
        return $this->belongsTo(Employess::class,'Eng');
    }
    
    public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
    
}

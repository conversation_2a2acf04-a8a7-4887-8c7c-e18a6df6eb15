<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SubVirablesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('sub_virables')->delete();
        
        \DB::table('sub_virables')->insert(array (
            0 => 
            array (
                'id' => 13,
                'Name' => 'احمر',
                'V_ID' => 7,
                'created_at' => '2022-06-09 05:43:53',
                'updated_at' => '2022-06-09 05:43:53',
            ),
            1 => 
            array (
                'id' => 14,
                'Name' => 'ازرق',
                'V_ID' => 7,
                'created_at' => '2022-06-09 05:44:01',
                'updated_at' => '2022-06-09 05:44:01',
            ),
            2 => 
            array (
                'id' => 15,
                'Name' => 'اسود',
                'V_ID' => 7,
                'created_at' => '2022-06-09 05:44:09',
                'updated_at' => '2022-06-09 05:44:09',
            ),
            3 => 
            array (
                'id' => 16,
                'Name' => 'S',
                'V_ID' => 8,
                'created_at' => '2022-06-09 05:44:37',
                'updated_at' => '2022-06-09 05:44:37',
            ),
            4 => 
            array (
                'id' => 17,
                'Name' => 'L',
                'V_ID' => 8,
                'created_at' => '2022-06-09 05:44:43',
                'updated_at' => '2022-06-09 05:44:43',
            ),
            5 => 
            array (
                'id' => 18,
                'Name' => 'M',
                'V_ID' => 8,
                'created_at' => '2022-06-09 05:44:51',
                'updated_at' => '2022-06-09 05:44:51',
            ),
            6 => 
            array (
                'id' => 19,
                'Name' => '128G',
                'V_ID' => 9,
                'created_at' => '2022-06-09 05:45:09',
                'updated_at' => '2022-06-09 05:45:09',
            ),
            7 => 
            array (
                'id' => 20,
                'Name' => '256G',
                'V_ID' => 9,
                'created_at' => '2022-06-09 05:45:17',
                'updated_at' => '2022-06-09 05:45:17',
            ),
            8 => 
            array (
                'id' => 21,
                'Name' => '64G',
                'V_ID' => 9,
                'created_at' => '2022-06-09 05:45:25',
                'updated_at' => '2022-06-09 05:45:25',
            ),
        ));
        
        
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExecutingReceiving extends Model
{
    use HasFactory;
      protected $table = 'executing_receivings';
      protected $fillable = [
        'Code',
        'Date',
        'Qty',
        'Total',
        'Model',
        'User',
        'Time',
        'Branch',
        'StoreIn',
        'StoreOut',
        'Sort',
        'Vendor',
        'Cost_Workmentship',
        'Total_Workmentship',

    ];
    
    
       public function Model()
    {
        return $this->belongsTo(ManufacturingModel::class,'Model');
    }
    
    
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
               public function StoreIn()
    {
        return $this->belongsTo(Stores::class,'StoreIn');
    }
    
                  public function StoreOut()
    {
        return $this->belongsTo(Stores::class,'StoreOut');
    }
    
 
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ReportsSettings;

class ReportsSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        ReportsSettings::create([
            'Product_Info' => '1',
            'Product_Order_Limit' => '1',
            'ReportStartPeriodProducts' => '1',
            'SettlementsReports' => '1',
            'StoresCost' => '1',
            'StoresInventory' => '1',
            'Collection_Delegates' => '1',
            'Sales_Delegates' => '1',
            'StagnantItems' => '1',
            'ItemsMoves' => '1',
            'StoresBalancesTwo' => '1',
            'NetPurchases' => '1',
            'NetSales' => '1',
            'ClientSales' => '1',
            'ExecutorSales' => '1',
            'InstallmentReport' => '1',
            'ExpiredProucts' => '1',
            'StoresSalesDetails' => '1',
            'TotalNetPurchases' => '1',
            'TotalNetSales' => '1',
            'Profits' => '1',
            'Shifts' => '1',
            'Shifts_Details' => '1',
            'DailyClosing' => '1',
            'Products' => '1',
            'DailyShifts' => '1',
            'ExpensesReport' => '1',
            'DailyProducts' => '1',
            'EmployeeCommissionDiscounts' => '1',
            'VendorPricesReport' => '1',
            'DailyMoves' => '1',
            'GroupsSales' => '1',
            'VendorPurchases' => '1',
            'ExceptProfits' => '1',
            'DelegateSalesDetails' => '1',
            'CreditStores' => '1',
            'ProductProfits' => '1',
            'ExceptProductProfits' => '1',
            'SalesBills' => '1',
            'PurchasesBills' => '1',
            'StoresMovesReport' => '1',
            'StoresTransferReport' => '1',
            'SafesTransferReport' => '1',
            'CompareSalesPrice' => '1',
            'ProductMoveDetails' => '1',
            'MostSalesProducts' => '1',
            'ProfitSalesProduct' => '1',
            'ClientAccountStatement' => '1',
            'ClientsStatement' => '1',
            'VendorAccountStatement' => '1',
            'VendorsStatement' => '1',
            'EmpGoals' => '1',
            'InventorySerial' => '1',
            'TotalExpensesSafes' => '1',
            'SubIncomList' => '1',
            'ExpensesList' => '1',
            'StoresBalances' => '1',
            'StoresBalancesCat' => '1',
            'ItemCost' => '1',
            'StoresInventoryy' => '1',
            'DelegateSalesDetailss' => '1',
            'ProfitDelegateSalesDetails' => '1',
            'InstallmentCompaniesSales' => '1',
            'StoresCosts' => '1',
            'DailyClosingDetails' => '1',
            'SalesProsMoreDetails' => '1',
            'StagnantItemss' => '1',
            'SalesCustomersGroups' => '1',
            'BrandsSales' => '1',
            'Customer_Debts' => '1',
            'Vendor_Debts' => '1',
            'MaintanceSalesReport' => '1',
            'Maintenance_Tune' => '1',
            'ProfitGroupsReport' => '1',
            'IncomListReport' => '1',
        ]);
    }
}

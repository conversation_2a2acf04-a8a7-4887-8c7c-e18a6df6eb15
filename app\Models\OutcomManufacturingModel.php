<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutcomManufacturingModel extends Model
{
    use HasFactory;
      protected $table = 'outcom_manufacturing_models';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'SmallQty',
        'SmallCode',  
        'Store',
        'Product',
        'Unit',
        'Model',
        'Cost',
    
    
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Model()
    {
        return $this->belongsTo(ManufacturingModel::class,'Model');
    }
    
}

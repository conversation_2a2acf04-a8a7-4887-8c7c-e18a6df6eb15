<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstallmentDates extends Model
{
    use HasFactory;
           protected $table = 'installment_dates';
      protected $fillable = [
        'Date',
        'Value',
        'Status',
        'Client',
        'Install',

     
    ];
    
    
       public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
      public function Install()
    {
        return $this->belongsTo(Installment::class,'Install');
    }
}

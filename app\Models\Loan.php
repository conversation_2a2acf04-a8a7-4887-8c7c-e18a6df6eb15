<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Loan extends Model
{
    use HasFactory;
     protected $table = 'loans';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Amount',
        'Years_Number',
        'First_Date',
        'Install',
        'Install_Numbers',
        'Note',
        'Emp',
        'Type',
        'User',
        'Draw',
        'Safe',
        'Coin',
        'Cost_Center',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
         public function Type()
    {
        return $this->belongsTo(LoanTypes::class,'Type');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
                          public function EmpInstallment()
    {
        return $this->hasOne(EmpInstallment::class);
    }
    
    
}

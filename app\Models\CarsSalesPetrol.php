<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CarsSalesPetrol extends Model
{
    use HasFactory;
      protected $table = 'cars_sales_petrols';
      protected $fillable = [
        'Car_Amount',
        'Car',       
        'SalesPetrol',       
    ];

            public function Car()
    {
        return $this->belongsTo(CompanyCars::class,'Car');
    }
    
             public function SalesPetrol()
    {
        return $this->belongsTo(SalesPetrol::class,'SalesPetrol');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReportsSettings extends Model
{
    use HasFactory;
        protected $table = 'reports_settings';
      protected $fillable = [
        'Product_Info',
        'Product_Order_Limit',
        'ReportStartPeriodProducts',
        'SettlementsReports',
        'StoresCost',
        'StoresInventory',
        'Collection_Delegates',
        'Sales_Delegates',
        'StagnantItems',
        'ItemsMoves',
        'StoresBalancesTwo',
        'NetPurchases',
        'NetSales',
        'ClientSales',
        'ExecutorSales',
        'InstallmentReport',
        'ExpiredProucts',
        'StoresSalesDetails',
        'TotalNetPurchases',
        'TotalNetSales',
        'Profits',
        'Shifts',
        'Shifts_Details',
        'DailyClosing',
        'Products',
        'DailyShifts',
        'ExpensesReport',
        'DailyProducts',
        'EmployeeCommissionDiscounts',
        'VendorPricesReport',
        'DailyMoves',
        'GroupsSales',
        'VendorPurchases',
        'ExceptProfits',
        'DelegateSalesDetails',
        'CreditStores',
        'ProductProfits',
        'ExceptProductProfits',
        'SalesBills',
        'PurchasesBills',
        'StoresMovesReport',
        'StoresTransferReport',
        'SafesTransferReport',
        'CompareSalesPrice',
        'ProductMoveDetails',
        'MostSalesProducts',
        'ProfitSalesProduct',
        'ClientAccountStatement',
        'ClientsStatement',
        'VendorAccountStatement',
        'VendorsStatement',
        'EmpGoals',
        'InventorySerial',
        'TotalExpensesSafes',
        'SubIncomList',
        'ExpensesList',
        'StoresBalances',
        'StoresBalancesCat',
        'ItemCost',
        'StoresInventoryy',
        'DelegateSalesDetailss',
        'ProfitDelegateSalesDetails',
        'InstallmentCompaniesSales',
        'StoresCosts',
        'DailyClosingDetails',
        'SalesProsMoreDetails',
        'StagnantItemss',
        'SalesCustomersGroups',
        'BrandsSales',
        'Customer_Debts',
        'Vendor_Debts',
        'MaintanceSalesReport',
        'Maintenance_Tune',
        'ProfitGroupsReport',
        'IncomListReport',

    ];
}

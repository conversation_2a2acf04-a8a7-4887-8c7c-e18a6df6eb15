<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HolidaysTypes extends Model
{
    use HasFactory;
        protected $table = 'holidays_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Days',
        'From_Date',
        'To_Date',
    ];
    
        public function Holidays()
    {
        return $this->hasOne(Holidays::class);
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesSubscribes extends Model
{
    use HasFactory;
     protected $table = 'sales_subscribes';
      protected $fillable = [

        'Code',                     
        'Date',
        'Draw',
        'Payment_Method',
        'Note',
        'Product_Numbers',
        'Total_Price',
        'Safe',
        'Account',
        'Delegate',
        'Coin',
        'Later_Due',
        'User',


    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }

          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }

          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    

    
}

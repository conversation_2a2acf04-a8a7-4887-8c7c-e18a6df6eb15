<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BarcodeProducts extends Model
{
    use HasFactory;
             protected $table = 'barcode_products';
      protected $fillable = [

                    'Name',
                    'Code',
                    'Qty',
                    'Price',
                    'Group',
                    'Product',
                    'V1',
                    'V2',
                    'Unit',
      
          
    ];

    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }

             public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
             public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
             public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
}

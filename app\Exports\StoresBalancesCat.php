<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use App\Models\Stores;
use DB;
class StoresBalancesCat implements FromCollection ,WithHeadings , WithChunkReading
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      set_time_limit(0);

        $storex=$this->store;
        $storee =  $storex['store'];
         $group = $storex['group'] ;
        $brand =  $storex['brand'] ;
         $branch = $storex['branch'] ;
        $product_Name =   $storex['product_Name'] ;
         $product_Code = $storex['product_Code'] ;
         $zero = $storex['zero'] ;
        
         if(app()->getLocale() == 'ar' ){ 
                if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
->select('stores.Name as StoreName'
         ,'products_qties.P_Ar_Name'
         ,'products.Space'
         ,'products.Storage'
         ,'products.Processor'
         ,'products.Camera'
         ,'products.Screen'
         ,'products.OS'
         ,'products.Battery'
         ,'products.Warranty'
         ,'products.Color'
         ,'products.Category'
         ,'products.Model'
         ,'products_qties.P_Code'
         ,'products_qties.Qty'
        )
->orderBy('products_qties.Store','asc')  
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
      
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->select('stores.Name as StoreName'
         ,'products_qties.P_Ar_Name'
         ,'products.Space'
         ,'products.Storage'
         ,'products.Processor'
         ,'products.Camera'
         ,'products.Screen'
         ,'products.OS'
         ,'products.Battery'
         ,'products.Warranty'
         ,'products.Color'
         ,'products.Category'
         ,'products.Model'
         ,'products_qties.P_Code'
         ,'products_qties.Qty'
        )
->orderBy('products_qties.Store','asc')              
                  ->get();
        
      }
      
         }else{
             
             
           if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
->select('stores.NameEn as StoreName'
         ,'products_qties.P_En_Name'
         ,'products.Space'
         ,'products.Storage'
         ,'products.Processor'
         ,'products.Camera'
         ,'products.Screen'
         ,'products.OS'
         ,'products.Battery'
         ,'products.Warranty'
         ,'products.Color'
         ,'products.Category'
         ,'products.Model'
         ,'products_qties.P_Code'
         ,'products_qties.Qty'
        )
->orderBy('products_qties.Store','asc')  
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
      
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->select('stores.NameEn as StoreName'
         ,'products_qties.P_En_Name'
         ,'products.Space'
         ,'products.Storage'
         ,'products.Processor'
         ,'products.Camera'
         ,'products.Screen'
         ,'products.OS'
         ,'products.Battery'
         ,'products.Warranty'
         ,'products.Color'
         ,'products.Category'
         ,'products.Model'
         ,'products_qties.P_Code'
         ,'products_qties.Qty'
        )
->orderBy('products_qties.Store','asc')              
                  ->get();
        
      }    
             
         }

        return collect($prods);
    }
         

    public function headings(): array
    {
                 return [
          'Store',
          'Product Name',
          'Space',
          'Storage',
          'Processor',
          'Camera',
          'Screen',
          'OS',
          'Battery',
          'Warranty',
          'Color',
          'Category',
          'Model',
          'Product Code',
          'Qty',

        ];
            
    
    }
    
            public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    } 

}

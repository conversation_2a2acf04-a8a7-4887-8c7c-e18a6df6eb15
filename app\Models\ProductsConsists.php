<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsConsists extends Model
{
    use HasFactory;
        protected $table = 'products_consists';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Qty',
        'Consist',
        'Price',
        'Total',
        'Product',
        'V1',
        'V2',
        'Unit',
        'Consist_ID',
       
    ];


    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
    
    
}

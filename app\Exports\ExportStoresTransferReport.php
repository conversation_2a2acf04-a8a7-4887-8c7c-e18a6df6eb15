<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\StorsTransfers;
use DB;
class ExportStoresTransferReport implements FromCollection ,WithHeadings 
{
 
    
     private $coin=[] ;

    public function __construct($coin=0) 
    {
        $this->coin = $coin;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
 

        $storex=$this->coin;

         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $coinn=$storex['coin'];
         $code=$storex['code'];
         $value=$storex['value'];
         $qty=$storex['qty'];
         $fromStore=$storex['fromStore'];
         $toStore=$storex['toStore'];
         $delegate=$storex['delegate'];
         $user=$storex['user'];
         $shipping_Company=$storex['shipping_Company'];
         $types=$storex['types'];
        

            if(app()->getLocale() == 'ar' ){  
         $prods = DB::table('stors_transfers')->whereBetween('stors_transfers.Date',[$from,$to])
             
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('stors_transfers.Code', $code);
    })
          

          
         ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('stors_transfers.Branch', $branch);
    })
          
                  ->when(!empty($coinn), function ($query) use ($coinn) {
        return $query->where('stors_transfers.Coin', $coinn);
    })
          
                         ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('stors_transfers.Status', $types);
    })
          
    
          
                    ->when(!empty($value), function ($query) use ($value) {
        return $query->where('stors_transfers.Total','>=', $value);
    })
          
          
                  ->when(!empty($qty), function ($query) use ($qty) {
        return $query->where('stors_transfers.TotalQty','>=', $qty);
    })
          
                                   ->when(!empty($fromStore), function ($query) use ($fromStore) {
        return $query->whereIn('stors_transfers.From_Store', $fromStore);
    })    
          
                   ->when(!empty($toStore), function ($query) use ($toStore) {
        return $query->whereIn('stors_transfers.To_Store', $toStore);
    })  
          
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('stors_transfers.User', $user);
    })   
          
                  ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whewhereInre('stors_transfers.Ship', $shipping_Company);
    })   
          
                          ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('stors_transfers.Delegate', $delegate);
    })   
    
             
            ->leftJoin('stores', function ($join) {
    
            $join->on('stors_transfers.From_Store', '=', 'stores.id');
         
        })
                  
        ->leftJoin('stores as TO', function ($join) {
    
            $join->on('stors_transfers.To_Store', '=', 'stores.id');
        }) 
         
         ->leftJoin('admins', function ($join) {
    
            $join->on('stors_transfers.User', '=', 'admins.id');
        })
             
                 ->leftJoin('coins', function ($join) {
    
            $join->on('stors_transfers.Coin', '=', 'coins.id');
        })
        
             
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        }) 
             
             ->leftJoin('employesses', function ($join) {
    
            $join->on('stors_transfers.Delegate', '=', 'employesses.id');
        })
             
              ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('stors_transfers.Ship', '=', 'shipping_companies.id');
        }) 
              
        ->select('stors_transfers.Date'
         ,'stors_transfers.Code'
         ,'stors_transfers.Time'
         ,'stores.Name as FromStore'
         ,'TO.NameEn as ToStore'
         ,'admins.name as User'
         ,'coins.Arabic_Name as Coin'
         ,'shipping_companies.Name as Shipping'
         ,'stors_transfers.Note'
         ,'employesses.Name as Delegate'
         ,'branches.Arabic_Name as Branch'
         ,'stors_transfers.Status'
        )

                  ->get();

            }else{
                
             $prods = DB::table('stors_transfers')->whereBetween('stors_transfers.Date',[$from,$to])
             
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('stors_transfers.Code', $code);
    })
          

          
         ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('stors_transfers.Branch', $branch);
    })
          
                  ->when(!empty($coinn), function ($query) use ($coinn) {
        return $query->where('stors_transfers.Coin', $coinn);
    })
          
                         ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('stors_transfers.Status', $types);
    })
          
    
          
                    ->when(!empty($value), function ($query) use ($value) {
        return $query->where('stors_transfers.Total','>=', $value);
    })
          
          
                  ->when(!empty($qty), function ($query) use ($qty) {
        return $query->where('stors_transfers.TotalQty','>=', $qty);
    })
          
                                   ->when(!empty($fromStore), function ($query) use ($fromStore) {
        return $query->whereIn('stors_transfers.From_Store', $fromStore);
    })    
          
                   ->when(!empty($toStore), function ($query) use ($toStore) {
        return $query->whereIn('stors_transfers.To_Store', $toStore);
    })  
          
                    ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('stors_transfers.User', $user);
    })   
          
                  ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whewhereInre('stors_transfers.Ship', $shipping_Company);
    })   
          
                          ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('stors_transfers.Delegate', $delegate);
    })   
    
             
            ->leftJoin('stores', function ($join) {
    
            $join->on('stors_transfers.From_Store', '=', 'stores.id');
         
        })
                  
        ->leftJoin('stores as TO', function ($join) {
    
            $join->on('stors_transfers.To_Store', '=', 'stores.id');
        }) 
         
         ->leftJoin('admins', function ($join) {
    
            $join->on('stors_transfers.User', '=', 'admins.id');
        })
             
                 ->leftJoin('coins', function ($join) {
    
            $join->on('stors_transfers.Coin', '=', 'coins.id');
        })
        
             
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        }) 
             
             ->leftJoin('employesses', function ($join) {
    
            $join->on('stors_transfers.Delegate', '=', 'employesses.id');
        })
             
              ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('stors_transfers.Ship', '=', 'shipping_companies.id');
        }) 
              
        ->select('stors_transfers.Date'
         ,'stors_transfers.Code'
         ,'stors_transfers.Time'
         ,'stores.NameEn as FromStore'
         ,'TO.Name as ToStore'
         ,'admins.nameEn as User'
         ,'coins.English_Name as Coin'
         ,'shipping_companies.NameEn as Shipping'
         ,'stors_transfers.Note'
         ,'employesses.NameEn as Delegate'
         ,'branches.English_Name as Branch'
         ,'stors_transfers.Status'
        )

                  ->get();
     
                
                
            }
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Time',
          'From_Store',
          'To_Store',
          'User',
          'Coin',
          'Shipping',
          'Note',
          'Delegate',
          'Branch',
          'Type'
        ];
    }

}

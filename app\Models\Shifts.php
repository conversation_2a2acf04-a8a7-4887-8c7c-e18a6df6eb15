<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shifts extends Model
{
    use HasFactory;
         protected $table = 'shifts';
      protected $fillable = [
        'Code',
        'Date',
        'Note',
        'Total_Cash',
        'Total_Later',
        'Total_Visa',
        'Total',
        'Pass',
        'Amount',
        'Emp',
        'User',
        'Close',
        'Total_Recipt',
        'Total_Payment',
        'Store',
        'Safe',
    ];
    
            public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }

               public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
                   public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    

}

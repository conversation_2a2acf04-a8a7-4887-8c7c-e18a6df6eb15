<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExchangeCommissions extends Model
{
    use HasFactory;
      protected $table = 'exchange_commissions';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Note',
        'Draw',
        'Amount',
        'Commision',
        'Total_Exchange_Commision',
        'Pre_Sales',
        'Pre_Execu',
        'Return_Maintaince',
        'Safe',
        'Coin',
        'Cost_Center',
        'Emp',
        'User',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }

    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
}

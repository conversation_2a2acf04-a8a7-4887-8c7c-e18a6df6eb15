<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSafesBanksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('safes_banks', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Date');
            $table->string('Name');
            $table->string('Type');
            $table->string('Note')->nullable();
            $table->integer('Account');
            $table->integer('User');
            $table->timestamps();
            $table->string('Branch');
            $table->string('Service_Fee')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('safes_banks');
    }
}
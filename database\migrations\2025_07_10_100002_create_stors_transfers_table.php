<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStorsTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stors_transfers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Date');
            $table->string('Draw')->nullable();
            $table->string('Total')->nullable();
            $table->string('TotalQty')->nullable();
            $table->text('Note')->nullable();
            $table->string('From_Store');
            $table->string('To_Store');
            $table->string('Coin');
            $table->string('Cost_Center')->nullable();
            $table->string('User');
            $table->integer('Status')->default(0);
            $table->string('Ship')->nullable();
            $table->string('CostShip')->nullable();
            $table->string('RecivedShip')->nullable();
            $table->string('OldQty')->nullable();
            $table->string('Edit')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Time');
            $table->string('Branch');
            $table->string('Total_Cost')->nullable();
            $table->string('TypeTransfer')->nullable();
            $table->string('Cost_Store')->nullable();
            $table->string('File')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stors_transfers');
    }
}

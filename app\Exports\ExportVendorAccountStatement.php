<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Purchases;
use App\Models\GeneralDaily;
use DB;
class ExportVendorAccountStatement implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->from;

         $from=$storex['from'];
         $to=$storex['to'];
         $cost_Center=$storex['cost_Center'];
         $coin=$storex['coin'];
         $client=$storex['client'];
         $payment_Method=$storex['payment_Method'];
         $types=$storex['types'];

        
           if(app()->getLocale() == 'ar' ){ 
 if($payment_Method != null){
             $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المشتريات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('purchases', function ($join) {
                  $storex=$this->from;
     $payment_Method=$storex['payment_Method'];
            $join->on('general_dailies.Code_Type', '=', 'purchases.Code')
                ->where('purchases.Payment_Method','=',$payment_Method);
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
       
        })    
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('purchases.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'purchases.Time'
         ,'general_dailies.Code_Type'
         ,'purchases.Refernce_Number'
         ,'branches.Arabic_Name as BranchName'
         ,'acccounting_manuals.Name as AccountName'
         ,'stores.Name as StoreName'
         ,'safes_banks.Name as SafeName'
         ,'general_dailies.Type'
           ,'purchases.Total_Price'
           ,'purchases.Total_Discount'
           ,'purchases.Total_Taxes'
           ,'purchases.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'purchases.Due_Date'
         ,'coins.Arabic_Name as CoinName'
          ,'employesses.Name as DelegateName'
          ,'admins.name as UserName'
           ,'shipping_companies.Name as ShippingName'
           ,'cost_centers.Arabic_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();
        }else{
               $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المشتريات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('purchases', function ($join) {
    
            $join->on('general_dailies.Code_Type', '=', 'purchases.Code');
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
       
        })    
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('purchases.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'purchases.Time'
         ,'general_dailies.Code_Type'
         ,'purchases.Refernce_Number'
         ,'branches.Arabic_Name as BranchName'
         ,'acccounting_manuals.Name as AccountName'
         ,'stores.Name as StoreName'
         ,'safes_banks.Name as SafeName'
         ,'general_dailies.Type'
           ,'purchases.Total_Price'
           ,'purchases.Total_Discount'
           ,'purchases.Total_Taxes'
           ,'purchases.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'purchases.Due_Date'
         ,'coins.Arabic_Name as CoinName'
          ,'employesses.Name as DelegateName'
          ,'admins.name as UserName'
           ,'shipping_companies.Name as ShippingName'
           ,'cost_centers.Arabic_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();

        }
           }else{
               
        if($payment_Method != null){
             $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المشتريات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('purchases', function ($join) {
                  $storex=$this->from;
     $payment_Method=$storex['payment_Method'];
            $join->on('general_dailies.Code_Type', '=', 'purchases.Code')
                ->where('purchases.Payment_Method','=',$payment_Method);
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
       
        })    
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('purchases.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'purchases.Time'
         ,'general_dailies.Code_Type'
         ,'purchases.Refernce_Number'
         ,'branches.English_Name as BranchName'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'stores.NameEn as StoreName'
         ,'safes_banks.NameEn as SafeName'
         ,'general_dailies.Type'
           ,'purchases.Total_Price'
           ,'purchases.Total_Discount'
           ,'purchases.Total_Taxes'
           ,'purchases.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'purchases.Due_Date'
         ,'coins.English_Name as CoinName'
          ,'employesses.NameEn as DelegateName'
          ,'admins.nameEn as UserName'
           ,'shipping_companies.NameEn as ShippingName'
           ,'cost_centers.English_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();
        }else{
               $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المشتريات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('purchases', function ($join) {
    
            $join->on('general_dailies.Code_Type', '=', 'purchases.Code');
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
       
        })    
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('purchases.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'purchases.Time'
         ,'general_dailies.Code_Type'
         ,'purchases.Refernce_Number'
         ,'branches.English_Name as BranchName'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'stores.NameEn as StoreName'
         ,'safes_banks.NameEn as SafeName'
         ,'general_dailies.Type'
           ,'purchases.Total_Price'
           ,'purchases.Total_Discount'
           ,'purchases.Total_Taxes'
           ,'purchases.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'purchases.Due_Date'
         ,'coins.English_Name as CoinName'
          ,'employesses.NameEn as DelegateName'
          ,'admins.nameEn as UserName'
           ,'shipping_companies.NameEn as ShippingName'
           ,'cost_centers.English_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();

        }       
               
           }
        

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
         'Date',
          'Time',
          'Code',
          'Refernce_Number',
          'Branch',
          'Vendor',
          'Store',
          'Safe',
          'Type',
          'Total_Price',
          'Total_Discount',
          'Total_Tax',
          'Pay',
          'Total_Creditor',
          'Total_Debitor',
          'Due_Date',
          'Coin',
          'Delegate',
          'User',
          'Shipping_Compaines',
          'Cost_Center',
          'Note'
        
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExaminationsTypes extends Model
{
    use HasFactory;
          protected $table = 'examinations_types';
      protected $fillable = [
                'Name',
                'NameEn',
                'Allow_From',
                'Allow_To',
                'Unit',
    ];
    
        public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
  
}

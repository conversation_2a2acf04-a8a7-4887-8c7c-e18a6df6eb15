<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\AcccountingManual;
use App\Models\Admin;
use App\Models\Governrate;
use App\Models\City;
use App\Models\Activites;
use App\Models\ClientStatus;
use App\Models\Platforms;
use App\Models\Campaigns;
use App\Models\Customers;
use App\Models\CustomersFiles;
use App\Models\Employess;
use App\Models\CustomersTickets;
use App\Models\InterviewsTypes;
use App\Models\Interviews;
use App\Models\Projects;
use App\Models\ProjectTeam;
use App\Models\Missions;
use App\Models\Countris;
use App\Models\CrmDefaultData;
use App\Models\ShippingCompany;
use App\Models\Places;
use App\Models\Competitors;
use App\Models\CustomerFollowUp;
use App\Models\Event;
use App\Models\Notifications;
use DB;
use Str;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;


class CRMController extends Controller
{

function __construct()
{

$this->middleware('permission:المحافظه', ['only' => ['GovernratePage','AddGovernrate','EditGovernrate','DeleteGovernrate','CityPage','AddCity','EditCity','DeleteCity']]);
$this->middleware('permission:النشاطات', ['only' => ['ActivitesPage','AddActivites','EditActivites','DeleteActivites']]);
$this->middleware('permission:حالات العملاء', ['only' => ['Clients_StatusPage','AddClients_Status','EditClients_Status','DeleteClients_Status']]);
$this->middleware('permission:المنصات', ['only' => ['PlatformsPage','AddPlatforms','EditPlatforms','DeletePlatforms','CampaignsPage','AddCampaigns','EditCampaigns','DeleteCampaigns']]);
$this->middleware('permission:انواع المقابلات', ['only' => ['Interviews_TypesPage','AddInterviews_Types','EditInterviews_Types','DeleteInterviews_Types']]);
$this->middleware('permission:المقابلات', ['only' => ['InterviewsPage','AddInterviews','EditInterviews','DeleteInterviews']]);
$this->middleware('permission:مقابلاتي', ['only' => ['MyMettingsPage']]);
$this->middleware('permission:المشاريع', ['only' => ['ProjectsPage']]);
$this->middleware('permission:اضافه مشروع', ['only' => ['AddProjects']]);
$this->middleware('permission:تعديل مشروع', ['only' => ['EditProjects']]);
$this->middleware('permission:حذف مشروع', ['only' => ['DeleteProjects']]);
$this->middleware('permission:المهام', ['only' => ['MissionsPage']]);
$this->middleware('permission:اضافه مهمه', ['only' => ['AddMissions']]);
$this->middleware('permission:تعديل مهمه', ['only' => ['EditMissions']]);
$this->middleware('permission:حذف مهمه', ['only' => ['DeleteMissions']]);
$this->middleware('permission:مهامي', ['only' => ['MyMissions']]);
$this->middleware('permission:المنافسين', ['only' => ['Competitors']]);
$this->middleware('permission:متابعه العملاء', ['only' => ['Customerـfollowـup']]);

}    
    
    
      //======  Governrate ======= 
        public function GovernratePage(){
        $items=Governrate::all();
        $Countris=Countris::all();
         return view('admin.CRM.Governrate',['items'=>$items,'Countris'=>$Countris]);
    }
    
     public function AddGovernrate(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Country']=request('Country');
         $data['SearchCode']=request('SearchCode');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Governrate::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المحافظات';
           $dataUser['ScreenEn']='Governrate';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditGovernrate($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Country']=request('Country');
         $data['SearchCode']=request('SearchCode');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Governrate::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المحافظات';
           $dataUser['ScreenEn']='Governrate';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteGovernrate($id){
               
          $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->Governrate == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
               $Count=City::where('Gov',$id)->count();
         
         if($Count == 0){
 
         
        $del=Governrate::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='المحافظات';
           $dataUser['ScreenEn']='Governrate';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();
             
                }else{
                 session()->flash('error',trans('admin.CantDeleteThisItem'));
        return back();  
             
         }

           }
    
    
              //======  City ======= 
        public function CityPage($id){
        $items=City::where('Gov',$id)->get();
        $Shippings=ShippingCompany::all();
         return view('admin.CRM.City',['items'=>$items,'Gov'=>$id,'Shippings'=>$Shippings]);
    }
    
     public function AddCity(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Gov']=request('Gov');
         $data['SearchCode']=request('SearchCode');
         $data['Ship_Price']=request('Ship_Price');
         $data['Shipping_Company']=request('Shipping_Company');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         City::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المدن';
           $dataUser['ScreenEn']='City';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCity($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
          $data['Gov']=request('Gov');
          $data['Ship_Price']=request('Ship_Price');
          $data['SearchCode']=request('SearchCode');
          $data['Shipping_Company']=request('Shipping_Company');
           City::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='المدن';
           $dataUser['ScreenEn']='City';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCity($id){
               
                   $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->City == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
         
                $Count=Places::where('City',$id)->count();
         
         if($Count == 0){

        $del=City::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='المدن';
           $dataUser['ScreenEn']='City';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();
             
       }else{
                 session()->flash('error',trans('admin.CantDeleteThisItem'));
        return back();  
             
         }         

           }
    
              //======  Places ======= 
        public function PlacesPage($id){
        $items=Places::where('City',$id)->get();
          $Employess = Employess::
             where('Emp_Type','Delivery')
                  ->where("EmpSort",1)->where('Active',1)
              ->get();  
            
         return view('admin.CRM.Places',['items'=>$items,'City'=>$id,'Employess'=>$Employess]);
    }
    
     public function AddPlaces(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['City']=request('City');
         $data['Delivery']=request('Delivery');
         $data['Ship_Price']=request('Ship_Price');
         $data['SearchCode']=request('SearchCode');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Places::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='المناطق';
           $dataUser['ScreenEn']='Places';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditPlaces($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
          $data['City']=request('City');
          $data['Ship_Price']=request('Ship_Price');
          $data['Delivery']=request('Delivery');
          $data['SearchCode']=request('SearchCode');
           Places::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='المناطق';
           $dataUser['ScreenEn']='Places';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         

            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeletePlaces($id){
   
         
             $Crms=CompanyData::orderBy('id','desc')->first();
           if($Crms->Place == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
        $del=Places::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                     $dataUser['Screen']='المناطق';
           $dataUser['ScreenEn']='Places';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
        
              //======  Activites ======= 
        public function ActivitesPage(){
        $items=Activites::all();
         return view('admin.CRM.Activites',['items'=>$items]);
    }
    
     public function AddActivites(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Activites::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='النشاطات';
           $dataUser['ScreenEn']='Activites';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditActivites($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Activites::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='النشاطات';
           $dataUser['ScreenEn']='Activites';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteActivites($id){
          
                        $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->Activity == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
         
        $del=Activites::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='النشاطات';
           $dataUser['ScreenEn']='Activites';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
        //======  Clients Status ======= 
    public function Clients_StatusPage(){
        $items=ClientStatus::all();
         return view('admin.CRM.ClientStatus',['items'=>$items]);
    }
    
     public function AddClients_Status(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         ClientStatus::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='حالات العملاء';
           $dataUser['ScreenEn']='Clients Status';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditClients_Status($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           ClientStatus::where('id',$id)->update($data);
         
            $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='حالات العملاء';
           $dataUser['ScreenEn']='Clients Status';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteClients_Status($id){
           
                                $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->ClientStatus == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
         
        $del=ClientStatus::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='حالات العملاء';
           $dataUser['ScreenEn']='Clients Status';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
            //======  Platforms ======= 
        public function PlatformsPage(){
        $items=Platforms::all();
         return view('admin.CRM.Platforms',['items'=>$items]);
    }
    
     public function AddPlatforms(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Platforms::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
      
                     $dataUser['Screen']='المنصات';
           $dataUser['ScreenEn']='Platforms';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditPlatforms($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Platforms::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                            $dataUser['Screen']='المنصات';
           $dataUser['ScreenEn']='Platforms';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeletePlatforms($id){
           
                                     $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->Platforms == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
         
                    $Count=Campaigns::where('Platform',$id)->count();
         
         if($Count == 0){

   
        $del=Platforms::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='المنصات';
           $dataUser['ScreenEn']='Platforms';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();
             
              }else{
                 session()->flash('error',trans('admin.CantDeleteThisItem'));
        return back();  
             
         }   

           }
    
    
              //======  Campaigns ======= 
        public function CampaignsPage($id){
        $items=Campaigns::where('Platform',$id)->get();
         return view('admin.CRM.Campaigns',['items'=>$items,'Platform'=>$id]);
    }
    
     public function AddCampaigns(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Platform']=request('Platform');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Campaigns::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                             $dataUser['Screen']='الحملات';
           $dataUser['ScreenEn']='Campaigns';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCampaigns($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
          $data['Platform']=request('Platform');
           Campaigns::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                        $dataUser['Screen']='الحملات';
           $dataUser['ScreenEn']='Campaigns';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCampaigns($id){
          
                                        $Crms=CrmDefaultData::orderBy('id','desc')->first();
           if($Crms->Campagin == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
         
        $del=Campaigns::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                               $dataUser['Screen']='الحملات';
           $dataUser['ScreenEn']='Campaigns';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
                  //======  Interviews Types ======= 
        public function Interviews_TypesPage(){
        $items=InterviewsTypes::all();
         return view('admin.CRM.InterviewsTypes',['items'=>$items]);
    }
    
     public function AddInterviews_Types(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         InterviewsTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='انواع المقابلات';
           $dataUser['ScreenEn']='Interviews Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditInterviews_Types($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           InterviewsTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='انواع المقابلات';
           $dataUser['ScreenEn']='Interviews Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteInterviews_Types($id){
                      
        $del=InterviewsTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='انواع المقابلات';
           $dataUser['ScreenEn']='Interviews Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    //======  Interviews  ======= 
    public function InterviewsPage(){
        $items=Interviews::orderBy('id','desc')->paginate(100);
        $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();
        $InterviewsTypes=InterviewsTypes::all();
        $Users=Admin::all();
                $Governrates=Governrate::all();
            $Activites=Activites::all();
      $Platforms=Platforms::all();
        
         $Cities=City::all();
            $Campaigns=Campaigns::all();
        
              $res=Interviews::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }

        
         return view('admin.CRM.Interviews',[
             'items'=>$items,
             'Employess'=>$Employess,
             'Code'=>$Code,
             'Governrates'=>$Governrates,
             'Activites'=>$Activites,
             'Platforms'=>$Platforms,
             'Users'=>$Users,
             'Cities'=>$Cities,
             'Campaigns'=>$Campaigns,
             'InterviewsTypes'=>$InterviewsTypes,
         ]);
    }
    
     public function AddInterviews(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Emp'=>'required',
             'Client'=>'required',
             'Type'=>'required',
     
             
               ],[
            'Code.required' => trans('admin.CodeRequired'),    
        
     
         ]);
   
         
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Time']=request('Time');
         $data['Status']=0;
         $data['Rate']=0;
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Client']=request('Client');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
         

         Interviews::create($data);
                 $cli=Customers::find(request('Client'));
         
                  $event['Start_Date']=request('Date');
         $event['End_Date']=request('Date');
         $event['Event_Ar_Name']='مقابلة';
         $event['Event_En_Name']='Meet';
         $event['Type']='المقابلات';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Emp');
         $event['Client']=$cli->Account;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
          
           
         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مقابلة جديدة';
         $notii['Noti_En_Name']='New Meet';
         $notii['Type']='المقابلات';
  $notii['TypeEn']='Interviews';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Emp');
         $notii['Client']=$cli->Account;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
             
                notify()->success(trans('admin.NewMeet'));

            
              
               

         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
            return redirect('Interviews');
        
    }
    
     public function EditInterviews($id){ 
         
            $data= $this->validate(request(),[
             'Code'=>'required',
             'Emp'=>'required',
             'Client'=>'required',
             'Type'=>'required',
     
             
               ],[
            'Code.required' => trans('admin.CodeRequired'),    
        
     
         ]);
   
         
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Time']=request('Time');
         $data['Status']=request('Status');
         $data['Rate']=request('Rate');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Client']=request('Client');
         $data['Type']=request('Type');
         $data['User']=request('User');
         

           Interviews::where('id',$id)->update($data);
              $del=Interviews::find($id);
         
            Event::where('Type_Code',$del->Code)->where('Type','المقابلات')->delete();
            Notifications::where('Type_Code',$del->Code)->where('Type','المقابلات')->delete(); 
         
         
           $cli=Customers::find(request('Client'));

         
                $event['Start_Date']=request('Date');
         $event['End_Date']=request('Date');
         $event['Event_Ar_Name']='مقابلة';
         $event['Event_En_Name']='Meet';
         $event['Type']='المقابلات';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=request('Emp');
         $event['Client']=$cli->Account;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
         
         
                  $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مقابلة جديدة';
         $notii['Noti_En_Name']='New Meet';
         $notii['Type']='المقابلات';
  $notii['TypeEn']='Interviews';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Emp');
         $notii['Client']=$cli->Account;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
             
                notify()->success(trans('admin.NewMeet'));

         
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
             return redirect('Interviews');
     
     
     }
    
     public function DeleteInterviews($id){
                      
        $del=Interviews::find($id);
            Event::where('Type_Code',$del->Code)->where('Type','المقابلات')->delete();
           Notifications::where('Type_Code',$del->Code)->where('Type','المقابلات')->delete(); 
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delet';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
      public function NotDone($id){
        
          
        Interviews::where('id',$id)->update(['Status'=>0,'StatusNote'=>null]);  

        $del=Interviews::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                              $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='لم تكتمل';
           $dataUser['TypeEn']='Not Done';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
  
        session()->flash('error',trans('admin.Not_Done'));
        return back();

           }
    
     public function Done($id){
        
          
        Interviews::where('id',$id)->update(['Status'=>1,'StatusNote'=>request('StatusNote')]);  
         
         
         
         
         

        $del=Interviews::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='اكتملت';
           $dataUser['TypeEn']='Done';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
  
        session()->flash('success',trans('admin.Done'));
        return back();

           }
    
    public function Rate($id){
        
          
        Interviews::where('id',$id)->update(['Rate'=>request('Rate')]);  

        $del=Interviews::find($id);
         
  
        session()->flash('success',trans('admin.Rated'));
        return back();

           } 

      public function AllClients() {

             if(app()->getLocale() == 'ar' ){ 
       $states = Customers::orderBy('Code','asc')->pluck("Name","id");
             }else{
        $states = Customers::orderBy('Code','asc')->pluck("NameEn","id");            
             }
   
       return response()->json($states);
           
    }
    
     public function FilterInterviews(){
        $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();
        $InterviewsTypes=InterviewsTypes::all();
        $Users=Admin::all();
        
              $res=Interviews::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }

         
         if(request('Code') != null){
            
        $c= 'Code' ; 
        $cR=  request('Code');        
         $cName= "where" ;    
             
         }else{

         $c= 'id' ; 
        $cR=  'asc' ;      
          $cName=   "orderBy" ;
         }
          
          if(request('Status') != null){
            
        $S= 'Status' ; 
        $SR=  request('Status');        
         $SName= "where" ;    
             
         }else{

         $S= 'id' ; 
        $SR=  'asc' ;      
          $SName=   "orderBy" ;
         }
         
          if(request('Rateee') != null){
            
        $R= 'Rate' ; 
        $RR=  request('Rateee');        
         $RName= "where" ;    
             
         }else{

         $R= 'id' ; 
        $RR=  'asc' ;      
          $RName=   "orderBy" ;
         }
         
          if(request('Emp') != null){
            
        $E= 'Emp' ; 
        $ER=  request('Emp');        
         $EName= "where" ;    
             
         }else{

         $E= 'id' ; 
        $ER=  'asc' ;      
          $EName=   "orderBy" ;
         }
         
           if(request('Client') != null){
            
        $C= 'Client' ; 
        $CR=  request('Client');        
         $CName= "where" ;    
             
         }else{

         $C= 'id' ; 
        $CR=  'asc' ;      
          $CName=   "orderBy" ;
         }
 
           if(request('Type') != null){
            
        $T= 'Type' ; 
        $TR=  request('Type');        
         $TName= "where" ;    
             
         }else{

         $T= 'id' ; 
        $TR=  'asc' ;      
          $TName=   "orderBy" ;
         }
         
         if(request('User') != null){
            
        $U= 'User' ; 
        $UR=  request('User');        
         $UName= "where" ;    
             
         }else{

         $U= 'id' ; 
        $UR=  'asc' ;      
          $UName=   "orderBy" ;
         }
         
             $items=Interviews::orderBy('id','asc')
             ->$cName($c,$cR)
             ->$SName($S,$SR)
             ->$RName($R,$RR)
             ->$EName($E,$ER)
             ->$CName($C,$CR)
             ->$TName($T,$TR)
             ->$UName($U,$UR)
             ->whereBetween('Date', [request('From'), request('To')]) 
             ->paginate(100);
 
  
                $Governrates=Governrate::all();
            $Activites=Activites::all();
      $Platforms=Platforms::all();
         $Cities=City::all();
            $Campaigns=Campaigns::all();
        

         return view('admin.CRM.Interviews',[
             'items'=>$items,
             'Employess'=>$Employess,
             'Code'=>$Code,
             'Users'=>$Users,
             'InterviewsTypes'=>$InterviewsTypes,
             'Governrates'=>$Governrates,
             'Activites'=>$Activites,
             'Platforms'=>$Platforms,
             'Cities'=>$Cities,
             'Campaigns'=>$Campaigns,
         ]);
    }
    
    // MyMettings
     public function MyMettingsPage(){
         
        if(auth()->guard('admin')->user()->emp == 0){
              session()->flash('error',trans('admin.Just_Emp'));
           return back(); 
        }else{ 
        $items=Interviews::orderBy('id','desc')
            ->where('Emp',auth()->guard('admin')->user()->emp)
            ->where('Status',0)
            ->paginate(100);


         return view('admin.CRM.MyMettings',[
             'items'=>$items,

         ]);
    }
    }
    
        // PerivousMettings
     public function PerivousMettings(){
         
        if(auth()->guard('admin')->user()->emp == 0){
              session()->flash('error',trans('admin.Just_Emp'));
           return back(); 
        }else{ 
        $items=Interviews::orderBy('id','desc')
            ->where('Emp',auth()->guard('admin')->user()->emp)
            ->where('Status','!=',0)
            ->paginate(100);
  $InterviewsTypes=InterviewsTypes::all();
 $Governrates=Governrate::all();
    $Cities=City::all();

         return view('admin.CRM.PerivousMettings',[
             'items'=>$items,
             'InterviewsTypes'=>$InterviewsTypes,
             'Governrates'=>$Governrates,
             'Cities'=>$Cities,

         ]);
    }
    }
    
     public function AddNEWInterviews(){
        
        $data= $this->validate(request(),[
           
             'Emp'=>'required',
             'Client'=>'required',
             'Type'=>'required',
     
             
               ],[
            'Code.required' => trans('admin.CodeRequired'),    
        
     
         ]);
   
           $res=Interviews::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
    
           
         $data['Code']=$Code;
         $data['Date']=request('Date');
         $data['Time']=request('Time');
         $data['Status']=1;
         $data['Rate']=0;
         $data['Note']=null;
         $data['StatusNote']=request('StatusNote');
         $data['Emp']=request('Emp');
         $data['Client']=request('Client');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
         

         Interviews::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المقابلات';
           $dataUser['ScreenEn']='Interviews';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
            return back();
        
    }
    
    
    
    //Add New Client by Ajax
       public function AddNewClientAjax(Request $request){
           
                  $Name=$request->get('Name');
               $Phone=$request->get('Phone');
               $PriceLevel=$request->get('PriceLevel');
               $Governrate=$request->get('Governrate');
               $City=$request->get('City');
               $Activity=$request->get('Activity');
               $Campagin=$request->get('Campagin');
               $Responsible=$request->get('Responsible');
               $Platform=$request->get('Platform');
    
           
           
            $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();    
            $codee=AcccountingManual::find(24);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                         $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=$Name;
           
                $dataX['NameEn']=request('NameEn');
         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

     
        
        $ress=Customers::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $CodeUser=$ress->Code + 1 ; 
               
           }else{
               
              $CodeUser=1; 
               
           }
  
        $data['Code']=$CodeUser;
        $data['Date']=date('Y-m-d');
        $data['Name']=$Name;
           
              $data['NameEn']=request('NameEn');    
        $data['Phone']=$Phone;
        $data['Price_Level']=$PriceLevel;
        $data['Account']=$Acc->id;           
        $data['User']=auth()->guard('admin')->user()->id;
        $data['Responsible']=$Responsible; 
        $data['Governrate']=$Governrate;
        $data['City']=$City;
        $data['Activity']=$Activity;
        $data['Campagin']=$Campagin; 
               $data['Platform']=$Platform;
           
          Customers::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='العملاء';
           $dataUser['ScreenEn']='Clients';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$Name;
              $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
            $states=['SUCEESS'=>'SUCEESS'];
           return response()->json($states);
        
    }
    
       
                 //======  Projects ======= 
        public function ProjectsPage(){
        $items=Projects::paginate(100);
            $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();    
         return view('admin.CRM.Projects',['items'=>$items,'Employess'=>$Employess]);
    }
    
     public function AddProjects(){
        
        $data= $this->validate(request(),[
             'Name'=>'required',
             'Duration'=>'required',
             'Value'=>'required',
             'Client'=>'required',
             'Manager'=>'required',
             'Start_Date' => 'required|date|after:yesterday',
             'End_Date' => 'required|date|after_or_equal:Start',

     
             
               ],[
            'Name.required' => trans('admin.NameRequired'),    
            'Duration.required' => trans('admin.DurationRequired'),    
            'Value.required' => trans('admin.ValueRequired'),    
            'Client.required' => trans('admin.ClientRequired'),    
            'Manager.required' => trans('admin.ManagerRequired'),    
    'Start_Date.date' => trans('admin.Startdate'),  
            'Start_Date.after' => trans('admin.Startafter'),  
            'End_Date.required' => trans('admin.EndRequired'),  
            'End_Date.date' => trans('admin.Enddate'),  
            'End_Date.after_or_equal' => trans('admin.Endafter_or_equal'),  
     
         ]);
   
                 $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ProjectsFile/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=null;
             }

           
         $data['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }


         $data['Start_Date']=request('Start_Date');
         $data['End_Date']=request('End_Date');
         $data['Duration']=request('Duration');
         $data['Value']=request('Value');
         $data['Client']=request('Client');
         $data['Manager']=request('Manager');
         $data['Status']=0;
         $data['User']=auth()->guard('admin')->user()->id;
   
         Projects::create($data);
         
         $last=Projects::orderBy('id','desc')->first();

           $cli=Customers::find(request('Client'));

         
         
                $event['Start_Date']=request('Start_Date');
         $event['End_Date']=request('End_Date');
         $event['Event_Ar_Name']='مشروع';
         $event['Event_En_Name']='Project';
         $event['Type']='المشاريع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Name');
         $event['Emp']=request('Manager');
         $event['Client']=$cli->Account;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
         
                  
           
         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مشروع جديد';
         $notii['Noti_En_Name']='New Project';
         $notii['Type']='المشاريع';
  $notii['TypeEn']='Projects';
         $notii['Type_Code']=request('Name');
         $notii['Emp']=request('Manager');
         $notii['Client']=$cli->Account;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
         
              notify()->success(trans('admin.New_Project'));
         

         if(!empty(request('Team'))){
             
             $Member=request('Team');
             
            for($i=0 ; $i < count($Member) ; $i++){
                
                    $team['Project']=$last->id;
         $team['Member']=$Member[$i]; 
              
                ProjectTeam::create($team);
                
            } 
 
             
         }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='المشاريع';
           $dataUser['ScreenEn']='Projects';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                      if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditProjects($id){ 
         
       
             $data= $this->validate(request(),[
             'Name'=>'required',
             'Duration'=>'required',
             'Value'=>'required',
             'Client'=>'required',
             'Manager'=>'required',
             'Start_Date' => 'required|date|after:yesterday',
             'End_Date' => 'required|date|after_or_equal:Start',

     
             
               ],[
            'Name.required' => trans('admin.NameRequired'),    
            'Duration.required' => trans('admin.DurationRequired'),    
            'Value.required' => trans('admin.ValueRequired'),    
            'Client.required' => trans('admin.ClientRequired'),    
            'Manager.required' => trans('admin.ManagerRequired'),    
    'Start_Date.date' => trans('admin.Startdate'),  
            'Start_Date.after' => trans('admin.Startafter'),  
            'End_Date.required' => trans('admin.EndRequired'),  
            'End_Date.date' => trans('admin.Enddate'),  
            'End_Date.after_or_equal' => trans('admin.Endafter_or_equal'),  
     
         ]);
   
                 $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ProjectsFile/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=request('Files');
             }

           
         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['Start_Date']=request('Start_Date');
         $data['End_Date']=request('End_Date');
         $data['Duration']=request('Duration');
         $data['Value']=request('Value');
         $data['Client']=request('Client');
         $data['Manager']=request('Manager');
         $data['Status']=request('Status');
         $data['User']=auth()->guard('admin')->user()->id;
   
         Projects::where('id',$id)->update($data);
         
         
          
              $del=Projects::find($id);
         
  Event::where('Type_Code',$del->Name)->where('Type','المشاريع')->delete();
               Notifications::where('Type_Code',$del->Name)->where('Type','المشاريع')->delete();      
         
         
         
           $cli=Customers::find(request('Client'));

                $event['Start_Date']=request('Start_Date');
         $event['End_Date']=request('End_Date');
         $event['Event_Ar_Name']='مشروع';
         $event['Event_En_Name']='Project';
         $event['Type']='المشاريع';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Name');
         $event['Emp']=request('Manager');
         $event['Client']=$cli->Account;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
                    
         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مشروع جديد';
         $notii['Noti_En_Name']='New Project';
         $notii['Type']='المشاريع';
  $notii['TypeEn']='Projects';
         $notii['Type_Code']=request('Name');
         $notii['Emp']=request('Manager');
         $notii['Client']=$cli->Account;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
         
              notify()->success(trans('admin.New_Project'));


         if(!empty(request('Team'))){
             
             ProjectTeam::where('Project',$id)->delete();
             
             $Member=request('Team');
             
            for($i=0 ; $i < count($Member) ; $i++){
                
                    $team['Project']=$id;
         $team['Member']=$Member[$i]; 
              
                ProjectTeam::create($team);
                
            } 
 
             
         }

                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='المشاريع';
           $dataUser['ScreenEn']='Projects';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteProjects($id){
                      
        $del=Projects::find($id);
         
            Event::where('Type_Code',$del->Name)->where('Type','المشاريع')->delete();
             Notifications::where('Type_Code',$del->Name)->where('Type','المشاريع')->delete();      
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='المشاريع';
           $dataUser['ScreenEn']='Projects';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
  public function EndProject($id){
                      
        Projects::where('id',$id)->update(['Status'=>1]);

        session()->flash('error',trans('admin.Updated'));
        return back();

           }    
    
    
               //======  Missions ======= 
        public function MissionsPage(){
        $items=Missions::paginate(100);
            $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();    
            $Projects=Projects::where('Status',0)->get();    
         return view('admin.CRM.Missions',['items'=>$items,'Employess'=>$Employess,'Projects'=>$Projects]);
    }
    
     public function AddMissions(){
        
        $data= $this->validate(request(),[
             'Name'=>'required',
             'Duration'=>'required',
             'Value'=>'required',
             'Task_Owner'=>'required',
             'Observer'=>'required',
             'Project'=>'required',
             'Start_Date' => 'required|date|after:yesterday',
             'End_Date' => 'required|date|after_or_equal:Start',

     
             
               ],[
            'Name.required' => trans('admin.NameRequired'),    
            'Task_Owner.required' => trans('admin.Task_OwnerRequired'),    
            'Observer.required' => trans('admin.ObserverRequired'),    
            'Project.required' => trans('admin.ProjectRequired'),    
            'Duration.required' => trans('admin.DurationRequired'),    
            'Value.required' => trans('admin.ValueRequired'),    
            'Client.required' => trans('admin.ClientRequired'),    
            'Manager.required' => trans('admin.ManagerRequired'),    
    'Start_Date.date' => trans('admin.Startdate'),  
            'Start_Date.after' => trans('admin.Startafter'),  
            'End_Date.required' => trans('admin.EndRequired'),  
            'End_Date.date' => trans('admin.Enddate'),  
            'End_Date.after_or_equal' => trans('admin.Endafter_or_equal'),  
     
         ]);
   
                 $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='MissionsFile/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=null;
             }

           
         $data['Name']=request('Name');
             if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }


         $data['Start_Date']=request('Start_Date');
         $data['End_Date']=request('End_Date');
         $data['Duration']=request('Duration');
         $data['Value']=request('Value');
         $data['Task_Owner']=request('Task_Owner');
         $data['Observer']=request('Observer');
         $data['Desc']=request('Desc');
         $data['Project']=request('Project');
         $data['Status']=0;
         $data['User']=auth()->guard('admin')->user()->id;
   
         Missions::create($data);
         
         
     
                $event['Start_Date']=request('Start_Date');
         $event['End_Date']=request('End_Date');
         $event['Event_Ar_Name']='مهمة';
         $event['Event_En_Name']='Mission';
         $event['Type']='المهام';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Name');
         $event['Emp']=request('Task_Owner');
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
         
                  $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مهمة جديدة';
         $notii['Noti_En_Name']='New Mission';
         $notii['Type']='المهام';
  $notii['TypeEn']='Missions';
         $notii['Type_Code']=request('Name');
         $notii['Emp']=request('Task_Owner');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
         
              notify()->success(trans('admin.New_Mission'));
         

    
    
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
        
                $dataUser['Screen']='المهام';
           $dataUser['ScreenEn']='Missions';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                    if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditMissions($id){ 
         
       
           $data= $this->validate(request(),[
             'Name'=>'required',
             'Duration'=>'required',
             'Value'=>'required',
             'Task_Owner'=>'required',
             'Observer'=>'required',
             'Project'=>'required',
             'Start_Date' => 'required|date|after:yesterday',
             'End_Date' => 'required|date|after_or_equal:Start',

     
             
               ],[
            'Name.required' => trans('admin.NameRequired'),    
            'Task_Owner.required' => trans('admin.Task_OwnerRequired'),    
            'Observer.required' => trans('admin.ObserverRequired'),    
            'Project.required' => trans('admin.ProjectRequired'),    
            'Duration.required' => trans('admin.DurationRequired'),    
            'Value.required' => trans('admin.ValueRequired'),    
            'Client.required' => trans('admin.ClientRequired'),    
            'Manager.required' => trans('admin.ManagerRequired'),    
    'Start_Date.date' => trans('admin.Startdate'),  
            'Start_Date.after' => trans('admin.Startafter'),  
            'End_Date.required' => trans('admin.EndRequired'),  
            'End_Date.date' => trans('admin.Enddate'),  
            'End_Date.after_or_equal' => trans('admin.Endafter_or_equal'),  
     
         ]);
   
                 $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='MissionsFile/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=null;
             }

           
         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['Start_Date']=request('Start_Date');
         $data['End_Date']=request('End_Date');
         $data['Duration']=request('Duration');
         $data['Value']=request('Value');
         $data['Task_Owner']=request('Task_Owner');
         $data['Observer']=request('Observer');
         $data['Desc']=request('Desc');
         $data['Project']=request('Project');
         $data['Status']=0;
         $data['User']=auth()->guard('admin')->user()->id;
  
         Missions::where('id',$id)->update($data);
         
         
         
               $del=Missions::find($id);
         
  Event::where('Type_Code',$del->Name)->where('Type','المهام')->delete();
                    Notifications::where('Type_Code',$del->Name)->where('Type','المهام')->delete(); 
                $event['Start_Date']=request('Start_Date');
         $event['End_Date']=request('End_Date');
         $event['Event_Ar_Name']='مهمة';
         $event['Event_En_Name']='Mission';
         $event['Type']='المهام';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Name');
         $event['Emp']=request('Task_Owner');
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
         
         
                           $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='مهمة جديدة';
         $notii['Noti_En_Name']='New Mission';
         $notii['Type']='المهام';
  $notii['TypeEn']='Missions';
         $notii['Type_Code']=request('Name');
         $notii['Emp']=request('Task_Owner');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
         
              notify()->success(trans('admin.New_Mission'));

                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='المهام';
           $dataUser['ScreenEn']='Missions';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteMissions($id){
                      
        $del=Missions::find($id);
           Event::where('Type_Code',$del->Name)->where('Type','المهام')->delete();
             Notifications::where('Type_Code',$del->Name)->where('Type','المهام')->delete(); 
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='المهام';
           $dataUser['ScreenEn']='Missions';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
       public function SureMission($id){
                      
        Missions::where('id',$id)->update(['Status'=>1]);

        session()->flash('error',trans('admin.Updated'));
        return back();

           }
    
     public function SureMissionByEmp($id){
                      
        Missions::where('id',$id)->update(['Status'=>2]);

        session()->flash('error',trans('admin.Updated'));
        return back();

           }
    
     public function AcceptMission(){
                    $id=request('ID');  
        Missions::where('id',$id)->update(['Status'=>request('Status'),'Value'=>request('Value')]);

        session()->flash('error',trans('admin.Updated'));
        return back();

           }
    
      public function MyMissions(){
        $items=Missions::where('Status',1)->where('Task_Owner',auth()->guard('admin')->user()->emp)->paginate(100);
         return view('admin.CRM.MyMissions',['items'=>$items]);
    }
    
        
                     //======  Competitors ======= 
    
        public function Competitors(){
        $items=Competitors::paginate(100);
            $Countris=Countris::all();    
         return view('admin.CRM.Competitors',['items'=>$items,'Countris'=>$Countris]);
    }
    
     public function AddCompetitors(){
        
        $data= $this->validate(request(),[
             'Name'=>'required',

             
               ],[
            'Name.required' => trans('admin.NameRequired'),    
 
     
         ]);
   
             
           
         $data['Name']=request('Name');
               if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }


         $data['Website']=request('Website');
         $data['Facebook']=request('Facebook');
         $data['Instagram']=request('Instagram');
         $data['Twitter']=request('Twitter');
         $data['Pinterest']=request('Pinterest');
         $data['Addtional_Link']=request('Addtional_Link');
         $data['Phone']=request('Phone');
         $data['Whatsapp']=request('Whatsapp');
         $data['Country']=request('Country');

   
         Competitors::create($data);
         
 

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
     
                         $dataUser['Screen']='المنافسين';
           $dataUser['ScreenEn']='Competitors';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                   if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCompetitors($id){ 
         
       
             $data= $this->validate(request(),[
             'Name'=>'required',
   
     
             
               ],[
            'Name.required' => trans('admin.NameRequired'),    

     
         ]);
   
      
           
         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['Website']=request('Website');
         $data['Facebook']=request('Facebook');
         $data['Instagram']=request('Instagram');
         $data['Twitter']=request('Twitter');
         $data['Pinterest']=request('Pinterest');
         $data['Addtional_Link']=request('Addtional_Link');
         $data['Phone']=request('Phone');
         $data['Whatsapp']=request('Whatsapp');
         $data['Country']=request('Country');

   
         Competitors::where('id',$id)->update($data);
         



                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='المنافسين';
           $dataUser['ScreenEn']='Competitors';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCompetitors($id){
                      
        $del=Competitors::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']='المنافسين';
           $dataUser['ScreenEn']='Competitors';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
                     //======  Customerـfollowـup ======= 
    
        public function Customerـfollowـup(){
        $items=CustomerFollowUp::paginate(100);
         $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
     $Employess = Employess::
                 where("EmpSort",1)->where('Active',1)
              ->get(); 
            
              $ress=CustomerFollowUp::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $Code=$ress->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
            
         return view('admin.CRM.CustomerFollowUp',['items'=>$items,'Clients'=>$Clients,'Employess'=>$Employess,'Code'=>$Code]);
    }
    
         public function FilterCustomerـfollowـup(){
            
             $From=request('From');
$To=request('To');
$Client=request('Client');
$Rate=request('Rate');
$Emp=request('Emp');
             
             
        $items=CustomerFollowUp::whereBetween('Date',[$From,$To])
             
               ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client',$Client);  
               
                })  
             
                    ->when(!empty($Rate), function ($query) use ($Rate) {
        return $query->where('Rate',$Rate);  
               
                })  
            
            
                    ->when(!empty($Emp), function ($query) use ($Emp) {
        return $query->where('Emp',$Emp);  
               
                })  
             
             
             ->paginate(100);
             
             
             
         $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
     $Employess = Employess::
                 where("EmpSort",1)->where('Active',1)
              ->get(); 
            
              $ress=CustomerFollowUp::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $Code=$ress->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
            
         return view('admin.CRM.CustomerFollowUp',['items'=>$items,'Clients'=>$Clients,'Employess'=>$Employess,'Code'=>$Code]);
    }
    
  
     public function AddCustomerـfollowـup(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Client'=>'required',
             'Subject'=>'required',
             'Emp'=>'required',

             
               ],[
     
     
         ]);
   
             
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Subject']=request('Subject');
         $data['Rate']=request('Rate');
         $data['Emp']=request('Emp');
         $data['Visit_Cost']=request('Visit_Cost');
         $data['Note']=request('Note');
   
   
         CustomerFollowUp::create($data);
         
 

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

         
                         $dataUser['Screen']='متابعه العملاء';
           $dataUser['ScreenEn']='Customer follow up';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditCustomerـfollowـup($id){ 
         
       
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Client'=>'required',
             'Subject'=>'required',
             'Emp'=>'required',

             
               ],[
     
     
         ]);
   
             
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Client']=request('Client');
         $data['Subject']=request('Subject');
         $data['Rate']=request('Rate');
         $data['Emp']=request('Emp');
         $data['Visit_Cost']=request('Visit_Cost');
         $data['Note']=request('Note');

   
         CustomerFollowUp::where('id',$id)->update($data);
         



                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                            $dataUser['Screen']='متابعه العملاء';
           $dataUser['ScreenEn']='Customer follow up';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteCustomerـfollowـup($id){
                      
        $del=CustomerFollowUp::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                                $dataUser['Screen']='متابعه العملاء';
           $dataUser['ScreenEn']='Customer follow up';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    





    
    
    
}

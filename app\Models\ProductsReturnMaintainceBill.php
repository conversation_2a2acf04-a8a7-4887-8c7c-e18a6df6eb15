<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsReturnMaintainceBill extends Model
{
    use HasFactory;
     protected $table = 'products_return_maintaince_bills';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'AvQty',
        'Qty',
        'Recived_Qty',
        'Price',
        'Discount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Exp_Date',
        'Store',          
        'Product',          
        'V1',          
        'V2',          
        'Unit',          
        'Return',  
        'Out',          
        'Date',          
        'Branch',          
        'Group',          
        'Brand',                  
        'User',          
        'Eng',          
        'Recipient',          
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
               public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Return()
    {
        return $this->belongsTo(ReturnMaintainceBill::class,'Return');
    }
    
    
    
                   public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
   public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }
    
    public function Eng()
    {
        return $this->belongsTo(Employess::class,'Eng');
    }
    public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }  
    public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }  
    public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }
}

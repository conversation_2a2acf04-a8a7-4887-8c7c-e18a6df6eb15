<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToReportsSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reports_settings', function (Blueprint $table) {
            if (!Schema::hasColumn('reports_settings', 'ProfitGroupsReport')) {
                $table->longText('ProfitGroupsReport')->nullable();
            }
            if (!Schema::hasColumn('reports_settings', 'IncomListReport')) {
                $table->longText('IncomListReport')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reports_settings', function (Blueprint $table) {
            if (Schema::hasColumn('reports_settings', 'ProfitGroupsReport')) {
                $table->dropColumn('ProfitGroupsReport');
            }
            if (Schema::hasColumn('reports_settings', 'IncomListReport')) {
                $table->dropColumn('IncomListReport');
            }
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ExportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('export_checks', function(Blueprint $table) {
            if (!Schema::hasColumn('export_checks', 'Image')) {
                $table->longText('Image')->nullable();
            }
            if (!Schema::hasColumn('export_checks', 'Signture_Name')) {
                $table->longText('Signture_Name')->nullable();
            }
            if (!Schema::hasColumn('export_checks', 'Bank_Branch')) {
                $table->longText('Bank_Branch')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('export_checks', function(Blueprint $table) {
            if (Schema::hasColumn('export_checks', 'Image')) {
                $table->dropColumn('Image');
            }
            if (Schema::hasColumn('export_checks', 'Signture_Name')) {
                $table->dropColumn('Signture_Name');
            }
            if (Schema::hasColumn('export_checks', 'Bank_Branch')) {
                $table->dropColumn('Bank_Branch');
            }
        });
    }
}

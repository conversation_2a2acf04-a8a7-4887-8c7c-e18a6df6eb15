<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesBillsFilter extends Model
{
    use HasFactory;
      protected $table = 'sales_bills_filters';
      protected $fillable = [

        'Code',                              
        'Date',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'Ship',
        'Shift_Code',
        'Later_Due',
        'Later_Collection',
        'Delivery',
        'ProfitPrecent',
        'TaxOnTotal',
        'TaxOnTotalType',
        'ProfitTax',
        'InstallCompany',
        'ServiceFee',
        'CompanyPrecent',
        'Time',
        'Branch',
        'CustomerGroup',
        'Type',
        'ID',
          
          
          


    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
    
   
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
     
                   public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
    

                     public function Delivery()
    {
        return $this->belongsTo(Employess::class,'Delivery');
    }
    
   
    
               public function TaxOnTotalType()
    {
        return $this->belongsTo(Taxes::class,'TaxOnTotalType');
    }
    

                           public function InstallCompany()
    {
        return $this->belongsTo(InstallmentCompanies::class,'InstallCompany');
    }
    
    
    
        public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
    
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSafeTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('safe_transfers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Date');
            $table->string('Draw')->nullable();
            $table->string('Amount');
            $table->text('Note')->nullable();
            $table->string('From_Safe');
            $table->string('To_Safe');
            $table->string('Coin');
            $table->string('Cost_Center')->nullable();
            $table->string('User');
            $table->integer('Status')->default(0);
            $table->string('File')->nullable();
            $table->string('OldAmount')->nullable();
            $table->string('Edit')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Time');
            $table->string('Branch');
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('safe_transfers');
    }
}

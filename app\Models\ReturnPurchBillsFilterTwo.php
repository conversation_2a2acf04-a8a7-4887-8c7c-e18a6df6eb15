<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnPurchBillsFilterTwo extends Model
{
    use HasFactory;
       protected $table = 'return_purch_bills_filter_twos';
      protected $fillable = [
        'Code',
        'Date',
        'Total_Return_Qty',
        'Total_Return_Value',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Discount',
        'Purchase',
        'Pay',
        'Payment_Method',
        'User',
        'Time',
        'Branch',
        'CustomerGroup',
        'Refernce_Number',
        'Safe',
        'Vendor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'Ship',
        'Later_Due',
        'Type',
        'ID',
          

    ];

           public function Purchase()
    {
        return $this->belongsTo(Purchases::class,'Purchase');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

    
        public function ReturnPurchProducts()
    {
        return $this->hasOne(ReturnPurchProducts::class);
    }
    
            public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
    
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
                  public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
}

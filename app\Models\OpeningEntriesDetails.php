<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OpeningEntriesDetails extends Model
{
    use HasFactory;
           protected $table = 'opening_entries_details';
      protected $fillable = [
        'Debitor',
        'Creditor',
        'Account',
        'Statement',
        'OP_ID',
       
    ];
    
        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
           public function OP_ID()
    {
        return $this->belongsTo(OpeningEntries::class,'OP_ID');
    }
}

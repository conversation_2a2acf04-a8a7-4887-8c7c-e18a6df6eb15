<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientAccountStatementColumn extends Model
{
    use HasFactory;
          protected $table = 'client_account_statement_columns';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',               
        'Refrence_Number',               
        'Branch',               
        'Store',               
        'Payment_Method',               
        'Safe',               
        'Type',               
        'Shipping',               
        'Cost_Center',               
        'ShiftCode',               
        'Executor',               
        'User',               
        'Coin',               
        'Due_Date',               
        'Delegate',               
        'Note',               
        'Total_Return',               
        'Total_Price',               
        'Total_Discount',               
        'Total_Tax',               
        'Total_Net',               
        'Paid',               
        'Residual',    
        'Client',    
          
             


    ];

}

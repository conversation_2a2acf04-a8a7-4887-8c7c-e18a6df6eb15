<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StoresTransferColumnsSechdulesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('stores_transfer_columns_sechdules')->delete();
        
        \DB::table('stores_transfer_columns_sechdules')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Amount' => '1',
                'From_Store' => '1',
                'To_Store' => '1',
                'User' => '1',
                'Coin' => '1',
                'Shipping' => '1',
                'Note' => '1',
                'Delegate' => NULL,
                'Product_Name' => '1',
                'Product_Code' => '1',
                'Group' => '1',
                'Brand' => NULL,
                'Qty' => NULL,
                'Price' => NULL,
                'Trans_Qty' => NULL,
                'Unit' => NULL,
                'Total' => NULL,
                'created_at' => NULL,
                'updated_at' => '2022-10-06 16:01:07',
                'Branch' => '1',
            ),
        ));
        
        
    }
}
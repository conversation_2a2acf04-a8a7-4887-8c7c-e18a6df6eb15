<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoresMoves extends Model
{
    use HasFactory;
        protected $table = 'stores_moves';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',                             
        'Branch',               
        'Store',                         
        'Safe',               
        'Type',                           
        'TypeEn',                           
        'Cost_Center',                            
        'User',               
        'Coin',                             
        'Note',               
        'Total_Qty',               
        'Total_Price',                          
        'Account',               
        'Ship',    
        'ID',    
  
           


    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
       
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
     
                   public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
    

        public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
}

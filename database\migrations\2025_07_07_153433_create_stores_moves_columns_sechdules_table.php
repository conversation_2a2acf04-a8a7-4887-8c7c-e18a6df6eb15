<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresMovesColumnsSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stores_moves_columns_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code');
            $table->string('Time');
            $table->string('Branch');
            $table->string('Store');
            $table->string('Safe');
            $table->string('Type');
            $table->string('Cost_Center');
            $table->string('User');
            $table->string('Coin');
            $table->string('Note');
            $table->string('Total_Qty');
            $table->string('Total_Price');
            $table->string('Account');
            $table->string('Ship');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stores_moves_columns_sechdules');
    }
}
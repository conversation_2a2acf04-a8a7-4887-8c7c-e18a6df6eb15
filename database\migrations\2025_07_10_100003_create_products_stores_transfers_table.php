<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsStoresTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products_stores_transfers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('Price')->nullable();
            $table->string('OldPrice')->nullable();
            $table->string('Av_Qty')->nullable();
            $table->string('SmallCode')->nullable();
            $table->string('Trans_Qty')->nullable();
            $table->string('Original_Trans_Qty')->nullable();
            $table->string('SmallTrans_Qty')->nullable();
            $table->string('Total')->nullable();
            $table->string('ST_ID');
            $table->string('Product');
            $table->string('V1')->nullable();
            $table->string('V2')->nullable();
            $table->string('Unit')->nullable();
            $table->string('To_Store')->nullable();
            $table->string('CostPrice')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products_stores_transfers');
    }
}

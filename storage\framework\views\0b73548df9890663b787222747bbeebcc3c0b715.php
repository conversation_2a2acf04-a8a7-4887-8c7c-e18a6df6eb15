<?php echo $__env->make('admin.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('admin.layouts.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'notify::components.notify','data' => []]); ?>
<?php $component->withName('notify::notify'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
<?php echo $__env->yieldContent('content'); ?>


<?php echo $__env->make('admin.layouts.afterNav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('admin.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<style>
    #laravel-notify{
        z-index: 9999999999999999;
    }    
</style>    
<?php
use App\Models\CompanyData;
$def=CompanyData::orderBy('id','desc')->first();
?>

<style>
.invoice-client-info{
 /*margin-top:20px;*/   
}
   .TableLabel{
            border: 1px solid #e5e5e5;
    padding: 9px;
    border-radius: 3px;
    background: #f3f3f3;
    font-size:11px !important;
    }
    @media  print {

     .no-print{
           display:none;
       }

   


      table { page-break-inside:auto }
tr    { page-break-inside:avoid; page-break-after:auto }
thead { display:table-header-group; }
tfoot { display:table-footer-group; }    
   
/*
        
Types of Print Sizes  :-       
A5 (148mm x 210mm)
A4 (210mm x 297mm) - the default size
A3 (297mm x 420mm)
B3 (353mm x 500mm)
B4 (250mm x 353mm)
JIS-B4 (257mm x 364mm)
letter (8.5in x 11in)
legal (8.5in x 14in)
ledger (11in x 17in)
        
        portrait or landscape
        
*/
   @page  {
            size: ledger landscape;
            margin: 3cm;
           
 page-break-after:always ;
         overflow: visible;

        }
        
           #X_filter{
            display:none;
        }
        #X_info{
             display:none;
        }
        .pagination{
             display:none;
        }
        #NoPara{
             display:none;
        }
        .breadcrumb-item {
            display:none;
        }
        #btn-more{
            display:none;
        }
        .auto-load{
            display:none; 
        }
        #X{
            width:100%;
        }
        .Headerrr{
       display:flex;
           flex-wrap: nowrap;
               justify-content: center;
        }
        .logoPri{
            display:flex;
                
               justify-content: flex-end;
        }
        table.dataTable thead .sorting:before, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_desc_disabled:before{
            display:none;
        }
        table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:after{
                display:none;
        }
  
    
    .panel-content,.panel-hdr , .panel{
        
margin:0px !important;   
border:unset !important;


    }
        
 .HeaderInvoice{
     font-size:15px !important;
 }
        
        }
</style>


<?php if($def->Font_Type == 1): ?>

     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Alexandria:wght@200;300;400;500;800;1000&family=Alexandria:wght@300;500;700&family=Alexandria:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Alexandria', sans-serif  !important;
        }
    </style>

<?php elseif($def->Font_Type == 2): ?>

    <!-- google font cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;800;1000&display=swap" rel="stylesheet">
    
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Cairo', sans-serif  !important;
        }
    </style>

<?php elseif($def->Font_Type == 3): ?>    

     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Mada:wght@200;300;400;500;800;1000&family=Mada:wght@300;500;700&family=Mada:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body  , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Mada', sans-serif  !important;
        }
    </style>

<?php elseif($def->Font_Type == 4): ?>

     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Marhey:wght@200;300;400;500;800;1000&family=Marhey:wght@300;500;700&family=Marhey:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Marhey', sans-serif  !important;
        }
    </style>

<?php elseif($def->Font_Type == 5): ?>

     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Reem+Kufi+Fun:ital,wght@0,700;1,400;1,700&family=Reem+Kufi+Fun:wght@200;300;400;500;800;1000&family=Reem+Kufi+Fun:wght@300;500;700&family=Reem+Kufi+Fun:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body, h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Reem Kufi Fun', sans-serif !important;
        }
    </style>


<?php endif; ?><?php /**PATH C:\xampp2\htdocs\new_ost\resources\views/admin/index.blade.php ENDPATH**/ ?>
<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Purchases;
use App\Models\ReturnPurch;
use DB;
class ExportPurchasesBillsReport implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result


        $storex=$this->store;
        $storee =  $storex['store'];
        $from =  $storex['from'];
        $to =  $storex['to'];
        $branch =  $storex['branch'];
        $clients_Group =  $storex['clients_Group'];
        $cost_Center =  $storex['cost_Center'];
        $coin =  $storex['coin'];
        $code =  $storex['code'];
        $refrence_Number =  $storex['refrence_Number'];
        $safe =  $storex['safe'];
        $client =  $storex['client'];
        $payment_Method =  $storex['payment_Method'];
        $delegate =  $storex['delegate'];
        $user =  $storex['user'];
        $shipping_Company =  $storex['shipping_Company'];
        $types =  $storex['types'];
        $typeX =  $storex['typeX'];


   if(app()->getLocale() == 'ar' ){         
         $prods = DB::table('purchases')->whereBetween('purchases.Date',[$from,$to])
             
                ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('purchases.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('purchases.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('purchases.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('purchases.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('purchases.Code', $code);
    }) 
          
 
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('purchases.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('purchases.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('purchases.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('purchases.Vendor', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('purchases.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('purchases.Delegate', $delegate);
    })     



          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('purchases.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('purchases.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('purchases.Status', $types);
    })  
 

             
            ->join('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('purchases.Vendor', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('purchases.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('purchases.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('purchases.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('purchases.Date'
         ,'purchases.Time'
         ,'purchases.Code'
         ,'purchases.Refernce_Number'
         ,'branches.Arabic_Name as Branch'
         ,'acccounting_manuals.Name as Vendor'
         ,'stores.Name as Store'
         ,'safes_banks.Name as Safe'
         ,'purchases.Total_Price'
         ,'purchases.Total_Discount'
         ,'purchases.Total_Taxes'
         ,'purchases.Pay'
         ,'purchases.Later_Due'
              ,'coins.Arabic_Name as Coin'
              ,'employesses.Name as Delegate'
              ,'admins.name as User'
              ,'cost_centers.Arabic_Name as CostCenter'
           ,'purchases.Note'
        )
                  ->get();
   }else{
       
       
           $prods = DB::table('purchases')->whereBetween('purchases.Date',[$from,$to])
             
                ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('purchases.Branch', $branch);
    })
        
          
         ->when(!empty($clients_Group), function ($query) use ($clients_Group) {
        return $query->where('purchases.CustomerGroup', $clients_Group);
    })      
          
          ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('purchases.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('purchases.Coin', $coin);
    })  
          
          
          ->when(!empty($code), function ($query) use ($code) {
        return $query->where('purchases.Code', $code);
    }) 
          
 
          ->when(!empty($refrence_Number), function ($query) use ($refrence_Number) {
        return $query->where('purchases.Refernce_Number', $refrence_Number);
    })   
          
          
           ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('purchases.Store', $storee);
    })
          

          
               ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('purchases.Safe', $safe);
    })        
        
                  ->when(!empty($client), function ($query) use ($client) {
        return $query->whereIn('purchases.Vendor', $client);
    })    
          
          
                  ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('purchases.Payment_Method', $payment_Method);
    })          
    

      ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('purchases.Delegate', $delegate);
    })     



          ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('purchases.User', $user);
    })     

     ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('purchases.Ship', $shipping_Company);
    })     

    ->when(!empty($types), function ($query) use ($types) {
        return $query->whereIn('purchases.Status', $types);
    })  
 

             
            ->join('stores', function ($join) {
    
            $join->on('purchases.Store', '=', 'stores.id');
        })
                  ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })     
             
              ->join('acccounting_manuals', function ($join) {
    
            $join->on('purchases.Vendor', '=', 'acccounting_manuals.id');
        })
              ->join('safes_banks', function ($join) {
    
            $join->on('purchases.Safe', '=', 'safes_banks.Account');
        })
              ->leftJoin('coins', function ($join) {
    
            $join->on('purchases.Coin', '=', 'coins.id');
        })
             
              ->leftJoin('employesses', function ($join) {
    
            $join->on('purchases.Delegate', '=', 'employesses.id');
        })
               ->leftJoin('admins', function ($join) {
    
            $join->on('purchases.User', '=', 'admins.id');
        })
              ->leftJoin('cost_centers', function ($join) {
    
            $join->on('purchases.Cost_Center', '=', 'cost_centers.id');
        })
 
->select('purchases.Date'
         ,'purchases.Time'
         ,'purchases.Code'
         ,'purchases.Refernce_Number'
         ,'branches.English_Name as Branch'
         ,'acccounting_manuals.NameEn as Vendor'
         ,'stores.NameEn as Store'
         ,'safes_banks.NameEn as Safe'
         ,'purchases.Total_Price'
         ,'purchases.Total_Discount'
         ,'purchases.Total_Taxes'
         ,'purchases.Pay'
         ,'purchases.Later_Due'
              ,'coins.English_Name as Coin'
              ,'employesses.NameEn as Delegate'
              ,'admins.nameEn as User'
              ,'cost_centers.English_Name as CostCenter'
           ,'purchases.Note'
        )
                  ->get();
       
       
       
   }

        
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Time',
          'Code',
          'Refernce_Number',
          'Branch',
          'Vendor',
          'Store',
          'Safe',
          'Total_Price',
          'Total_Discount',
          'Total_Tax',
          'Pay',
          'Due_Date',
          'Coin',
          'Delegate',
          'User',
          'Cost_Center',
          'Note',

        ];
    }
    
    
    

}

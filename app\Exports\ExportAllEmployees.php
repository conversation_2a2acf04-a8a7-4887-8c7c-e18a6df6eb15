<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllEmployees implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
        
        
        
           if(app()->getLocale() == 'ar' ){ 
   
           $prods = DB::table('employesses')
               
          
                  
                   ->join('jobs_types', function ($join) {
    
            $join->on('employesses.Job', '=', 'jobs_types.id');
        })
               
                   ->join('work_departments', function ($join) {
    
            $join->on('employesses.Department', '=', 'work_departments.id');
        })
               
               
                   ->join('acccounting_manuals', function ($join) {
    
            $join->on('employesses.Account_Emp', '=', 'acccounting_manuals.id');
        })
        


          
        
->select('employesses.Code'
         ,'employesses.Name'
         ,'employesses.Salary'
         ,'employesses.Attendence'
         ,'employesses.Departure'
         ,'employesses.Hours_Numbers'
         ,'employesses.Days_Numbers'
         ,'employesses.Day_Price'
         ,'employesses.Address'
         ,'employesses.ID_Number'
         ,'employesses.Contract_Start'
         ,'employesses.Contract_End'
         ,'employesses.Phone'
         ,'employesses.Phone2'
     ,'jobs_types.Arabic_Name as Job'
     ,'work_departments.Arabic_Name as Department'
        ,'acccounting_manuals.Code as Account'
         ,'employesses.Price_Level'
         ,'employesses.Birthdate'

   
       
  
         
        )
                  ->get();
               
               
           }else{
             
               
                   $prods = DB::table('employesses')
               
          
                  
                   ->join('jobs_types', function ($join) {
    
            $join->on('employesses.Job', '=', 'jobs_types.id');
        })
               
                   ->join('work_departments', function ($join) {
    
            $join->on('employesses.Department', '=', 'work_departments.id');
        })
               
               
                   ->join('acccounting_manuals', function ($join) {
    
            $join->on('employesses.Account_Emp', '=', 'acccounting_manuals.id');
        })
        


          
        
->select('employesses.Code'
         ,'employesses.NameEn'
         ,'employesses.Salary'
         ,'employesses.Attendence'
         ,'employesses.Departure'
         ,'employesses.Hours_Numbers'
         ,'employesses.Days_Numbers'
         ,'employesses.Day_Price'
         ,'employesses.Address'
         ,'employesses.ID_Number'
         ,'employesses.Contract_Start'
         ,'employesses.Contract_End'
         ,'employesses.Phone'
         ,'employesses.Phone2'
     ,'jobs_types.English_Name as Job'
     ,'work_departments.English_Name as Department'
        ,'acccounting_manuals.Code as Account'
         ,'employesses.Price_Level'
         ,'employesses.Birthdate'

   
       
  
         
        )
                  ->get();
               
               
               
               
           }
 
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Name',
          'Salary',
          'Attendence',
          'Departure',
          'Hours_Numbers',
          'Days_Numbers',
          'Day_Price',
          'Address 1',
          'ID_Number 2',
          'Contract_Start 3',
          'Contract_End',
          'Phone',
          'Phone2',
          'Job',
          'Department',
          'Account',
          'Price_Level',
          'Birthdate',
         
        
        ];
    }
    
    
    

}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DB_Backup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'DB:BACKUP';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Database Backup Daily';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
   
             @ini_set('max_execution_time', 0);
		@set_time_limit(0);
        ini_set('memory_limit', '-1');
        //ENTER THE RELEVANT INFO BELOW
        $mysqlHostName      = env('DB_HOST');
        $mysqlUserName      = env('DB_USERNAME');
        $mysqlPassword      = env('DB_PASSWORD');
        $DbName             = env('DB_DATABASE');
        $backup_name        = "mybackup.sql";
        $tables             = 
        array(
            "abouts",
            "acccounting_manuals",
            "accounts_default_data",
            "activites",
            "addressses",
            "admins",
            "all_groups",
            "attendances",
            "attendance_emps",
            "assets_expenses",
            "assets",
            "assembly_products",
            "articles",
            "barcode_products",
            "barcode_settings",
            "barcode_shows",
            "befroe_footers",
            "beneftis_types",
            "bones_sales_petrols",
            "bones_types",
            "borrowas",
            "branches",
            "brands",
            "campaigns",
            "capitals",
            "cars_sales_petrols",
            "checks_types",
            "cities",
            "client_account_statement_column_sechdules",
            "client_account_statement_columns",
            "client_filters",
            "client_sales_petrols",
            "client_statuses",
            "clients_statements_column_sechdules",
            "clients_statements_columns",
            "coins",
            "comments",
            "company_cars",
            "company_data",
            "compare_prices_columns",
            "compare_prices_columns_sechdules",
            "compare_prices_filter_twos",
            "compare_prices_filters",
            "compares",
            "consist_maintainces",
            "consists",
            "contact_u_s",
            "cost_centers",
            "counters_types",
            "countris",
            "coupon_codes",
            "crm_default_data",
            "customers",
            "customers_files",
            "customers_groups",
            "customers_tickets",
            "deducations_types",
            "deductions",
            "default_data_show_hides",
            "departure_emps",
            "departures",
            "desvice_cases",
            "device_descrips",
            "devices_typesies",
            "disclaimers",
            "emp_excs", 
            "emp_installment_details",
            "emp_installments",
            "emp_p_o_s_stores",
            "emp_ratios",
            "employesses",
            "employment_levels",
            "entitlements",
            "examinations_types",
            "exchange_commissions",
            "executing_receiving_secretariats", 
            "executing_receivings",
            "executor_filters",
            "expenses_list_column_sechdules",
            "expenses_list_columns",
            "export_checks",
            "f_a_q_s",
            "failed_jobs",
            "faults_types",
            "firewall_ips",
            "firewall_logs", 
            "general_dailies",
            "governrates",
            "group_filters",
            "holidays",
            "holidays_types",
            "import_new_prods",
            "import_new_prods_starts",
            "incom_checks",
            "incom_manufacturing_models",
            "incom_manufacturing_secretariat_models", 
            "install_companies_sales_bills_columns",
            "install_companies_sales_bills_columns_sechdules",
            "installment_companies",
            "installment_companies_ratios",
            "installment_dates",
            "installments",
            "insurance_companies",
            "insurance_papers",
            "interviews",
            "interviews_types",
            "inventories",
            "items_groups",
            "jobs_types",
            "journalizing_details",
            "journalizings",
            "loan_types",
            "loans",        
            "maintainc_bills",
            "maintaince_colors",
            "maintaince_default_data", 
            "manu_store_counts",
            "manufacture_companies",
            "manufacturing_default_data",
            "manufacturing_executions",
            "manufacturing_halls",
            "manufacturing_models",
            "manufacturing_orders",
            "manufacturing_requests",
            "manufacturing_secretariat_models",
            "measuerments",
            "migrations",
            "missions",
            "model_has_permissions",
            "model_has_roles",
            "module_settings_nums",
            "modules",
            "most_sales_products_column_sechdules",        
            "most_sales_products_columns",
            "msg_rqsts",
            "opening_entries", 
            "opening_entries_details",
            "outcom_manufacturing_models",
            "outcome_manufacturing_secretariat_models",
            "over_times",
            "partners",
            "password_resets",
            "pay_salaries",
            "payment_voucher_details",
            "payment_vouchers",
            "permission_to_exchange_goods",
            "permission_to_recived_goods",
            "permissions",
            "places",
            "platforms",
            "polices",
            "pro_details_imgs",
            "product_executing_receiving_secretariats",        
            "product_inventories",
            "product_maintainc_bills",
            "product_manufacturing_executions", 
            "product_moves",
            "product_moves_columns",
            "product_moves_columns_sechdules",
            "product_moves_filter_twos",
            "product_moves_filters",
            "product_quote_images",
            "product_sales",
            "product_sales_orders",
            "product_sales_subscribes",
            "product_settlements",
            "product_type_defaults",
            "product_units",
            "products",
            "products_consists",
            "products_executing_receivings",
            "products_manufacturing_orders",
            "products_manufacturing_requests",         
            "products_permission_to_exchange_goods",
            "products_permission_to_recived_goods",
            "products_purchase_petrols", 
            "products_purchases",
            "products_purchases_orders",
            "products_qties",
            "products_quotes",
            "products_return_maintaince_bills",
            "products_secretariat_export_goods",
            "products_secretariat_import_goods",
            "products_shortcomings",
            "products_start_periods",
            "products_stores",
            "products_stores_transfers",
            "products_viras",
            "profit_sales_product_column_sechdulrs",
            "profit_sales_product_columns",
            "project_teams",          
            "projects",
            "purch_bills_columns",         
            "purch_bills_columns_sechdules",
            "purch_bills_filter_twos",
            "purch_bills_filters", 
            "purchase_petrols",
            "purchases",
            "purchases_default_data",
            "purchases_orders",
            "q_r_s",
            "qualities",
            "quality_details",
            "quote_images",
            "quotes",
            "rates",
            "reasons",
            "recipt_maintaince_errors",
            "recipt_maintainces",
            "recipt_voucher_details",
            "recipt_vouchers",            
            "recipts_sales_petrols",
            "recipts_types",         
            "recived_purch_products",
            "recived_purchts",
            "recived_sales", 
            "recived_sales_products",
            "reg_over_times",
            "reservations",
            "resignation_requests",
            "return_maintaince_bills",
            "return_purch_bills_filter_twos",
            "return_purch_bills_filters",
            "return_purch_products",
            "return_purches",
            "return_sales",
            "return_sales_bills_filter_twos",
            "return_sales_bills_filters",
            "return_sales_products",
            "role_has_permissions",
            "roles",           
            "room_reservations",
            "rooms",         
            "rooms_types",
            "safe_transfer_columns",
            "safe_transfer_columns_sechdules", 
            "safe_transfer_filter_twos",
            "safe_transfer_filters",
            "safe_transfers",
            "safes_banks",
            "sales",
            "sales_bills_columns",
            "sales_bills_columns_sechdules",
            "sales_bills_filter_twos",
            "sales_bills_filters",
            "sales_default_data",
            "sales_orders",
            "sales_petrols",
            "sales_subscribes",
            "secretariat_export_goods",
            "secretariat_import_goods",            
            "secretariat_qties",
            "secretariat_stores",         
            "select_a_p_i_s",
            "settlements",
            "shifts", 
            "shipping_companies",
            "shipping_defaults",
            "shipping_orders",
            "shipping_statuses",
            "shipping_types",
            "shortcomings",
            "show_print_defaults",
            "social_media",
            "spend_profits",
            "start_periods",
            "store_count_sales",
            "store_counts",
            "store_transfer_filter_twos",           
            "store_transfer_filters",
            "stores",
            "stores_default_data",
            "stores_moves",
            "stores_moves_columns",
            "stores_moves_columns_sechdules",
            "stores_moves_filter_twos",
            "stores_moves_filters",
            "stores_transfer_columns",
            "stores_transfer_columns_sechdules",           
            "stors_transfers",
            "sub_images",
            "sub_virables",
            "subscribe_types",
            "taxes",
            "terms",
            "terms_maintainces",
            "transltors",
            "users_moves",
            "vendor_account_statement_column_sechdules",           
            "vendor_account_statement_columns",
            "vendor_filters",
            "vendors",
            "vendors_statements_column_sechdules",
            "vendors_statements_columns",
            "virables",
            "vouchers",
            "web_sliders",
            "wishlists",
            "work_departments",
            "discounts_emps",
            "allowences_emps",
            "return_products_stores_transfers",
            "return_stors_transfers",
            "emps_producation_quantities",
            "emps_producation_points",
            "login_sliders",
            "attendence_policy_emps",
            "depaarture_policy_emps",
            "workers_sales_petrols"
        ); //here your tables...

        $connect = new \PDO("pgsql:host=$mysqlHostName;port=5432;dbname=$DbName", "$mysqlUserName", "$mysqlPassword");
        $get_all_table_query = "SELECT * FROM information_schema.tables WHERE table_schema = 'public'";
        $statement = $connect->prepare($get_all_table_query);        
        $statement->execute();
        $result = $statement->fetchAll();

        
        $output = '';
        foreach($tables as $table)
        {
   
         $select_query = "SELECT * FROM " . $table . "";
         $statement = $connect->prepare($select_query);
         $statement->execute();
         $total_row = $statement->rowCount();

         for($count=0; $count<$total_row; $count++)
         {
          $single_result = $statement->fetch(\PDO::FETCH_ASSOC);
          $table_column_array = array_keys($single_result);
          $table_value_array = array_values($single_result);
          $output .= "\nINSERT INTO public.$table VALUES (";
          $output .= "'" . implode("','", $table_value_array) . "');\n";
         }
        }
        
        
        //save file
		//$file_name = 'public/backup/DB-BACKUP-'.time().'.sql';
		$file_name = 'database_backup_on_'.time().'.sql';
		$handle = fopen($file_name,'w+');
		fwrite($handle,$output);
		fclose($handle);
		
		return response()->download($file_name);
       	return redirect()->back()->with('success', trans('admin.DBBackupSuccessfully')); 
        
        
        
    }
}

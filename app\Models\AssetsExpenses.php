<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetsExpenses extends Model
{
    use HasFactory;
      protected $table = 'assets_expenses';
      protected $fillable = [
        'Code',
        'Date',
        'Name',
        'Amount',
        'Asset',
        'Safe',
        'User',
        'Draw',
        'Coin',
        'Cost_Center',
    ];
    
    
       public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
             public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
                public function Asset()
    {
        return $this->belongsTo(Assets::class,'Asset');
    }
    
            public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
}

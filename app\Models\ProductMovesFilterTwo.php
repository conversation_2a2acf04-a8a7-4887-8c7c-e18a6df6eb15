<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductMovesFilterTwo extends Model
{
    use HasFactory;
      protected $table = 'product_moves_filter_twos';
      protected $fillable = [

                'Date',
                'Type',
                'Bill_Num',
                'Incom',
                'Outcom',
                'Current',
                'P_Ar_Name',
                'P_En_Name',
                'P_Code',
                'Unit',
                'Group',
                'Store',
                'Product',
                'V1',
                'V2',
                'User',
                'CostIn',
                'CostOut',
                'CostCurrent',
                'CostOneIn',
                'CostOneOut',
                'CostOneCurrent',
                'QTY',
                'Brand',
                'Safe',
                'Branch',
                'SalePrice',
                'ProductPrice',
          
    ];
    
        public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
        public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }
    
        public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
          public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
          public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
             public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
}

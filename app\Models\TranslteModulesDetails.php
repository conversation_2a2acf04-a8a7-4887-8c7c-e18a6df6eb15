<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TranslteModulesDetails extends Model
{
    use HasFactory;
          protected $table = 'translte_modules_details';
      protected $fillable = [

        'Arabic_Text',
        'Translate_Text',
        'Module',

     
   
    ];
    
               public function Module()
    {
        return $this->belongsTo(TranslteModules::class,'Module');
    }
}

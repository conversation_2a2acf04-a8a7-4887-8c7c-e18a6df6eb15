<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admins', function (Blueprint $table) {
            $table->increments('id');
            $table->string('email')->unique();
            $table->string('name');
            $table->string('password');
            $table->string('image')->nullable();
            $table->string('phone')->nullable();
            $table->string('hidden');
            $table->string('emp');
            $table->string('ship');
            $table->string('vend');
            $table->string('status');
            $table->rememberToken();
            $table->timestamps();
            $table->integer('cli');
            $table->string('lat')->nullable();
            $table->string('long')->nullable();
            $table->integer('account');
            $table->string('safe')->nullable();
            $table->string('store')->nullable();
            $table->string('type')->nullable();
            $table->string('roles_name')->nullable();
            $table->string('code')->nullable();
            $table->string('token')->nullable();
            $table->string('price_sale')->nullable();
            $table->string('discount')->nullable();
            $table->string('price_1')->nullable();
            $table->string('price_2')->nullable();
            $table->string('price_3')->nullable();
            $table->string('pos_pay')->nullable();
            $table->string('executor')->nullable();
            $table->string('cost_price')->nullable();
            $table->string('price_level')->nullable();
            $table->string('guest')->nullable();
            $table->string('pos_stores')->nullable();
            $table->string('pos_hold')->nullable();
            $table->string('cost_price_purch')->nullable();
            $table->string('cost_price_sales')->nullable();
            $table->string('manu_order_precent')->nullable();
            $table->string('pos_product');
            $table->string('Cash');
            $table->string('Delivery');
            $table->string('Cash_Collection');
            $table->string('Cash_Visa');
            $table->string('Installment');
            $table->string('Check');
            $table->string('Later');
            $table->string('InstallmentCompanies');
            $table->timestamp('Date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admins');
    }
}
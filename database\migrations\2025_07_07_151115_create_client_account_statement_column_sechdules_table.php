<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientAccountStatementColumnSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_account_statement_column_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code');
            $table->string('Time');
            $table->string('Refrence_Number');
            $table->string('Branch');
            $table->string('Store');
            $table->string('Payment_Method');
            $table->string('Safe');
            $table->string('Type');
            $table->string('Shipping');
            $table->string('Cost_Center');
            $table->string('User');
            $table->string('Coin');
            $table->string('Due_Date');
            $table->string('Delegate');
            $table->string('Note');
            $table->string('Total_Return');
            $table->string('Total_Price');
            $table->string('Total_Discount');
            $table->string('Total_Tax');
            $table->string('Total_Net');
            $table->string('Paid');
            $table->string('Residual');
            $table->string('Client');
            $table->timestamps();
            $table->string('ShiftCode');
            $table->string('Executor');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_account_statement_column_sechdules');
    }
}
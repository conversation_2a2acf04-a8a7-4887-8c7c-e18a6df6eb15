<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInstallCompaniesSalesBillsColumnsSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('install_companies_sales_bills_columns_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code');
            $table->string('Time');
            $table->string('Refrence_Number');
            $table->string('Branch');
            $table->string('Store');
            $table->string('Payment_Method');
            $table->string('Safe');
            $table->string('Type');
            $table->string('Shipping');
            $table->string('Cost_Center');
            $table->string('ShiftCode');
            $table->string('Executor');
            $table->string('User');
            $table->string('Coin');
            $table->string('Due_Date');
            $table->string('Delegate');
            $table->string('Note');
            $table->string('Total_Return');
            $table->string('Total_Price');
            $table->string('Total_Discount');
            $table->string('Total_Tax');
            $table->string('Total_Net');
            $table->string('Paid');
            $table->string('Residual');
            $table->string('Client');
            $table->string('InstallCompany');
            $table->string('ContractNumber');
            $table->string('PayFees');
            $table->string('ServiceFee');
            $table->string('CompanyPrecent');
            $table->string('Product_Code');
            $table->string('Product_Name');
            $table->string('Unit');
            $table->string('Av_Qty');
            $table->string('Qty');
            $table->string('Price');
            $table->string('Discount');
            $table->string('Total_BF_Tax');
            $table->string('Tax');
            $table->string('Total');
            $table->string('Group');
            $table->string('Brand');
            $table->string('Exp_Date');
            $table->string('Product_Store');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('install_companies_sales_bills_columns_sechdules');
    }
}
<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllJournalizing implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
        
             if(app()->getLocale() == 'ar' ){ 
   
           $prods = DB::table('journalizings')
               
               
            ->leftJoin('journalizing_details', function ($join) {
    
            $join->on('journalizings.id', '=', 'journalizing_details.Joun_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('journalizings.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('journalizing_details.Account', '=', 'acccounting_manuals.id');
        })     
         
   
->select('journalizings.Date'
         ,'journalizings.Code'
         ,'coins.Arabic_Name as Coin'
         ,'journalizing_details.Debitor'
         ,'journalizing_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.Name as AccountName'
         ,'journalizing_details.Statement'
        
         
        )
                  ->get();
                 
             }else{
                 
                 
                     $prods = DB::table('journalizings')
               
               
            ->leftJoin('journalizing_details', function ($join) {
    
            $join->on('journalizings.id', '=', 'journalizing_details.Joun_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('journalizings.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('journalizing_details.Account', '=', 'acccounting_manuals.id');
        })     
         
   
->select('journalizings.Date'
         ,'journalizings.Code'
         ,'coins.English_Name as Coin'
         ,'journalizing_details.Debitor'
         ,'journalizing_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.Name as AccountName'
         ,'journalizing_details.Statement'
        
         
        )
                  ->get();
                 
                 
                 
             }
 
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Coin',
          'Debitor',
          'Creditor',
          'Account Code',
          'Account Name',
          'Statement',
        ];
    }
    
    
    

}

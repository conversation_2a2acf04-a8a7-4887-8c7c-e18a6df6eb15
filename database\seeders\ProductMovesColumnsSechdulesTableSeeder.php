<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProductMovesColumnsSechdulesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('product_moves_columns_sechdules')->delete();
        
        \DB::table('product_moves_columns_sechdules')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Product_Code' => '1',
                'Product_Name' => '1',
                'Unit' => '1',
                'Type' => '1',
                'Bill_Num' => '1',
                'Incom' => '1',
                'Outcom' => '1',
                'Credit' => '1',
                'Group' => '1',
                'Brand' => '1',
                'Store' => '1',
                'User' => '1',
                'Safe' => '1',
                'Branch' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-09 15:30:19',
            ),
        ));
        
        
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductUnits extends Model
{
    use HasFactory;
           protected $table = 'product_units';
      protected $fillable = [

                'Rate',
                'Barcode',
                'Price',
                'Price_Two',
                'Price_Three',
                'P_Ar_Name',
                'P_En_Name',
                'P_Type',
                'Unit',
                'Product',
                'Def',
                'Brand',
                'Group',

    ];
    
        public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }

           public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }
}

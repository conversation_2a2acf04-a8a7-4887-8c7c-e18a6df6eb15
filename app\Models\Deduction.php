<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Deduction extends Model
{
    use HasFactory;
         protected $table = 'deductions';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Amount',
        'Draw',
        'Note',
        'Emp',
        'Coin',
        'Type',
        'User',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
         public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Type()
    {
        return $this->belongsTo(DeducationsTypes::class,'Type');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecivedSalesProducts extends Model
{
    use HasFactory;
       protected $table = 'recived_sales_products';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'Recived_Qty',
        'AvQty',
        'Qty',
        'Price',
        'Discount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
        'Product',
        'Exp_Date',
        'V1',
        'V2',
        'Unit',
        'Recived',
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Recived()
    {
        return $this->belongsTo(RecivedSales::class,'Recived');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsManufacturingOrder extends Model
{
    use HasFactory;
               protected $table = 'products_manufacturing_orders';
      protected $fillable = [

        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Store_Qty',
        'Required_Qty',
        'Qty',
        'Store',
        'Product',
        'V1',
        'V2',
        'Unit',
        'ManuOrder',
        'Precent',
   
    ];
    
    
      public function ManuOrder()
    {
        return $this->belongsTo(ManufacturingOrder::class,'ManuOrder');
    }
    
         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
         public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    

    
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\WorkDepartments;
use App\Models\ModuleSettingsNum;
use App\Models\OutcomManufacturingModel;
use App\Models\JobsTypes;
use App\Models\DeducationsTypes;
use App\Models\BeneftisTypes;
use App\Models\UsersMoves;
use App\Models\OverTimes;
use App\Models\HolidaysTypes;
use App\Models\Employess;
use App\Models\AcccountingManual;
use App\Models\Admin;
use App\Models\LoanTypes;
use App\Models\Borrowa;
use App\Models\CostCenter;
use App\Models\ProductUnits;
use App\Models\Coins;
use App\Models\JournalizingDetails;
use App\Models\Journalizing;
use App\Models\GeneralDaily;
use App\Models\Entitlement;
use App\Models\Deduction;
use App\Models\Holidays;
use App\Models\Attendance;
use App\Models\Departure;
use App\Models\AttendanceEmp;
use App\Models\DepartureEmp;
use App\Models\RegOverTime;
use App\Models\Settlement;
use App\Models\Loan;
use App\Models\PaySalary;
use App\Models\Stores;
use App\Models\EmpInstallment;
use App\Models\EmpInstallmentDetails;
use App\Models\Sales;
use App\Models\EmpRatio;
use App\Models\ExchangeCommissions;
use App\Models\ReturnMaintainceBill;
use App\Models\ProductSales;
use App\Models\ProductsPurchases;
use App\Models\ReciptMaintaince;
use App\Models\ProductMaintaincBill;
use App\Models\ProductsStartPeriods;
use App\Models\ProductsStoresTransfers;
use App\Models\CrmDefaultData;
use App\Models\PurchasesDefaultData;
use App\Models\SalesDefaultData;
use App\Models\MaintainceDefaultData;
use App\Models\ReciptVoucher;
use App\Models\ReciptVoucherDetails;
use App\Models\EmpPOSStores;
use App\Models\Employment_levels;
use App\Models\Insurance_companies;
use App\Models\Branches;
use App\Models\Countris;
use App\Models\Disclaimer;
use App\Models\ResignationRequest;
use App\Models\AllowencesEmp;
use App\Models\DiscountsEmp;
use App\Models\EmpsProducationQuantity;
use App\Models\EmpsProducationPoint;
use App\Models\AttendencePolicyEmp;
use App\Models\DepaarturePolicyEmp;
use DB;
use Str;
use DateTime;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class HRReportsController extends Controller
{
    function __construct()
{
 
$this->middleware('permission:تقرير الحضور و الانصراف', ['only' => ['AttendenceAndDepartureReport','AttendenceAndDepartureReportFilterTwo'] ]);       
$this->middleware('permission:تقرير قيمة الحضور', ['only' => ['AttendenceValueReport','AttendenceValueReportFilterTwo'] ]);       
$this->middleware('permission:تقرير صرف الرواتب', ['only' => ['PaySalaryReport'] ]);       
}
    
    
    //Attendence And Departure Report
         public function AttendenceAndDepartureReport(){

                $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();
         return view('admin.HRReports.AttendenceAndDepartureReport',['Employess'=>$Employess]);
    }
   
         public function AttendenceAndDepartureReportFilterTwo(Request $request){

             $from = $request->get('from');             
      $to = $request->get('to');                        
      $emp = $request->get('emp');             
  
           $data =DepartureEmp::whereBetween('Date', [$from, $to])  
                        
          ->when(!empty($emp), function ($query) use ($emp) {
        return $query->where('Emp',$emp);  
               
                })  
              

          ->paginate(100); 
  
          
             
            
      $output = '';
          if($request->ajax())
     { 
              
       foreach($data as $row)
       {
          
           
                           $Mwzf=Employess::find($row->Emp);
if(!empty($Mwzf)){
$Att = new DateTime($Mwzf->Attendence);
$AttTime = new DateTime($row->In_Time);
$DifTimeAtt = $Att->diff($AttTime);
       
$Dep = new DateTime($Mwzf->Departure);
$DepTime = new DateTime($row->Out_Time);
$DifTimeDep = $DepTime->diff($Dep);         
             
$x=$DifTimeAtt->format("%H:%I:%S");
$y=$DifTimeDep->format("%H:%I:%S");
    
    
$Act = new DateTime($row->In_Time);
$ActTime = new DateTime($row->Out_Time);
$DifAct = $ActTime->diff($Act);       
    
$Actual=$DifAct->format("%H:%I:%S");    
}else{
$x=0;    
$y=0;    
  $Actual= 0;  
}
       
    $datetime = DateTime::createFromFormat('Y-m-d', $row->Date);
$Day=$datetime->format('l');

           if(!empty($row->Emp()->first()->Name)){
                  if(app()->getLocale() == 'ar' ){ 
               $Name=$row->Emp()->first()->Name;
                  }else{
                     $Name=$row->Emp()->first()->NameEn;      
                      
                  }
           }else{
               
             $Name='';  
           }
           
           
        $output .= '
        <tr>

                                                    <td>'.$row->Date.'</td>
                                                    <td>'.$Day.'</td>
                                                    <td>'.$row->Month.'</td>
                                                    <td>'.$Name.'</td>
                                                    <td>'.$row->In_Time.'</td>
                                                    <td>'.$x.'</td>
                                                    <td>'.$row->Out_Time.'</td>
                                                     <td>'.$y.'</td>
                                                     <td>'.$Actual.'</td>
                                                </tr>
   
        ';   
 
    $last_id = $row->id;
       }
          
    return   $output ;       
          }      
          
         return view('admin.HRReports.AttendenceAndDepartureReportFilterTwo',[
            'from'=>$from, 
            'to'=>$to, 
            'emp'=>$emp, 

         ]);
    }
    
    

    
    //Attendence Value Report
           public function AttendenceValueReport(){

                $Employess=Employess::where("EmpSort",1)->where('Active',1)->get();
         return view('admin.HRReports.AttendenceValueReport',['Employess'=>$Employess]);
    }
   
         public function AttendenceValueReportFilterTwo(Request $request){

             $from = $request->get('from');             
      $to = $request->get('to');                        
      $emp = $request->get('emp');             
  
           $data =Employess::  
                        
          when(!empty($emp), function ($query) use ($emp) {
        return $query->where('id',$emp);  
               
                })  
              

          ->paginate(100); 
  
          
             
            
      $output = '';
          if($request->ajax())
     { 
              
       foreach($data as $row)
       {
          
$HourValue=$row->Salary / $row->Hours_Numbers;
 $Attendence=DepartureEmp::whereBetween('Date', [$from, $to])->where('Emp',$row->id)->get()->sum('Hours_Number') ; 
 $DiscountLate=DepartureEmp::whereBetween('Date', [$from, $to])->where('Emp',$row->id)->get()->sum('Disc_Late') ; 
 $DiscountDeparture=DepartureEmp::whereBetween('Date', [$from, $to])->where('Emp',$row->id)->get()->sum('Disc_Early') ; 
          $FinValue= ( $Attendence * $HourValue)  - ( $DiscountLate + $DiscountDeparture );
           
           
                     if(app()->getLocale() == 'ar' ){ 
               $Name=$row->Name;
                  }else{
                     $Name=$row->NameEn;      
                      
                  }
           
        $output .= '
        <tr>

                                                      <td>'.$Name.'</td>
                                                    <td>'.$HourValue.'</td>
                                                    <td>'.number_format((float)$Attendence, 2, '.', '').'</td>
                                                    <td>'.$DiscountLate.'</td>
                                                    <td>'.$DiscountDeparture.'</td>
                                                    <td>'.number_format((float)$FinValue, 2, '.', '').'</td>
                                                    
                                                   
                                                </tr>
   
        ';   
 
    $last_id = $row->id;
       }
          
    return   $output ;       
          }      
          
         return view('admin.HRReports.AttendenceValueReportFilterTwo',[
            'from'=>$from, 
            'to'=>$to, 
            'emp'=>$emp, 

         ]);
    }
    
    
    
//PaySalaryReport
      public function PaySalaryReport(){

          if(auth()->guard('admin')->user()->emp != 0){
                 $Employess=Employess::where("EmpSort",1)->where('Active',1)->where('id',auth()->guard('admin')->user()->emp)->get();
          }else{
                $Employess=Employess::where("EmpSort",1)->where('Active',1)->get(); 
          }
             
         return view('admin.HRReports.PaySalaryReport',['Employess'=>$Employess]);
    }

     public function NewEmpCheckSalary($Emp,$Month) {         
         
      $Sal=Employess::find($Emp);
      $x = Borrowa::where("Month",$Month)->where('Emp',$Emp)->get()->sum('Amount');    
      $pp = EmpsProducationPoint::where("Month",$Month)->where('Emp',$Emp)->get()->sum('Point');    
       $point=0; 
       $newpp=0; 
     
         $HoursValue=$Sal->Salary / $Sal->Hours_Numbers ; 
     $Qties=EmpsProducationQuantity::where('Emp',$Emp)->get();
     
foreach($Qties as $qty){
    
    if($qty->FromQ <= $pp){
           if($qty->ToQ <= $pp){
            $newpp=$pp - $qty->ToQ ; 
          $point+=$qty->ValueQ * ($pp - $newpp);  
                       
        }
        
        if($qty->ToQ >= $pp){
            if($newpp != 0){
        $point+=$qty->ValueQ * $newpp;   
            }else{
                
              $point+=$qty->ValueQ * $pp;       
            }
        }
        
    }
    
}
      $PaySalary = PaySalary::where("Month",$Month)->where('Emp',$Emp)->first();    

    $month=$Month.'01';
     
    $SETT=Settlement::
       where('Date','>=',$month)
     ->where('Account_Dificit',$Sal->Account)->get()->sum('Total_Dificit_Price');
 
         $saleslater=Sales::
          where('Status',1)
        ->where('Date','>=',$month)      
          ->where('Delegate',$Emp)
          ->where('Payment_Method','Later')
          ->where('Later_Collection',0)
              ->get() 
              ->sum('Total_Price');
     
    
     
     $LATER= $saleslater  ;
     
     
    $s=0;      
    $xx=0;      
    $xxx=0;      
       
     
        $sales=Sales::
          where('Status',1)
          ->where('Delegate',$Emp)
              ->get(); 
          
            $Ex=Sales::
          where('Status',1)
          ->where('Executor',$Emp)
              ->get();  
          
     
     $Ratios=EmpRatio::where('Emp',$Emp)->get();
     
          foreach($sales as $sel){

                $date=$sel->Date;
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $s += $sel->Total_Price;
            }
                
          }
          
           foreach($Ex as $ex){
             
                $date=$ex->Date;
              
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $xx += $ex->Total_Price;
            }
                
          }
          
          
      $PS  = $s  * ($Sal->Precentage_of_Sales / 100) ;
      $PEX = $xx   *  ($Sal->Precentage_of_Execution / 100) ;
          
        $DED=Deduction::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 
        $ENTIT=Entitlement::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 

        $EmpDets=EmpInstallmentDetails::where('Emp',$Emp)->get() ; 
          
          foreach($EmpDets as $emD){
             
                $date=$emD->Date;
              
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $xxx += $emD->Value;
            }
                
          }

 $OVER=RegOverTime::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 
 $Attendence=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Hours_Number') ; 
 $DiscountLate=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Disc_Late') ; 
 $DiscountDeparture=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Disc_Early') ; 


        $HourCost=$Sal->Salary / $Sal->Hours_Numbers ; 
        
          $disc= $Attendence * $HourCost ;
          $discT= $Sal->Salary - $disc ;
          
        $Holidays=Holidays::where('Month',$Month)->where('Emp',$Emp)->where('Discount',1)->get()->sum('Num_of_Days') ;   
          
          $WorkDay =  $Sal->Hours_Numbers / 30 ; 
          $HoliDiscount = ($HourCost * $WorkDay) * $Holidays ;
          
     
     if(!empty($PaySalary)){
         
         $New = 1 ;
         
     }else{
         
         $New = 0 ;
     }
     
     
     
     $Allowances=AllowencesEmp::where('Emp',$Emp)->get()->sum('AmountAllow');
$Discounts=DiscountsEmp::where('Emp',$Emp)->get()->sum('AmountDiscount');
 $FinValue= ( $Attendence * $HoursValue)  - ( $DiscountLate + $DiscountDeparture );
          $states=[];
 $states += ["Total" => $x ,'Salary' => $Sal->Salary , 'Sales' => $PS, 'Exec' =>$PEX , 'Dedu' =>$DED , 'Entit' =>$ENTIT , 'Loan' =>$xxx , 'Over' =>$OVER , 'HWork' =>$Sal->Hours_Numbers , 'Att' =>$Attendence  , 'AttDisc' =>$discT , 'Holi' =>$Holidays  , 'HoliDisc' =>$HoliDiscount,'New'=>$New,'Settle'=>$SETT,'later'=>$LATER,'Allowances'=>$Allowances,'Discounts'=>$Discounts,'Points'=>$point,'DiscountLate'=>$DiscountLate,'DiscountDeparture'=>$DiscountDeparture , 'HoursValue' =>$HoursValue,'FinValue'=>$FinValue];   


       return response()->json($states);
           
    }
   
    
    
    
    //SalaryPayed
    
         public function SalaryPayed(){
          if(auth()->guard('admin')->user()->emp != 0){
                 $Employess=Employess::where("EmpSort",1)->where('Active',1)->where('id',auth()->guard('admin')->user()->emp)->get();
          }else{
                $Employess=Employess::where("EmpSort",1)->where('Active',1)->get(); 
          }
         return view('admin.HRReports.SalaryPayed',['Employess'=>$Employess]);
    }
   
         public function SalaryPayedFilterTwo(Request $request){

             $month = $request->get('month');             
                    
      $emp = $request->get('emp');             
  

  
             
                 $items=PaySalary::where('Month', $month)
             
             
                      ->when(!empty($emp), function ($query) use ($emp) {
        return $query->where('Emp',$emp);  
               
                })  
              
             
             ->paginate(30); 
             
          
             
            
      $output = '';
          if($request->ajax())
     { 
              
       foreach($items as $item)
       {
                
                    
                                              if(!empty($item->Cost_Center()->first()->Arabic_Name)){
                  if(app()->getLocale() == 'ar' ){ 
               $CostName=$item->Cost_Center()->first()->Arabic_Name;
                  }else{
                     $CostName=$item->Cost_Center()->first()->English_Name;      
                      
                  }
           }else{
               
             $CostName='';  
           }
                               
                                                        
                                                        
                                                        
                                                        
                                                        
           if(!empty($item->Coin()->first()->Arabic_Name)){
                  if(app()->getLocale() == 'ar' ){ 
               $CoinName=$item->Coin()->first()->Arabic_Name;
                  }else{
                     $CoinName=$item->Coin()->first()->English_Name;      
                      
                  }
           }else{
               
             $CoinName='';  
           }
           
           
           
           
                 if(!empty($item->Safe()->first()->Name)){
                  if(app()->getLocale() == 'ar' ){ 
               $SafeName=$item->Safe()->first()->Name;
                  }else{
                     $SafeName=$item->Safe()->first()->NameEn;      
                      
                  }
           }else{
               
             $SafeName='';  
           }
           
           
           
           
             if(!empty($item->Emp()->first()->Name)){
                  if(app()->getLocale() == 'ar' ){ 
               $Name=$item->Emp()->first()->Name;
                  }else{
                     $Name=$item->Emp()->first()->NameEn;      
                      
                  }
           }else{
               
             $Name='';  
           }
           
           
        $output .= '
        <tr>

                                                    <td>'.$item->Code.'</td>
                                                    <td>'.$item->Date.'</td>
                                                    <td>'.$item->Month.'</td>
                                                    <td>'.$item->Salary.'</td>
                                                    <td>'.$item->Deduction.'</td>
                                                    <td>'.$item->Entitlement.'</td>
                                                    <td>'.$item->Borrow.'</td>
                                                    <td>'.$item->Overtime.'</td>
                                                    <td>'.$item->Attendence_Hours.'</td>
                                                    <td>'.$item->Attendence.'</td>
                                                    <td>'.$item->Loan.'</td>
                                                    <td>'.$item->Holidays.'</td>
                                                    <td>'.$item->Resduial_Salary.'</td>
                                                    <td>'.$item->Draw.'</td>
                                                    <td>'.$SafeName.'</td>
                                                    <td>'.$CoinName.'</td>
                                                    <td>'.$CostName.'</td>
                                                    <td>'.$Name.'</td>
                                                    <td>'.$item->Settlements.'</td>
                                                    <td>'.$item->Later_Sales_Bill.'</td>
                                                    <td>'.$item->Attendence_Discount.'</td>
                                                    <td>'.$item->Holiday_Discount.'</td>
                                                    <td>'.$item->Allowances.'</td>
                                                    <td>'.$item->Discounts.'</td>
                                                    <td>'.$item->DiscountLate.'</td>
                                                    <td>'.$item->DiscountDeparture.'</td>
                                                    <td>'.$item->ProducationPoints.'</td>
                                                
                                                </tr>
   
        ';   
 

       }
          
    return   $output ;       
          }      
          
         return view('admin.HRReports.SalaryPayedFilterTwo',[
            'month'=>$month, 
            'emp'=>$emp, 

         ]);
    }
    
    
    
    //EmpSalaries
        public function EmpSalaries(){
        $items=Employess::where('EmpSort',1)->where('id','!=',31)->where('id','!=',32)->where('id','!=',33)->where('id','!=',34)->paginate(100);
            
                        //Salries
              $EmpSalaries=Employess::where('EmpSort',1)
                  ->where('id','!=',31)
                  ->where('id','!=',32)
                  ->where('id','!=',33)
                  ->where('id','!=',34)
                  ->get()->Sum('Salary');
            
            //Emp Count
                      $EmpCount=Employess::where('EmpSort',1)
                  ->where('id','!=',31)
                  ->where('id','!=',32)
                  ->where('id','!=',33)
                  ->where('id','!=',34)
                  ->count();
            
            
            
         return view('admin.HRReports.EmpSalaries',[
             'items'=>$items,
             'EmpSalaries'=>$EmpSalaries,
             'EmpCount'=>$EmpCount,
         
         ]);
    }


    
    
}

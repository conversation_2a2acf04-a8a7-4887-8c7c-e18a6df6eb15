<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAcccountingManualsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('acccounting_manuals', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Name');
            $table->string('Type');
            $table->string('Parent');
            $table->string('Note')->nullable();
            $table->integer('User');
            $table->timestamps();
            $table->string('Account_Code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('acccounting_manuals');
    }
}
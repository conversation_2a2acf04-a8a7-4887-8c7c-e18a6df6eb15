<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupPagesPartTwoEComDesign extends Model
{
    use HasFactory;
    protected $table = 'sup_pages_part_two_e_com_designs';
      protected $fillable = [

        'Shop_Product_BG_Color',
        'Shop_Product_Group_BG_Color',
        'Shop_Product_Group_Txt_Color',
        'Shop_Product_Group_Hover_BG_Color',
        'Shop_Product_Group_Hover_Txt_Color',
        'Shop_Product_Icon_BG_Color',  
          'Shop_Product_Icon_Txt_Color',
        'Shop_Product_Icon_Hover_BG_Color',
        'Shop_Product_Icon_Hover_Txt_Color',
        'Shop_Product_Txt_Color',
        'Shop_Product_Price_Color',
        'Shop_Product_Rate_Color',
        'Shop_Filter_Title_Color',
        'Shop_Filter_Txt_Color',      
          'Shop_Filter_Search_BG_Color',
        'Shop_Filter_Search_Icon_BG_Color',
        'Shop_Filter_Search_Icon_Txt_Color',
        'Shop_Filter_Search_Icon_BG_Hover_Color',
        'Shop_Filter_Search_Icon_Txt_Hover_Color',
          
        'Cart_Button_BG_Color',
        'Cart_Button_Txt_Color',
        'Cart_Button_BG_Hover_Color',
        'Cart_Button_Txt_Hover_Color',
        'Cart_Box_BG_Color',  
          'Cart_Box_Title_Color',
        'Cart_Box_Txt_Color',
        'Cart_Box_Button_BG_Color',
        'Cart_Box_Button_Txt_Color',
        'Cart_Box_Button_BG_Hover_Color',
        'Cart_Box_Button_Txt_Hover_Color',
        'Cart_Cupon_Button_BG_Color',
        'Cart_Cupon_Button_Txt_Color',
        'Cart_Cupon_Button_BG_Hover_Color',
        'Cart_Cupon_Button_Txt_Hover_Color',        
          'Cart_Cupon_Input_BG_Color',
          
        'Checkout_Box_BG_Color',
        'Checkout_Box_Title_Color',
        'Checkout_Box_Txt_Color',
        'Checkout_Box_Button_BG_Color',
        'Checkout_Box_Button_Txt_Color',
        'Checkout_Box_Button_BG_Hover_Color',
        'Checkout_Box_Button_Txt_Hover_Color',
        'Checkout_Head_BG_Color',
        'Checkout_Head_Txt_Color',
        'Checkout_Input_BG_Color',
        'Checkout_Input_Txt_Color',
          
        'Reg_Login_Form_Box_BG_Color',
        'Reg_Login_Form_Box_Title_Color',
        'Reg_Login_Form_Box_Txt_Color',
        'Reg_Login_Form_Box_Txt_Hover_Color',
        'Reg_Login_Form_Box_Input_Border_Color',
        'Reg_Login_Form_Box_Button_BG_Color',
        'Reg_Login_Form_Box_Button_Txt_Color',
        'Reg_Login_Form_Box_Button_BG_Hover_Color',
        'Reg_Login_Form_Box_Button_Txt_Hover_Color',


    ];
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCapitalsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('capitals', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Authorized_Capital');
            $table->string('Source_Capital')->nullable();
            $table->string('Shares_Number')->nullable();
            $table->string('Nominal_Value_of_Shares')->nullable();
            $table->string('Actual_Share_Value')->nullable();
            $table->string('Actual_Capital')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('capitals');
    }
}
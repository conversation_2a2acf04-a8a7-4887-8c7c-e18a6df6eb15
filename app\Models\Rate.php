<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rate extends Model
{
    use HasFactory;
    
         protected $table = 'rates';
      protected $fillable = [
        'Rate',
        'Product',
        'User',
        
      
    ];
    
             public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
                 public function User()
    {
        return $this->belongsTo(Customers::class,'User');
    }
}

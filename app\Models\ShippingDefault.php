<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingDefault extends Model
{
    use HasFactory;
         protected $table = 'shipping_defaults';
      protected $fillable = [
        'Delegate',
        'Vendor',
        'Client',
        'Type',
        'Status',
        'Breakable',
        'Coin',
        'Safe',
        'Show_Coin',
        'Show_Draw',
        'Show_Safe',
        'Show_Code',
        'Show_Weight',
        'Show_Width',
        'Show_Length',
        'Show_Height',
        'Payment_Method',

    ];

      public function Status()
    {
        return $this->belongsTo(ShippingStatus::class,'Status');
    }
    
             public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
      public function Type()
    {
        return $this->belongsTo(ShippingType::class,'Type');
    }
    
            public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
            public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
    
 
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecivedSales extends Model
{
    use HasFactory;
       protected $table = 'recived_sales';
      protected $fillable = [
        'Code',
        'Date',
        'Total_Trans_Qty',
        'Total_Trans_Value',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Sales',
        'User',
    ];

           public function Sales()
    {
        return $this->belongsTo(Sales::class,'Sales');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

        public function RecivedSalesProducts()
    {
        return $this->hasOne(RecivedSalesProducts::class);
    }
}

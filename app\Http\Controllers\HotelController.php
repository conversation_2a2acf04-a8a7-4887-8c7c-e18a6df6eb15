<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\Rooms;
use App\Models\RoomsType;
use App\Models\Reservations;
use App\Models\RoomReservations;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\Customers;
use App\Models\Coins;
use App\Models\Countris;
use App\Models\AcccountingManual;
use App\Models\SalesDefaultData;
use App\Models\UsersMoves;
use App\Models\Event;
use App\Models\Notifications;
use DB ;
use Str ;
use App\Mail\AdminResetPassword;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use SpamProtector;
use Storage;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
  

class HotelController extends Controller
{
    
function __construct()
{

$this->middleware('permission:انواع الغرف', ['only' => ['RoomsType']]);
$this->middleware('permission:الغرف', ['only' => ['Rooms']]);
$this->middleware('permission:الحجز', ['only' => ['Reservations']]);
$this->middleware('permission:جدول الحجوزات', ['only' => ['Reservations_Sechdule']]);
$this->middleware('permission:تقرير الحجوزات', ['only' => ['ReservationsReport']]);


}  
    
    
    
      //======  RoomsType ======= 
       
        public function RoomsType(){
        $items=RoomsType::all();
         return view('admin.Hotels.RoomsType',['items'=>$items]);
    }
    
     public function AddRoomsType(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         RoomsType::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='انواع الغرف';
           $dataUser['ScreenEn']='Rooms Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
                    if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditRoomsType($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           RoomsType::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='انواع الغرف';
           $dataUser['ScreenEn']='Rooms Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteRoomsType($id){
               
  
        $del=RoomsType::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='انواع الغرف';
           $dataUser['ScreenEn']='Rooms Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
            //======  Rooms ======= 
     public function Rooms(){
        $items=Rooms::paginate(100);
        $RoomsTypes=RoomsType::all();
         return view('admin.Hotels.Rooms',['items'=>$items,'RoomsTypes'=>$RoomsTypes]);
    }
    
     public function AddRooms(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
            
             'Adults_Num'=>'required',
             'Childs_Num'=>'required',
             'Beds_Num'=>'required',
             'Desc'=>'required',
             'Price'=>'required',

             
               ],[
 
     
         ]);
   
         
           
         $data['Code']=request('Code');
         $data['Floor']=request('Floor');
         $data['Bulding_Name']=request('Bulding_Name');
         $data['RoomsType']=request('RoomsType');
         $data['Adults_Num']=request('Adults_Num');
         $data['Childs_Num']=request('Childs_Num');
         $data['Beds_Num']=request('Beds_Num');
         $data['Desc']=request('Desc');
         
                if(!empty(request('NameEn'))){
                $data['DescEn']=request('DescEn');
          }else{
                    $data['DescEn']=request('Desc');
              
          }


         $data['Price']=request('Price');
         $data['Reserved']=0;
         
      
        
         Rooms::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']=' الغرف';
           $dataUser['ScreenEn']='Rooms ';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditRooms($id){ 
         
        $data= $this->validate(request(),[
             'Code'=>'required',
            
             'Adults_Num'=>'required',
             'Childs_Num'=>'required',
             'Beds_Num'=>'required',
             'Desc'=>'required',
             'Price'=>'required',

     
             
               ],[
 
     
         ]);
   
         
           
         $data['Code']=request('Code');
         $data['Floor']=request('Floor');
         $data['Bulding_Name']=request('Bulding_Name');
         $data['RoomsType']=request('RoomsType');
         $data['Adults_Num']=request('Adults_Num');
         $data['Childs_Num']=request('Childs_Num');
         $data['Beds_Num']=request('Beds_Num');
         $data['Desc']=request('Desc');
         $data['DescEn']=request('DescEn');
         $data['Price']=request('Price');
    
         
      Rooms::where('id',$id)->update($data);
         
         
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']=' الغرف';
           $dataUser['ScreenEn']='Rooms ';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteRooms($id){
                      
        $del=Rooms::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                      $dataUser['Screen']=' الغرف';
           $dataUser['ScreenEn']='Rooms ';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    //Reservations
        public function Reservations(){
   
                      $res=Reservations::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }

            
            
               $Coins=Coins::all();  
 if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
   $Def=SalesDefaultData::orderBy('id','desc')->first();
          
           if($Def->V_and_C == 0){
                 $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
     }elseif($Def->V_and_C == 1){
         
               
                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
                  ->orWhere('Parent',37)
              ->get();
     
         
     }
         
               $ress=Customers::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $CodeUser=$ress->Code + 1 ; 
               
           }else{
               
              $CodeUser=1; 
               
           }
            $Nationality=Countris::all();
         return view('admin.Hotels.Reservations',[
             'Code'=>$Code,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Clients'=>$Clients,
             'CodeUser'=>$CodeUser,
             'Nationality'=>$Nationality,
         ]);
    }
    
         function RoomsFilter(Request $request)
             {

     if($request->ajax())
     {
      $output = '';
      $From = $request->get('From');
      $To = $request->get('To');

    if($From != '' and $To != '')
    {

        
        $Rooms=Rooms::all();
         foreach($Rooms as $room){

           $x=RoomReservations::where('Room',$room->id)
                 ->whereBetween('Date',[$From,$To])
                 ->get();
   
             if(!empty($room->RoomsType)){
                 
                     if(app()->getLocale() == 'ar' ){ 
                            $type= $room->RoomsType()->first()->Arabic_Name;
   }else{
                            $type= $room->RoomsType()->first()->English_Name;
       } 
                 
               
             }else{
                  $type= '';
             }
          
             
             
             if(count($x) == 0){
                 
                        if(app()->getLocale() == 'ar' ){ 
                
                      $decee=$room->Desc; 
                   
                   }else{
              
                         $decee=$room->DescEn;  
                       
                   }  
                 
         $output .= '
<div class="col-md-4"> 
<div class="animated"> 
                                        <div style="overflow:auto;">
        <table id="dt-basic-example" class="table table-bordered table-hover table-striped text-center ">
<input type="radio" class="form-control" name="Room" value="'.$room->id.'" onclick="Calculate('.$room->id.')">
<tr>
 <th>'.trans('admin.RoomCode').'</th>
<td>'.$room->Code.'</td>
</tr>

<tr>
 <th>'.trans('admin.RoomsType').'</th>
<td>'.$type.'</td>
</tr>

<tr>
 <th>'.trans('admin.Adults_Num').'</th>
<td>'.$room->Adults_Num.'</td>
</tr>
<tr>
 <th>'.trans('admin.Childs_Num').'</th>
<td>'.$room->Childs_Num.'</td>
</tr>
<tr>
 <th>'.trans('admin.Beds_Num').'</th>
<td>'.$room->Beds_Num.'</td>
</tr>
<tr>
  <th>'.trans('admin.Desc').'</th>
<td>'.$decee.'</td>
</tr>
<tr>
  <th>'.trans('admin.Price').'</th>
<td>'.$room->Price.'<input type="hidden" id="PRICE'.$room->id.'" value="'.$room->Price.'"></td>
</tr>
</table>
</div>
   </div>
              </div>

            ';
             }
        }
    
        
    
        
        
             }
        
        }

 
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }

     public function AddResrvation(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'From'=>'required',
             'To'=>'required',
             'Amount'=>'required',
             'Draw'=>'required',
             'Client'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Room'=>'required',
               ],[
 
     
         ]);
   
         
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['From']=request('From');
         $data['To']=request('To');
         $data['Amount']=request('Amount');
         $data['Checkout']=0;
         $data['Expire']=0;
         $data['Draw']=request('Draw');
         $data['Client']=request('Client');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['Room']=request('Room');
         $data['Note']=request('Note');
         $data['User']=auth()->guard('admin')->user()->id;

         Reservations::create($data);
         
         
         
                  $event['Start_Date']=request('From');
         $event['End_Date']=request('To');
         $event['Event_Ar_Name']='حجز غرفه';
         $event['Event_En_Name']='Room Reserved';
         $event['Type']='الحجوزات';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=null;
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);




         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='حجز غرفه';
         $notii['Noti_En_Name']='Room Reserved';
         $notii['Type']='الحجوزات';
  $notii['TypeEn']='Reservation';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=null;
         $notii['Client']=request('Client');
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=request('Safe');
         Notifications::create($notii);
         
              notify()->success(trans('admin.NewReservation'));


    
         $st_date = request('From');
$ed_date =request('To');

$dateMonthYearArr = array();
$st_dateTS = strtotime($st_date);
$ed_dateTS = strtotime($ed_date);

for ($currentDateTS = $st_dateTS; $currentDateTS <= $ed_dateTS; $currentDateTS += (60 * 60 * 24)) {

$currentDateStr = date('Y-m-d',$currentDateTS);
$dateMonthYearArr[] = $currentDateStr;

}


          for($i =0 ; $i < count($dateMonthYearArr) ; $i++){
              
                       $dataX['Room']=request('Room');
         $dataX['Date']=$dateMonthYearArr[$i];

         RoomReservations::create($dataX); 
          }
   
         
         
         
         
          $c= DB::select("SELECT last_value FROM reservations_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
         
           $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' =>'الحجز',
            'TypeEn' => 'Reservations',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => null,
  
        )
    );
  $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الحجز';
        $Gen['TypeEn']='Reservations';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * (request('Amount'));
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الحجز';
        $Gen['TypeEn']='Reservations';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
  
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الحجز';
           $dataUser['ScreenEn']='Reservations';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
      public function Reservations_Sechdule(){
        $items=Reservations::where('Checkout',0)->paginate(100);

          foreach($items as $item){
           
              if($item->To <= date('Y-m-d')){
                 Reservations::where('id',$item->id)->update(['Expire'=>1]); 
                  
              }
              
          }
          
          
         return view('admin.Hotels.Reservations_Sechdule',['items'=>$items]);
    }
    
            public function EditReserv($id){
   
                      $item=Reservations::find($id);
           
   
       RoomReservations::where('Room',$item->Room)
                 ->whereBetween('Date',[$item->From,$item->To])
                 ->delete();

            
            
               $Coins=Coins::all();  
 if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
   $Def=SalesDefaultData::orderBy('id','desc')->first();
          
           if($Def->V_and_C == 0){
                 $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
     }elseif($Def->V_and_C == 1){
         
               
                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
                  ->orWhere('Parent',37)
              ->get();
     
         
     }
         
               $ress=Customers::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $CodeUser=$ress->Code + 1 ; 
               
           }else{
               
              $CodeUser=1; 
               
           }
                 $Nationality=Countris::all();
         return view('admin.Hotels.EditReservations',[
             'item'=>$item,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Clients'=>$Clients,
             'CodeUser'=>$CodeUser,
             'Nationality'=>$Nationality,
         ]);
    }
    
         public function PostEditResrvation(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'From'=>'required',
             'To'=>'required',
             'Amount'=>'required',
             'Draw'=>'required',
             'Client'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Room'=>'required',
               ],[
 
     
         ]);
   
         $id=request('ID');
              $del=Reservations::find($id);
         
                
                 Journalizing::where('Code_Type',$del->Code)->where('Type','الحجز')->delete();
                 GeneralDaily::where('Code_Type',$del->Code)->where('Type','الحجز')->delete();
               Event::where('Type_Code',$del->Code)->where('Type','الحجوزات')->delete();
  
           Notifications::where('Type_Code',$del->Code)->where('Type','الحجوزات')->delete(); 
 
                
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['From']=request('From');
         $data['To']=request('To');
         $data['Amount']=request('Amount');
         $data['Checkout']=0;
         $data['Expire']=0;
         $data['Draw']=request('Draw');
         $data['Client']=request('Client');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['Room']=request('Room');
         $data['Note']=request('Note');
         $data['User']=auth()->guard('admin')->user()->id;

         Reservations::where('id',$id)->update($data);
         
    
                               $event['Start_Date']=request('From');
         $event['End_Date']=request('To');
         $event['Event_Ar_Name']='حجز غرفه';
         $event['Event_En_Name']='Room Reserved';
         $event['Type']='الحجوزات';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=null;
         $event['Client']=request('Client');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           
         

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='حجز غرفه';
         $notii['Noti_En_Name']='Room Reserved';
         $notii['Type']='الحجوزات';
  $notii['TypeEn']='Reservation';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=null;
         $notii['Client']=request('Client');
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=request('Safe');
         Notifications::create($notii);
         
              notify()->success(trans('admin.NewReservation'));

             
             
         $st_date = request('From');
$ed_date =request('To');

$dateMonthYearArr = array();
$st_dateTS = strtotime($st_date);
$ed_dateTS = strtotime($ed_date);

for ($currentDateTS = $st_dateTS; $currentDateTS <= $ed_dateTS; $currentDateTS += (60 * 60 * 24)) {

$currentDateStr = date('Y-m-d',$currentDateTS);
$dateMonthYearArr[] = $currentDateStr;

}


          for($i =0 ; $i < count($dateMonthYearArr) ; $i++){
              
                       $dataX['Room']=request('Room');
         $dataX['Date']=$dateMonthYearArr[$i];

         RoomReservations::create($dataX); 
          }
   
         
           $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
                  'Type' =>'الحجز',
            'TypeEn' => 'Reservations',
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => null,
  
        )
    );
  $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
              $Gen['Type']='الحجز';
        $Gen['TypeEn']='Reservations';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * (request('Amount'));
        $Gen['Account']=request('Client');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
                  $Gen['Type']='الحجز';
        $Gen['TypeEn']='Reservations';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
  
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='الحجز';
           $dataUser['ScreenEn']='Reservations';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
                       return redirect('Reservations_Sechdule'); 
        
    }
 
            public function DeleteReservation($id){
                      
        $del=Reservations::find($id);
         
                
                 Journalizing::where('Code_Type',$del->Code)->where('Type','الحجز')->delete();
                 GeneralDaily::where('Code_Type',$del->Code)->where('Type','الحجز')->delete();
                   Event::where('Type_Code',$del->Code)->where('Type','الحجوزات')->delete();
         Notifications::where('Type_Code',$del->Code)->where('Type','الحجوزات')->delete(); 

                  RoomReservations::where('Room',$del->Room)
                 ->whereBetween('Date',[$del->From,$del->To])
                 ->delete();
                
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
          $dataUser['Screen']='الحجز';
           $dataUser['ScreenEn']='Reservations';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['Explainِر']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
        public function CheckoutReservation($id){
                      
        $del=Reservations::find($id);
            
            
            
            
                     $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='انتهاء حجز غرفة';
         $notii['Noti_En_Name']='End Room Reservation';
         $notii['Type']='الحجوزات';
  $notii['TypeEn']='Reservation';
         $notii['Type_Code']=$del->Code;
         $notii['Emp']=null;
         $notii['Client']=$del->Client;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=$del->Safe;
         Notifications::create($notii);
         
              notify()->success(trans('admin.EndReservation'));
            
            
                  RoomReservations::where('Room',$del->Room)
                 ->whereBetween('Date',[$del->From,$del->To])
                 ->delete();
                
            Reservations::where('id',$id)->update(['Checkout'=>1]);
            

            
            
        session()->flash('success','Checkout');
        return back();

           }
    
    
         public function NewClientHotel(Request $request){

                        $code = $request->get('code'); 
                        $Name = $request->get('Name'); 
                        $PriceLevel = $request->get('PriceLevel'); 
                        $Phone = $request->get('Phone'); 
                        $ID_Numbe = $request->get('ID_Numbe'); 
                        $Passport_Number = $request->get('Passport_Number'); 
                        $Nationality = $request->get('Nationality'); 
                        $File = $request->get('File'); 
            

            $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();    
            $codee=AcccountingManual::find(24);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                        $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=$Name;
         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

           
        $ress=Customers::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $CodeUser=$ress->Code + 1 ; 
               
           }else{
               
              $CodeUser=1; 
               
           }
  
        $data['Code']=$CodeUser;
        $data['Date']=date('Y-m-d');
        $data['Name']=$Name;
        $data['Phone']=$Phone;
        $data['Price_Level']=$PriceLevel;
        $data['Account']=$Acc->id;  
               $data['ID_Number']=$ID_Numbe;
    $data['Passport_Number']=$Passport_Number;
  $data['Nationality']=$Nationality;
        $data['User']=auth()->guard('admin')->user()->id;

       $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ClientImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }
           
          Customers::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Clients');
           $dataUser['Type']=trans('admin.AddNew');
           $dataUser['Explain']=$Name;
           UsersMoves::create($dataUser);
         
            $states=['SUCEESS'=>'SUCEESS'];
           return response()->json($states);
        
    }
    
    
    
    //ReservationsReport
          public function ReservationsReport(){
   
$Rooms=Rooms::all();
   $Def=SalesDefaultData::orderBy('id','desc')->first();
          
           if($Def->V_and_C == 0){
                 $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
     }elseif($Def->V_and_C == 1){
         
               
                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
                  ->orWhere('Parent',37)
              ->get();
     
         
     }
         
          
         return view('admin.Hotels.ReservationsReport',[
             'Rooms'=>$Rooms,
             'Clients'=>$Clients,

         ]);
    }

          function ReservReportFilterrr(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $From = $request->get('From');             
      $To = $request->get('To');             
      $Room = $request->get('Room');             
      $client = $request->get('client');             
             
  
    if($From != '' and $To != '')
    {

          if($Room != '' and $client != '')
    {
         $Prods=Reservations::      
            whereBetween('To',[$From,$To])                         
            ->where('Client',$client)     
            ->where('Room',$Room)     
          ->get();  
              
          }elseif($Room == '' and $client != ''){
              $Prods=Reservations::      
            whereBetween('To',[$From,$To])                         
            ->where('Client',$client)        
          ->get();  
          }elseif($Room != '' and $client == ''){
              $Prods=Reservations::      
            whereBetween('To',[$From,$To])                            
            ->where('Room',$Room)     
          ->get();  
          }elseif($Room == '' and $client == ''){
              $Prods=Reservations::      
            whereBetween('To',[$From,$To])                             
          ->get();  
          }
                       
     }   
          
               
         $total_row = $Prods->count();
      if($total_row > 0) 
      { 
   
          foreach($Prods as $pro){
              
                     if(app()->getLocale() == 'ar' ){ 
                      $xName=$pro->Client()->first()->Name; 
                   
                   }else{
                         $xName=$pro->Client()->first()->NameEn;  
                       
                   }  

                  $output .= '
       <tr>
       <td>'.$pro->Code.'</td>
       <td>'.$pro->Date.'</td>
       <td>'.$pro->From.'</td>
       <td>'.$pro->To.'</td>
       <td>'.$pro->Amount.'
       <input type="hidden" class="Tot" value="'.$pro->Amount.'">
       </td>
       <td>'.$xName.'</td>
       <td>'.$pro->Room()->first()->Code.'</td>
       
       </tr>
       ';
          }
  
      }else
      {
       $output = '
        <div class="col-md-3"> '.trans('admin.No_Data_Find').'  </div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    



    
}

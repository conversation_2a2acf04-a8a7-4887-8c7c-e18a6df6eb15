<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('roles')->delete();
        
        \DB::table('roles')->insert(array (
            0 => 
            array (
                'id' => 15,
                'name' => 'Employee',
                'guard_name' => 'admin',
                'created_at' => '2021-08-19 17:57:19',
                'updated_at' => '2021-08-19 17:57:19',
            ),
            1 => 
            array (
                'id' => 17,
                'name' => 'Owner',
                'guard_name' => 'admin',
                'created_at' => '2021-08-19 21:53:01',
                'updated_at' => '2021-08-19 21:53:01',
            ),
            2 => 
            array (
                'id' => 18,
                'name' => 'Guest',
                'guard_name' => 'admin',
                'created_at' => '2021-12-14 17:22:16',
                'updated_at' => '2021-12-14 17:22:16',
            ),
            3 => 
            array (
                'id' => 19,
                'name' => 'موظفين',
                'guard_name' => 'admin',
                'created_at' => '2021-12-28 11:53:38',
                'updated_at' => '2021-12-28 11:53:38',
            ),
            4 => 
            array (
                'id' => 21,
                'name' => 'المبيعات',
                'guard_name' => 'admin',
                'created_at' => '2022-03-10 22:49:39',
                'updated_at' => '2022-03-10 22:49:39',
            ),
        ));
        
        
    }
}
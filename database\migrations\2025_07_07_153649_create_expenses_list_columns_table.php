<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExpensesListColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('expenses_list_columns', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code_Type');
            $table->string('Statement');
            $table->string('Debitor');
            $table->string('Cost_Center');
            $table->string('Coin');
            $table->string('User');
            $table->string('Account');
            $table->string('Branch');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('expenses_list_columns');
    }
}
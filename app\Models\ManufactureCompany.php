<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufactureCompany extends Model
{
    use HasFactory;
         protected $table = 'manufacture_companies';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
            
    ];
    
    public function DevicesTypesy()
    {
        return $this->hasOne(DevicesTypesy::class);
    }
    
            public function ReciptMaintaince()
    {
        return $this->hasOne(ReciptMaintaince::class);
    }
    
    
}

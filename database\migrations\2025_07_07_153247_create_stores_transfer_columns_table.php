<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresTransferColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stores_transfer_columns', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code');
            $table->string('Time');
            $table->string('Amount');
            $table->string('From_Store');
            $table->string('To_Store');
            $table->string('User');
            $table->string('Coin');
            $table->string('Shipping');
            $table->string('Note');
            $table->string('Delegate');
            $table->string('Product_Name');
            $table->string('Product_Code');
            $table->string('Group');
            $table->string('Brand');
            $table->string('Qty');
            $table->string('Price');
            $table->string('Trans_Qty');
            $table->string('Unit');
            $table->string('Total');
            $table->timestamps();
            $table->string('Branch');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stores_transfer_columns');
    }
}
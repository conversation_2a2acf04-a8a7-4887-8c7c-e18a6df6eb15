<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstallmentCompanies extends Model
{
    use HasFactory;
              protected $table = 'installment_companies';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Logo',
        'Account',
        'NakdyaAccount',

    
    ];
    
            public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
              public function NakdyaAccount()
    {
        return $this->belongsTo(AcccountingManual::class,'NakdyaAccount');
    }
}

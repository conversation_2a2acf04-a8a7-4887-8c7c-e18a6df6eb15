<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SecretariatExportGoods extends Model
{
    use HasFactory;
       protected $table = 'secretariat_export_goods';
      protected $fillable = [
        'Code',
        'Date',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Account',
        'Store',
        'StoreGoods',
        'User',
        'Status',
        'Total_Recived_Qty',
        'Total_Price',
        'Draw',
        'Coin',
        'Safe',
          

    ];

 
          public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }
            public function StoreGoods()
    {
        return $this->belongsTo(Stores::class,'StoreGoods');
    }

              public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    

          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendors extends Model
{
    use HasFactory;
      protected $table = 'vendors';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Phone',
        'Phone2',
        'Commercial_Register',
        'Tax_Card',
        'Price_Level',
        'Account',
        'User',
        'Governrate',
        'City',
        'Place',
          'Tax_Registration_Number',
          'Tax_activity_code',
          'work_nature',
          'Nationality',
          'Buliding_Num',
          'Street',
          'Postal_Code',
          'tax_magistrate',
            'Floor',
          'Room',
          'Landmark',
          'Add_Info',
             'Responsible',
             'Pro_Group',
             'Brand',
             'SearchCode',

     
    ];
    
    
       public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
           public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
                  public function Governrate()
    {
        return $this->belongsTo(Governrate::class,'Governrate');
    }
    
                   public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
                   public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }
    
                  public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }
                  public function Responsible()
    {
        return $this->belongsTo(Employess::class,'Responsible');
    }
    
                      public function Pro_Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Pro_Group');
    }
    
                      public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }
}

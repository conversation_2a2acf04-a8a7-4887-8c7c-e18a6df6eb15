<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllPaymentVoucher implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
   
        
        
          if(app()->getLocale() == 'ar' ){ 
           $prods = DB::table('payment_vouchers')
               
               
            ->leftJoin('payment_voucher_details', function ($join) {
    
            $join->on('payment_vouchers.id', '=', 'payment_voucher_details.PV_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('payment_vouchers.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('payment_voucher_details.Account', '=', 'acccounting_manuals.id');
        })     
               
            ->leftJoin('safes_banks', function ($join) {
    
            $join->on('payment_vouchers.Safe', '=', 'safes_banks.Account');
        })      
        
          
        
->select('payment_vouchers.Date'
         ,'payment_vouchers.Code'
         ,'safes_banks.Name as SafeName'
         ,'coins.Arabic_Name as Coin'
         ,'payment_voucher_details.Debitor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.Name as AccountName'
         ,'payment_voucher_details.Statement'
        
         
        )
                  ->get();
 
              
          }else{
              
                 $prods = DB::table('payment_vouchers')
               
               
            ->leftJoin('payment_voucher_details', function ($join) {
    
            $join->on('payment_vouchers.id', '=', 'payment_voucher_details.PV_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('payment_vouchers.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('payment_voucher_details.Account', '=', 'acccounting_manuals.id');
        })     
               
            ->leftJoin('safes_banks', function ($join) {
    
            $join->on('payment_vouchers.Safe', '=', 'safes_banks.Account');
        })      
        
          
        
->select('payment_vouchers.Date'
         ,'payment_vouchers.Code'
         ,'safes_banks.NameEn as SafeName'
         ,'coins.Arabic_NameEn as Coin'
         ,'payment_voucher_details.Debitor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'payment_voucher_details.Statement'
        
         
        )
                  ->get();
  
              
              
          }
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Safe',
          'Coin',
          'Debitor',
          'Account Code',
          'Account Name',
          'Statement',
        ];
    }
    
    
    

}

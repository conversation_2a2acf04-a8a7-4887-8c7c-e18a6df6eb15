<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ImpBrands implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         //DB::table('products')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('brands')->insert([

             'Name'	   =>$value[1]
            ,'Note'  =>$value[2]
            ,'created_at'  =>$value[3]
            ,'updated_at'  =>$value[4]
            ,'Image'  =>$value[5]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

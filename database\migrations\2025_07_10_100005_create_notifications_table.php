<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->integer('Status')->default(0);
            $table->string('Noti_Ar_Name');
            $table->string('Noti_En_Name');
            $table->string('Type')->nullable();
            $table->string('TypeEn')->nullable();
            $table->string('Type_Code')->nullable();
            $table->string('Emp')->nullable();
            $table->string('Client')->nullable();
            $table->string('Product')->nullable();
            $table->string('Store')->nullable();
            $table->string('Safe')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaySalary extends Model
{
    use HasFactory;
      protected $table = 'pay_salaries';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Salary',
        'Pre_Sales',
        'Pre_Execu',
        'Deduction',
        'Entitlement',
        'Borrow',
        'Overtime',
        'Attendence_Hours',
        'Attendence',
        'Loan',
        'Holidays',
        'Resduial_Salary',
        'Note',
        'Draw',
        'Safe',
        'Coin',
        'Cost_Center',
        'Emp',
        'User',
        'Attendence_Discount',
        'Holiday_Discount',
        'Settlements',
        'Later_Sales_Bill',
        'Allowances',
        'Discounts',
        'ProducationPoints',
        'DiscountLate',
        'DiscountDeparture',
        'Shipping_Precent',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }

    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
  
    
}

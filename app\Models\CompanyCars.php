<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyCars extends Model
{
    use HasFactory;
             protected $table = 'company_cars';
      protected $fillable = [
        'Name',
        'NameEn',
        'Number',
        'Account',

     
    ];
    
    
      public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoresDefaultData extends Model
{
    use HasFactory;
          protected $table = 'stores_default_data';
      protected $fillable = [
        'Group',
        'Unit',
        'Tax',
        'Coin',
        'Account_Excess',
        'Account_Dificit',
        'Store',
        'Type',
        'Style',
        'StoresTarnsferPrice',
        'Guide_Product_Cost',
        'Client_Store_Account',
        'Show_Ship',
 'StoresTarnsferHide',
 'CodeType',
 'ReturnStoresTransfer',
 'Cost_Price',
       

   
    ];
    
          public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }
    
    
        public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
    
     public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
               public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
               public function Account_Excess()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Excess');
    }
    
              public function Account_Dificit()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Dificit');
    }
    
    
}

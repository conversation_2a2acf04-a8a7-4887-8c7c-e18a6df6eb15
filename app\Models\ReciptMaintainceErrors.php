<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReciptMaintainceErrors extends Model
{
    use HasFactory;
         protected $table = 'recipt_maintaince_errors';
      protected $fillable = [
        'Recipt',
        'Error',
        'Recived',
        'Eng',
      
    ];

           public function Recipt()
    {
        return $this->belongsTo(ReciptMaintainceErrors::class,'Recipt');
    }


        public function Error()
    {
        return $this->belongsTo(DeviceDescrips::class,'Error');
    }    
    
    
}

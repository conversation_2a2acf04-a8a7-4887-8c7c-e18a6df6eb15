<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\ResturantStyle;

class ReviewsResurantUse
{
    public function handle($request, Closure $next)
    {

        $styleX=ResturantStyle::orderBy('id','desc')->first();
                if($styleX->Reviews !=  1)
                {
                   return redirect('/');
                }       

        return $next($request);
    }
}




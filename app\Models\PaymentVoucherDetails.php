<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentVoucherDetails extends Model
{
    use HasFactory;
         protected $table = 'payment_voucher_details';
      protected $fillable = [
        'Debitor',
        'PV_ID',
        'Account',
        'Statement',
        'Date',
        'Time',
        'Cost_Center',
        'Coin',
        'User',
        'Branch',
        'Safe',
       
    ];
    
        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
           public function PV_ID()
    {
        return $this->belongsTo(PaymentVoucher::class,'PV_ID');
    }
     
       public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
        public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
            public function User()
    {
        return $this->belongsTo(Employess::class,'User');
    }
}

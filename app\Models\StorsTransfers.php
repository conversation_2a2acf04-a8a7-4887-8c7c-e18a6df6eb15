<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StorsTransfers extends Model
{
    use HasFactory;
      protected $table = 'stors_transfers';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Total',
        'TotalQty',
        'Note',
        'From_Store',
        'To_Store',
        'Coin',
        'Cost_Center',
        'User',
        'Status',
        'Ship',
        'CostShip',
        'RecivedShip',
        'OldQty',
        'Edit',
        'Delegate',
        'Time',
        'Branch',
        'Total_Cost',
        'TypeTransfer',
        'Cost_Store',
        'File',
       
    ];
    
        public function From_Store()
    {
        return $this->belongsTo(Stores::class,'From_Store');
    }
    
            public function To_Store()
    {
        return $this->belongsTo(Stores::class,'To_Store');
    }
    
            public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
            public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
            public function Ship()
    {
        return $this->belongsTo(ShippingCompany::class,'Ship');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
                   public function ProductsStoresTransfers()
    {
        return $this->hasOne(ProductsStoresTransfers::class);
    }
              public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
                    public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
}

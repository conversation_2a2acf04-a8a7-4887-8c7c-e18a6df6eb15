<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RoomReservations extends Model
{
    use HasFactory;
      protected $table = 'room_reservations';
      protected $fillable = [
        'Date',
        'Room',

   
    ];
    
    
      public function Room()
    {
        return $this->belongsTo(Rooms::class,'Room');
    }
    
    
}

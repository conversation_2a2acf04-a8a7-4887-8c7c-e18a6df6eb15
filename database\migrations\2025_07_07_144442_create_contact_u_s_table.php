<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContactUSTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_u_s', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Map')->nullable();
            $table->string('Arabic_Title');
            $table->string('English_Title');
            $table->text('Arabic_Desc');
            $table->text('English_Desc');
            $table->string('Opening_Hours')->nullable();
            $table->string('Phone1')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Phone_Header')->nullable();
            $table->string('Email')->nullable();
            $table->string('Arabic_Address')->nullable();
            $table->string('English_Address')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_u_s');
    }
}
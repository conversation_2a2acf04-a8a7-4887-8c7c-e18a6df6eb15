<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;
               protected $table = 'events';
      protected $fillable = [
        'Start_Date',
        'End_Date',
        'Event_Ar_Name',
        'Event_En_Name',
        'Type',
        'Type_ID',      
        'Type_Code',
        'Emp',
        'Client',
        'Product',
        'Customer',

       
    ];
    
        public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }      
    
    public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }      
    
    public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }       
    
    public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }
}



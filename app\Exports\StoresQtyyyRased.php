<?php

namespace App\Exports;

use App\Models\ProductsQty;
use App\Models\ProductUnits;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use DB;
class StoresQtyyyRased implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      

        $storex=$this->store;
        $storee =  $storex['store'];
         $group = $storex['group'] ;
        $brand =  $storex['brand'] ;
         $branch = $storex['branch'] ;
        $product_Name =   $storex['product_Name'] ;
         $product_Code = $storex['product_Code'] ;
         $zero = $storex['zero'] ;
        
                 if(app()->getLocale() == 'ar' ){  
      if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
            ->where('products.P_Type', '!=', 'Serial')       
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->distinct(['products_qties.Product'])->select('products_qties.P_Ar_Name','products_qties.P_Code','measuerments.Name as UnitName','product_units.Rate as UnitRate','products_qties.Qty','stores.Name as StoreName')
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
            ->where('products.P_Type', '!=', 'Serial')       
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->distinct(['products_qties.Product'])->select('products_qties.P_Ar_Name','products_qties.P_Code','measuerments.Name as UnitName','product_units.Rate as UnitRate','products_qties.Qty','stores.Name as StoreName')
                  ->get();
        
      }
                 }else{
                     
            if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
            ->where('products.P_Type', '!=', 'Serial')       
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->distinct(['products_qties.Product'])->select('products_qties.P_En_Name','products_qties.P_Code','measuerments.NameEn as UnitName','product_units.Rate as UnitRate','products_qties.Qty','stores.NameEn as StoreName')
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
            ->where('products.P_Type', '!=', 'Serial')       
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->distinct(['products_qties.Product'])->select('products_qties.P_En_Name','products_qties.P_Code','measuerments.NameEn as UnitName','product_units.Rate as UnitRate','products_qties.Qty','stores.NameEn as StoreName')
                  ->get();
        
      }           
                 }
 

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'P_Ar_Name',
          'P_Code',
          'Unit',
          'Rate',
          'Qty',
          'Store'
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReciptVoucherDetails extends Model
{
    use HasFactory;
     protected $table = 'recipt_voucher_details';
      protected $fillable = [
        'Creditor',
        'RV_ID',
        'Account',
        'Statement',
       
    ];
    
        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
           public function RV_ID()
    {
        return $this->belongsTo(ReciptVoucher::class,'RV_ID');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSafeTransferColumnsSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('safe_transfer_columns_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Code');
            $table->string('Time');
            $table->string('Amount');
            $table->string('From_Safe');
            $table->string('To_Safe');
            $table->string('User');
            $table->string('Coin');
            $table->string('Cost_Center');
            $table->string('Note');
            $table->string('Delegate');
            $table->timestamps();
            $table->string('Branch');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('safe_transfer_columns_sechdules');
    }
}
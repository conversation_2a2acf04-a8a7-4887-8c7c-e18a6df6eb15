<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProfitSalesProductColumnSechdulrsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('profit_sales_product_column_sechdulrs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Product_Code');
            $table->string('Product_Name');
            $table->string('Unit');
            $table->string('Qty');
            $table->string('Price');
            $table->string('Cost');
            $table->string('Profit');
            $table->string('Store');
            $table->string('Branch');
            $table->string('Group');
            $table->string('Brand')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('profit_sales_product_column_sechdulrs');
    }
}
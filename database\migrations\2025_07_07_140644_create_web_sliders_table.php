<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebSlidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('web_sliders', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('Status')->default(1);
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->text('English_Desc')->nullable();
            $table->string('Image')->nullable();
            $table->string('Type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_sliders');
    }
}

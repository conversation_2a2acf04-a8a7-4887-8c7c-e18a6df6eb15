<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductJobOrder extends Model
{
    use HasFactory;
       protected $table = 'product_job_orders';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'Price',
        'Discount',
        'TDiscount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
          
        'Length',
        'Width',
        'Height',
        'Thickness',
        'Size',
          
        'Product',
        'Unit',
        'Order',
          
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
               public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Order()
    {
        return $this->belongsTo(JobOrder::class,'Order');
    }
                public function SubVID()
    {
        return $this->belongsTo(SubVirables::class,'SubVID');
    }  
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
                  public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
    
         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
    
              public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }
    
              public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }
}

<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllOpeningEntries implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
   
            if(app()->getLocale() == 'ar' ){ 
           $prods = DB::table('opening_entries')
               
               
            ->leftJoin('opening_entries_details', function ($join) {
    
            $join->on('opening_entries.id', '=', 'opening_entries_details.OP_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('opening_entries.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('opening_entries_details.Account', '=', 'acccounting_manuals.id');
        })     
         
   

->select('opening_entries.Date'
         ,'opening_entries.Code'
         ,'coins.Arabic_Name as Coin'
         ,'opening_entries_details.Debitor'
         ,'opening_entries_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.Name as AccountName'
         ,'opening_entries_details.Statement'
        
         
        )
                  ->get();
 
            }else{
                
               $prods = DB::table('opening_entries')
               
               
            ->leftJoin('opening_entries_details', function ($join) {
    
            $join->on('opening_entries.id', '=', 'opening_entries_details.OP_ID');
        })
                  
                   ->leftJoin('coins', function ($join) {
    
            $join->on('opening_entries.Coin', '=', 'coins.id');
        })
              
      
           ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('opening_entries_details.Account', '=', 'acccounting_manuals.id');
        })     
         
   

->select('opening_entries.Date'
         ,'opening_entries.Code'
         ,'coins.English_Name as Coin'
         ,'opening_entries_details.Debitor'
         ,'opening_entries_details.Creditor'
         ,'acccounting_manuals.Code as AccountCode'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'opening_entries_details.Statement'
        
         
        )
                  ->get();      
                
                
            }
                
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Coin',
          'Debitor',
          'Creditor',
          'Account Code',
          'Account Name',
          'Statement',
        ];
    }
    
    
    

}

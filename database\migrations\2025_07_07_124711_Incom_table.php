<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class IncomTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('incom_checks', function(Blueprint $table) {
            if (!Schema::hasColumn('incom_checks', 'Image')) {
                $table->longText('Image')->nullable();
            }
            if (!Schema::hasColumn('incom_checks', 'Signture_Name')) {
                $table->longText('Signture_Name')->nullable();
            }
            if (!Schema::hasColumn('incom_checks', 'Bank_Branch')) {
                $table->longText('Bank_Branch')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('incom_checks', function(Blueprint $table) {
            if (Schema::hasColumn('incom_checks', 'Image')) {
                $table->dropColumn('Image');
            }
            if (Schema::hasColumn('incom_checks', 'Signture_Name')) {
                $table->dropColumn('Signture_Name');
            }
            if (Schema::hasColumn('incom_checks', 'Bank_Branch')) {
                $table->dropColumn('Bank_Branch');
            }
        });
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExecutingReceivingSecretariat extends Model
{
    use HasFactory;
      protected $table = 'executing_receiving_secretariats';
      protected $fillable = [
        'Code',
        'Date',
        'Qty',
        'Total',
        'Total_Workmanship_Price',
        'Model',
        'User',
        'Time',
        'Branch',
        'StoreIn',
        'StoreOut',
        'Client',

    ];
    
    
       public function Model()
    {
        return $this->belongsTo(ManufacturingSecretariatModel::class,'Model');
    }
    
    
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
               public function StoreIn()
    {
        return $this->belongsTo(SecretariatStores::class,'StoreIn');
    }
    
                  public function StoreOut()
    {
        return $this->belongsTo(Stores::class,'StoreOut');
    }
    
                      public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
    
    
}

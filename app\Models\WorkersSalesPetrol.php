<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkersSalesPetrol extends Model
{
    use HasFactory;
      protected $table = 'workers_sales_petrols';
      protected $fillable = [
        'Worker',
        'SalesPetrol',       
    ];

            public function Worker()
    {
        return $this->belongsTo(Employess::class,'Worker');
    }
    
             public function SalesPetrol()
    {
        return $this->belongsTo(SalesPetrol::class,'SalesPetrol');
    }
    
}

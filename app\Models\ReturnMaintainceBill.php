<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnMaintainceBill extends Model
{
    use HasFactory;
     protected $table = 'return_maintaince_bills';
      protected $fillable = [
        'Code',
        'Date',
        'Note',
        'Serial_Num',
        'Total',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_Bf_Taxes',
        'Total_Taxes',
        'Draw',
        'Company',
        'Device_Type',  
        'Device_Case',
        'Coin',
        'Cost_Center',
        'Account',
        'User',
        'Pattern_Image',
        'Time',
        'Payment_Method',
        'Password',
        'Pay',
        'Work',
        'Store',
        'Eng',
        'Recipient',
        'Branch',
        'Total_Cost',
        'Cost_Precent',
        'Bill',
        'CustomerGroup',
        'Total_Talf',

          
          

    ];

           public function Company()
    {
        return $this->belongsTo(ManufactureCompany::class,'Company');
    }
    
               public function Device_Type()
    {
        return $this->belongsTo(DevicesTypesy::class,'Device_Type');
    }
    
               public function Device_Case()
    {
        return $this->belongsTo(DesviceCases::class,'Device_Case');
    }
    
               public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
               public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
               public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

  
            public function ProductsReturnMaintainceBill()
    {
        return $this->hasOne(ProductsReturnMaintainceBill::class);
    }
    
    
    public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }
    
    public function Eng()
    {
        return $this->belongsTo(Employess::class,'Eng');
    }
    
    public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    

    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
use App\Models\ModuleSettingsNum;
class EXPIRED
{    
     public function handle($request, Closure  $next=null,$guard=null)
    {
         
         
         $Date=ModuleSettingsNum::orderBy('id','desc')->first();
         if($Date->System == 1){  
             
             if(auth()->guard('admin')->user()->email != "<EMAIL>"){
                if($Date->Expire_Date < date('Y-m-d')){  
               return redirect('ReSubscribtion');
                    
                }
             }
                    }
         
         
             return $next($request);

      
    }
    
    
}

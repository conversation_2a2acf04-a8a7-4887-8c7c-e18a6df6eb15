<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReserveCourse extends Model
{
    use HasFactory;
         protected $table = 'reserve_courses';
      protected $fillable = [
        'Code',
        'Course',
        'Start_Date',
        'End_Date',
        'Course_Type',
        'Teacher',
        'Required_Number',
        'Certificate',
        'Cost',
        'Total_Required',
        'Status',
        'Days',
        'Time',
        'Total_Paid',
        'Total_Num',
        'Total_Cost',
        'Safe',
        'Draw',
        'Coin',
        'Hall',
    ];

         public function Course()
    {
        return $this->belongsTo(Courses::class,'Course');
    }
         public function Course_Type()
    {
        return $this->belongsTo(CoursesType::class,'Course_Type');
    }
    
         public function Teacher()
    {
        return $this->belongsTo(Teachers::class,'Teacher');
    }
    
            public function Hall()
    {
        return $this->belongsTo(CoursesHalls::class,'Hall');
    } 
}

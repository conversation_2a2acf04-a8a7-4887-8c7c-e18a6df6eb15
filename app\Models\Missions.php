<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Missions extends Model
{
    use HasFactory;
          protected $table = 'missions';
      protected $fillable = [

                'Name',
                'NameEn',
                'Start_Date',
                'End_Date',
                'Duration',
                'Value',
                'Status',
                'File',
                'Desc',
                'Task_Owner',
                'Observer',
                'Project',
                'User',
            

    ];
    
        public function Task_Owner()
    {
        return $this->belongsTo(Employess::class,'Task_Owner');
    }
    
           public function Observer()
    {
        return $this->belongsTo(Employess::class,'Observer');
    }
    
    
           public function Project()
    {
        return $this->belongsTo(Projects::class,'Project');
    }
    
           public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubVirables extends Model
{
    use HasFactory;
      protected $table = 'sub_virables';
      protected $fillable = [
        'Name',
        'NameEn',
        'V_ID',
    ];
    
          public function V_ID()
    {
        return $this->belongsTo(Virables::class,'V_ID');
    }
    
               public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }
    
         public function ProductsStartPeriods()
    {
        return $this->hasOne(ProductsStartPeriods::class);
    }
    
             public function ProductsStores()
    {
        return $this->hasOne(ProductsStores::class);
    }
    
             public function ProductsVira()
    {
        return $this->hasOne(ProductsVira::class);
    } 
    
                 public function ProductsQty()
    {
        return $this->hasOne(ProductsQty::class);
    } 
    
           public function ProductInventory()
    {
        return $this->hasOne(ProductInventory::class);
    }
    
            public function ProductSettlement()
    {
        return $this->hasOne(ProductSettlement::class);
    }
    
                   public function ProductsStoresTransfers()
    {
        return $this->hasOne(ProductsStoresTransfers::class);
    }
    
     public function BarcodeProducts()
    {
        return $this->hasOne(BarcodeProducts::class);
    }
    
              public function ProductsPurchasesOrder()
    {
        return $this->hasOne(ProductsPurchasesOrder::class);
    }
    
                  public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }
    
                          public function RecivedPurchProducts()
    {
        return $this->hasOne(RecivedPurchProducts::class);
    }
    
        public function ReturnPurchProducts()
    {
        return $this->hasOne(ReturnPurchProducts::class);
    }
    
              public function ProductsQuote()
    {
        return $this->hasOne(ProductsQuote::class);
    }
    
           public function ProductSales()
    {
        return $this->hasOne(ProductSales::class);
    }
    
         public function ProductSalesOrder()
    {
        return $this->hasOne(ProductSalesOrder::class);
    }
    
           public function RecivedSalesProducts()
    {
        return $this->hasOne(RecivedSalesProducts::class);
    }
    
          public function ReturnSalesProducts()
    {
        return $this->hasOne(ReturnSalesProducts::class);
    }
    
    
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employesses', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code')->nullable();
            $table->string('Name')->nullable();
            $table->string('Emp_Type')->nullable();
            $table->string('Salary')->nullable();
            $table->string('Attendence')->nullable();
            $table->string('Departure')->nullable();
            $table->string('Hours_Numbers')->nullable();
            $table->string('Days_Numbers')->nullable();
            $table->string('Day_Price')->nullable();
            $table->string('Precentage_of_Sales')->nullable();
            $table->string('Precentage_of_Profits')->nullable();
            $table->string('Image')->nullable();
            $table->string('Bank_Account')->nullable();
            $table->string('Qualifications')->nullable();
            $table->string('Address')->nullable();
            $table->string('Social_Status')->nullable();
            $table->string('ID_Number')->nullable();
            $table->string('Contract_Start')->nullable();
            $table->string('Contract_End')->nullable();
            $table->string('Phone')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Email')->nullable();
            $table->string('Password')->nullable();
            $table->integer('Job')->nullable();
            $table->integer('Department')->nullable();
            $table->integer('Account')->nullable();
            $table->integer('Account_Emp')->nullable();
            $table->integer('User')->nullable();
            $table->timestamps();
            $table->string('Note')->nullable();
            $table->string('Precentage_of_Execution')->nullable();
            $table->integer('Covenant')->nullable();
            $table->integer('Commission')->nullable();
            $table->string('Price_Level')->nullable();
            $table->string('Bill_Num')->nullable();
            $table->string('NumbersOfBill')->nullable();
            $table->string('EmpSort')->nullable();
            $table->string('CV')->nullable();
            $table->string('ID_Image')->nullable();
            $table->string('Criminal_status')->nullable();
            $table->string('Contract')->nullable();
            $table->string('health_certificate')->nullable();
            $table->string('Search_Card')->nullable();
            $table->string('Recruitment_certificate')->nullable();
            $table->string('employee_profile')->nullable();
            $table->string('duration_criminal_investigation')->nullable();
            $table->string('Birthdate')->nullable();
            $table->string('Attitude_recruiting')->nullable();
            $table->string('Job_Number')->nullable();
            $table->string('date_resignation')->nullable();
            $table->string('Living')->nullable();
            $table->string('Branch')->nullable();
            $table->string('Level')->nullable();
            $table->string('Religion')->nullable();
            $table->string('Insurance_salary')->nullable();
            $table->string('Insurance_companies')->nullable();
            $table->string('Previous_experience')->nullable();
            $table->string('Nationality')->nullable();
            $table->string('MonthlyTarget')->nullable();
            $table->string('QuarterTarget')->nullable();
            $table->string('SemiTarget')->nullable();
            $table->string('YearlyTarget')->nullable();
            $table->string('IDExpireDate')->nullable();
            $table->string('LicensExpireDate')->nullable();
            $table->string('PassportExpireDate')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employesses');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsStores extends Model
{
    use HasFactory;
      protected $table = 'products_stores';
      protected $fillable = [
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'P_Code',
        'Exp_Date',
        'Product',
        'Store',
        'V1',
        'V2',
       
    ];
    
           public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
         public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
         public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
         public function ProductsQty()
    {
        return $this->hasOne(ProductsQty::class);
    }
}

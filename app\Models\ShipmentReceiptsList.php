<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentReceiptsList extends Model
{
    use HasFactory;
     protected $table = 'shipment_receipts_lists';
      protected $fillable = [

                'Shipping_List',
                'Code',
                'Sender_Name',
                'Addressees_Name',
                'Total_Qty',
                'Total_Price',
                'Payment_Method',
                'Notes',
                'Ticket',
                'ShipmentReceipts',
                'Status',

    ];
    
    
        public function Shipping_List()
    {
        return $this->belongsTo(ShippingList::class,'Shipping_List');
    }
    
          public function ShipmentReceipts()
    {
        return $this->belongsTo(ShipmentReceipts::class,'ShipmentReceipts');
    }
    
                public function Ticket()
    {
        return $this->belongsTo(Ticket::class,'Ticket');
    }
    
    
        public function Sender_Name()
    {
        return $this->belongsTo(AcccountingManual::class,'Sender_Name');
    }
    
            public function Addressees_Name()
    {
        return $this->belongsTo(AcccountingManual::class,'Addressees_Name');
    }
}

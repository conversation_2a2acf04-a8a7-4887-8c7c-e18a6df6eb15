<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StoresTransferColumnsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('stores_transfer_columns')->delete();
        
        \DB::table('stores_transfer_columns')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Amount' => '1',
                'From_Store' => '1',
                'To_Store' => '1',
                'User' => '1',
                'Coin' => '1',
                'Shipping' => '1',
                'Note' => '1',
                'Delegate' => '1',
                'Product_Name' => '1',
                'Product_Code' => '1',
                'Group' => '1',
                'Brand' => '1',
                'Qty' => '1',
                'Price' => '1',
                'Trans_Qty' => '1',
                'Unit' => '1',
                'Total' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-06 11:34:30',
                'Branch' => '1',
            ),
        ));
        
        
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuoteImage extends Model
{
    use HasFactory;
      protected $table = 'quote_images';
      protected $fillable = [
        'Code',
        'Date',
        'Payment_Method',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Client',
        'User',
    ];

          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
     
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
}

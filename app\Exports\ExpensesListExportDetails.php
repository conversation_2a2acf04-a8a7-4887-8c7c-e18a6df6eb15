<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\AcccountingManual;
use App\Models\PaymentVoucherDetails;
use DB ;
class ExpensesListExportDetails implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->from;

         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $safe=$storex['safe'];
         $account=$storex['account'];
         $subAccount=$storex['subAccount'];
    
        
$items = AcccountingManual::orderBy('Code','asc')
      
           
        ->when(!empty($subAccount), function ($query) use ($subAccount) {
        return $query->whereIn('id', $subAccount);
    })        
          ->get(); 
       
             $result = array();
             $resultDetails = array();
        foreach($items as $item){
            
                                  $subAccount=$item->id;
                                              $lists=PaymentVoucherDetails::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                ->when(!empty($branch), function ($query) use ($branch) {
                                            return $query->where('Branch', $branch);
                                        }) 
                                          ->when(!empty($safe), function ($query) use ($safe) {
                                            return $query->whereIn('Safe', $safe);
                                        }) 
                                                ->get();
                                                
                                $Tot=PaymentVoucherDetails::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                ->when(!empty($branch), function ($query) use ($branch) {
                                            return $query->where('Branch', $branch);
                                        }) 
                                          ->when(!empty($safe), function ($query) use ($safe) {
                                            return $query->whereIn('Safe', $safe);
                                        }) 
                                                ->get()->sum('Debitor');
            
           




            
            foreach($lists as $detail){
                
                
                if(app()->getLocale() == 'ar'){
                   $cc=$detail->Account()->first()->Name;
                    $ran=$detail->Branch()->first()->Arabic_Name;   
                     $Name=$item->Name;
                }else{
                        $cc=$detail->Account()->first()->NameEn;
                    $ran=$detail->Branch()->first()->English_Name;   
                  $Name=$item->NameEn;    
                    
                }

                          if($Tot != 0){
               
   $resultDetails[] = array(
       
       
              'Name'=>$Name,
              'Total' =>$Tot,
              'Date'=>$detail->Date,
              'CodeBill' =>$detail->PV_ID()->first()->Code,
              'Statement' =>$detail->Statement,
              'Debitor' =>$detail->Debitor,
              'Accpunt' =>$cc,
              'Branch' =>$ran,
             
                                              
        
           );
            }  
                
            }
            

            
           
        }
                                                
                     
        return collect($resultDetails);
    }
    

    public function headings(): array
    {
        return [
          'Name',
          'Total',
          'Date',
          'CodeBill',
          'Statement',
          'Debitor',
          'Accpunt',
          'Branch'

        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductExecutingReceivingSecretariat extends Model
{
    use HasFactory;
      protected $table = 'product_executing_receiving_secretariats';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'Dep',
        'Price',
        'Workmanship_Price',
        'Total',
         'StoreIn',
        'StoreOut',
        'Product',
        'Unit',
        'Executing',
        'type',
    
    ];

             public function StoreIn()
    {
        return $this->belongsTo(SecretariatStores::class,'StoreIn');
    }
    
                  public function StoreOut()
    {
        return $this->belongsTo(Stores::class,'StoreOut');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Executing()
    {
        return $this->belongsTo(ExecutingReceivingSecretariat::class,'Executing');
    }
}

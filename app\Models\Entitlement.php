<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Entitlement extends Model
{
    use HasFactory;
           protected $table = 'entitlements';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Amount',
        'Draw',
        'Note',
        'Emp',
        'Coin',
        'Type',
        'User',
    ];

         public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
         public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
         public function Type()
    {
        return $this->belongsTo(BeneftisTypes::class,'Type');
    }
    
         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

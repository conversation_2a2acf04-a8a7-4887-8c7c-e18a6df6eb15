<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stores', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Date');
            $table->string('Time');
            $table->string('Name');
            $table->string('Phone')->nullable();
            $table->string('Address')->nullable();
            $table->integer('Account');
            $table->integer('User');
            $table->timestamps();
            $table->string('Code')->nullable();
            $table->integer('Branch');
            $table->string('Letter')->nullable();
            $table->string('Account_Client')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stores');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeducationsTypes extends Model
{
    use HasFactory;
      protected $table = 'deducations_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
            public function Deduction()
    {
        return $this->hasOne(Deduction::class);
    }
    
}

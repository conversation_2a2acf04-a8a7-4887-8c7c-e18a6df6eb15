<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Teachers extends Model
{
    use HasFactory;
     protected $table = 'teachers';
      protected $fillable = [

                'Code',
                'Arabic_Name',
                'English_Name',
                'Image',
                'Work_Type',
                'Hour_Price',
                'Qualification',
                'Qualification_Attach',
                'National_ID',
                'National_ID_Attach',
                'CV',
                'Nationality',
                'Gov',
                'City',
                'Place',
                'Address',
                'Age',
                'Phone1',
                'Phone2',
                'Whatsapp',
                'Facebook',
                'Instagram',
                'Linked',
                'Telegram',
                'Arabic_Bio',
                'English_Bio',
                'Marital_Status',
                'Account',
                'Covenant',
                'Commission',
                'Merit',
                'Account_Emp',
           
            
 
          
    ];
    
    
        public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }    
        public function Gov()
    {
        return $this->belongsTo(Governrate::class,'Gov');
    }    
        public function City()
    {
        return $this->belongsTo(City::class,'City');
    }    
        public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }   
    

    
    
               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }       
    
    public function Merit()
    {
        return $this->belongsTo(AcccountingManual::class,'Merit');
    }
    
            public function Covenant()
    {
        return $this->belongsTo(AcccountingManual::class,'Covenant');
    }
    
            public function Commission()
    {
        return $this->belongsTo(AcccountingManual::class,'Commission');
    }
    
           public function Account_Emp()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Emp');
    }
}

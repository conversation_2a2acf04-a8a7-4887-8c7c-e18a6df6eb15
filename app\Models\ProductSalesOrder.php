<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSalesOrder extends Model
{
    use HasFactory;
    protected $table = 'product_sales_orders';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'AvQty',
        'Qty',
        'Price',
        'CostPrice',
        'Discount',
        'TDiscount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
        'Product',
        'Exp_Date',
        'V1',
        'V2',
        'Unit',
        'SalesOrder',
        'Patch_Number',
        'Pro_Note',
        'SubVID',
        'Total_Wight',
        'UnitRate',
        'weight',
        'SalesProDesc',
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function SubVID()
    {
        return $this->belongsTo(SubVirables::class,'SubVID');
    }       
    
    public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
            public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
            public function SalesOrder()
    {
        return $this->belongsTo(SalesOrder::class,'SalesOrder');
    }
}

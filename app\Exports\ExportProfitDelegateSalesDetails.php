<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductSales;
class ExportProfitDelegateSalesDetails implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      

        $storex=$this->store;
        
  
        
         $storee=$storex['store'];
         $from=$storex['from'];
         $to=$storex['to'];
         $delegate=$storex['delegate'];
         $branch=$storex['branch'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $payment_Method=$storex['payment_Method'];
   

     
         $items=ProductSales::whereBetween('Date',[$from,$to])
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
                            ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store', $storee);
    }) 
       
                                ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('Payment_Method', $payment_Method);
    }) 
       
        ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('Delegate', $delegate);
    })   

         ->select('Group','Brand','Branch','Delegate','Date','P_Ar_Name','Product_Code','Qty','Price','Total','Store','Client','CostPrice')->get();      
 
              
        

        $result = array();
        foreach($items as $record){
            
             if(!empty($record->Group()->first()->Name)){
                    if(app()->getLocale() == 'ar' ){ 
                   $GROP=$record->Group()->first()->Name;  
                    }else{
                        
                   $GROP=$record->Group()->first()->NameEn;         
                    }
                }else{
                    $GROP='';
                }
                    if(!empty($record->Brand()->first()->Name)){
                        
                           if(app()->getLocale() == 'ar' ){ 
                   $BRAND=$record->Brand()->first()->Name;
                           }else{
                               
                           $BRAND=$record->Brand()->first()->NameEn;          
                           }
                }else{
                    $BRAND='';
                }
                     if(!empty($record->Branch()->first()->Arabic_Name)){
                         
                             if(app()->getLocale() == 'ar' ){ 
                   $BRANCH=$record->Branch()->first()->Arabic_Name; 
                             }else{
                                 
                         $BRANCH=$record->Branch()->first()->English_Name;           
                             }
                }else{
                    $BRANCH='';
                }
                     if(!empty($record->Delegate()->first()->Name)){
                         
                          if(app()->getLocale() == 'ar' ){  
                    $DELE=$record->Delegate()->first()->Name;
                          }else{
                              
                        $DELE=$record->Delegate()->first()->NameEn;         
                          }
                }else{
                    $DELE='';
                }

             if($item->Payment_Method == 'Cash'){
                     $Pay=trans('admin.Cash'); 
      }elseif($item->Payment_Method == 'Later'){
           $Pay=trans('admin.Later'); 
                     
      }elseif($item->Payment_Method == 'Delivery'){
           $Pay=trans('admin.Delivery'); 
                   
      }elseif($item->Payment_Method == 'Cash_Visa'){
           $Pay=trans('admin.Cash_Visa'); 
                         
      }elseif($item->Payment_Method == 'Cash_Collection'){
           $Pay=trans('admin.Cash_Collection'); 
                      
                                                        
      }elseif($item->Payment_Method == 'Check'){
           $Pay=trans('admin.Check'); 
      
      }elseif($item->Payment_Method == 'Installment'){
           $Pay=trans('admin.Installment'); 
          
      }elseif($item->Payment_Method == 'InstallmentCompanies'){
           $Pay=trans('admin.InstallmentCompanies'); 
      }else{
          $Pay='';
      }   

               if(app()->getLocale() == 'ar' ){ 
                  $Proname= $record->P_Ar_Name;
                  $Stname= $record->Store()->first()->Name;
                  $Cliname=$record->Client()->first()->Name; 
               }else{
                  
                          $Proname= $record->P_En_Name;
                  $Stname= $record->Store()->first()->NameEn;
                  $Cliname=$record->Client()->first()->NameEn; 
                   
               }
           $result[] = array(
              'Date'=>$record->Date,
              'Name' => $Proname,
              'Code' => $record->Product_Code,
              'Qty' => $record->Qty,
              'Price' => $record->Price,
              'Total' => $record->Total,
              'Cost' => $record->CostPrice * $record->Qty,
              'Profit' => $record->Total - ($record->CostPrice * $record->Qty),
              'Store' => $Stname,
              'Client' => $Cliname,
              'Delegate' => $DELE,
              'Branch' => $BRANCH,
              'Group' => $GROP,
              'Brand' => $BRAND,
              'Payment_Method' => $Pay
        
           );
        }

        return collect($result);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Name',
          'Code',
          'Qty',
          'Price',
          'Total',
          'Cost',
          'Profit',
          'Store',
          'Client',
          'Delegate',
          'Branch',
          'Group',
            'Brand',
          'Payment_Method'
        ];
    }
    
    
    

}

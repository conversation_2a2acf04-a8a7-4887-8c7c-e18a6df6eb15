<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\AcccountingManual;
use App\Models\GeneralDaily;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\ReciptsType;
use App\Models\BonesType;
use App\Models\CompanyCars;
use App\Models\CountersType;
use App\Models\Stores;
use App\Models\Products;
use App\Models\PurchasePetrol;
use App\Models\ProductsPurchasePetrol;
use App\Models\Coins;
use App\Models\Employess;
use App\Models\Measuerments;
use App\Models\DefaultDataShowHide;
use App\Models\ProductUnits;
use App\Models\PurchasesDefaultData;
use App\Models\ProductsQty;
use App\Models\StoreCount;
use App\Models\ProductMoves;
use App\Models\ReciptsSalesPetrol;
use App\Models\ClientSalesPetrol;
use App\Models\CarsSalesPetrol;
use App\Models\BonesSalesPetrol;
use App\Models\WorkersSalesPetrol;
use App\Models\SalesPetrol;
use App\Models\Customers;
use DB;
use Str;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
 
class PetrolController extends Controller
{

        function __construct()
{

            
            
            
$this->middleware('permission:سيارات الشركه', ['only' => ['CompanyCarsPage']]);
$this->middleware('permission:انواع البونات', ['only' => ['BonesTypePage']]);
$this->middleware('permission:انواع الايصالات', ['only' => ['ReciptsTypePage']]);
$this->middleware('permission:انواع العدادات', ['only' => ['CountersTypePage']]);
$this->middleware('permission:مشتريات الوقود', ['only' => ['PurchasePetrolPage']]);
$this->middleware('permission:مبيعات الوقود', ['only' => ['SalesPetrolPage']]);
$this->middleware('permission:جدول مشتريات الوقود', ['only' => ['PurchasePetrolSechdule']]);
$this->middleware('permission:جدول مبيعات الوقود', ['only' => ['SalesPetrolSechdule']]);



}  
   
    
     //CompanyCars
          public function CompanyCarsPage(){
        $items=CompanyCars::all();
          
         return view('admin.Petrol.CompanyCars',['items'=>$items]);
    }
    
          public function AddCompanyCars(){
   
        $data= $this->validate(request(),[
             'Name'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);
   
  
            $count=AcccountingManual::orderBy('id','desc')->where('Parent',55)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',55)->first();    
            $codee=AcccountingManual::find(55);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $data['Code']=(int) $x ;
                      
                }else{
                    
                                $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $data['Code']=(int) $x;  
       
                }
                
         $data['Name']=request('Name');
             if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }


         $data['Type']=1;
         $data['Parent']=55;
         $data['Note']=null;
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);
          
          
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 
        
          
         $dataa['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
               $dataa['NameEn']=request('Name'); 
              
          }


         $dataa['Number']=request('Number');
         $dataa['Account']=$Acc->id;
          
         CompanyCars::create($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سيارات الشركة';
           $dataUser['ScreenEn']='Company Cars';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
              
                     if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');
              
          }
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
          public function EditCompanyCars($id){
   
        $data= $this->validate(request(),[
             'Name'=>'required',

               ],[
            'Name.required' => trans('admin.NameRequired'),

         ]);
   
  
     $safe=CompanyCars::find($id);
                

         AcccountingManual::where('id',$safe->Account)->update(['Name'=>request('Name'),'NameEn'=>request('NameEn')]);
          
   
         $dataa['Name']=request('Name');
                    $dataa['NameEn']=request('NameEn');  
         $dataa['Number']=request('Number');
         $dataa['Account']=request('Account');

         CompanyCars::where('id',$id)->update($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سيارات الشركة';
           $dataUser['ScreenEn']='Company Cars';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

          public function DeleteCompanyCars($id){
                      
        $del=CompanyCars::find($id);
                  
              
         AcccountingManual::where('id',$del->Account)->delete();

         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='سيارات الشركة';
           $dataUser['ScreenEn']='Company Cars';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delte';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   

         //BonesType
          public function BonesTypePage(){
        $items=BonesType::all();
          
         return view('admin.Petrol.BonesType',['items'=>$items]);
    }
    
          public function AddBonesType(){
   
        $data= $this->validate(request(),[
             'Name'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);
   
  
            $count=AcccountingManual::orderBy('id','desc')->where('Parent',26)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',26)->first();    
            $codee=AcccountingManual::find(26);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $data['Code']=(int) $x ;
                      
                }else{
                    
                               $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $data['Code']=(int) $x;  
       
                }
                
         $data['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }


         $data['Type']=1;
         $data['Parent']=26;
         $data['Note']=null;
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);
          
          
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 
        
          
         $dataa['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
               $dataa['NameEn']=request('Name'); 
              
          }


         $dataa['Account']=$Acc->id;
          
         BonesType::create($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                  $dataUser['Screen']='انواع البونات';
           $dataUser['ScreenEn']='Bones Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                     if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');
              
          }
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
          public function EditBonesType($id){
   
        $data= $this->validate(request(),[
             'Name'=>'required',

               ],[
            'Name.required' => trans('admin.NameRequired'),

         ]);
   
  
     $safe=BonesType::find($id);
                

         AcccountingManual::where('id',$safe->Account)->update(['Name'=>request('Name'),'NameEn'=>request('NameEn')]);
          
   
         $dataa['Name']=request('Name');
                $dataa['NameEn']=request('NameEn');      
         $dataa['Account']=request('Account');

         BonesType::where('id',$id)->update($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                       $dataUser['Screen']='انواع البونات';
           $dataUser['ScreenEn']='Bones Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

          public function DeleteBonesType($id){
                      
        $del=BonesType::find($id);
                  
              
         AcccountingManual::where('id',$del->Account)->delete();

         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='انواع البونات';
           $dataUser['ScreenEn']='Bones Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   

             //ReciptsType
          public function ReciptsTypePage(){
        $items=ReciptsType::all();
          
         return view('admin.Petrol.ReciptsType',['items'=>$items]);
    }
    
          public function AddReciptsType(){
   
        $data= $this->validate(request(),[
             'Name'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);
   
  
            $count=AcccountingManual::orderBy('id','desc')->where('Parent',55)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',55)->first();    
            $codee=AcccountingManual::find(55);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $data['Code']=(int) $x ;
                      
                }else{
                    
                              $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $data['Code']=(int) $x;  
       
                }
                
         $data['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }

         $data['Type']=1;
         $data['Parent']=55;
         $data['Note']=null;
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);
          
          
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 
        
          
         $dataa['Name']=request('Name');
             if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
               $dataa['NameEn']=request('Name'); 
              
          }

         $dataa['Account']=$Acc->id;
          
         ReciptsType::create($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='انواع الايصالات';
           $dataUser['ScreenEn']='Recipts Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                   if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');
              
          }
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
          public function EditReciptsType($id){
   
        $data= $this->validate(request(),[
             'Name'=>'required',

               ],[
            'Name.required' => trans('admin.NameRequired'),

         ]);
   
  
     $safe=ReciptsType::find($id);
                

         AcccountingManual::where('id',$safe->Account)->update(['Name'=>request('Name'),'NameEn'=>request('NameEn')]);
          
   
         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');
         $dataa['Account']=request('Account');

         ReciptsType::where('id',$id)->update($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                        $dataUser['Screen']='انواع الايصالات';
           $dataUser['ScreenEn']='Recipts Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

          public function DeleteReciptsType($id){
                      
        $del=ReciptsType::find($id);
                  
              
         AcccountingManual::where('id',$del->Account)->delete();

         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                       $dataUser['Screen']='انواع الايصالات';
           $dataUser['ScreenEn']='Recipts Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   
    
                 //CountersType
          public function CountersTypePage(){
        $items=CountersType::all();
        $Stores=Stores::all();
        $Products=Products::where('P_Type','Petroll')->get();
          
         return view('admin.Petrol.CountersType',['items'=>$items,'Stores'=>$Stores,'Products'=>$Products]);
    }
    
          public function AddCountersType(){
   
        $data= $this->validate(request(),[
             'Name'=>'required',
             'Current_Read'=>'required',
             'Store'=>'required',
             'Product'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
            'Current_Read.required' => trans('admin.Current_ReadRequired'),
            'Store.required' => trans('admin.StoreRequired'),
            'Product.required' => trans('admin.ProductRequired'),
         ]);
   
          
          
         $dataa['Name']=request('Name');
                            if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
               $dataa['NameEn']=request('Name'); 
              
          }

         $dataa['Current_Read']=request('Current_Read');
         $dataa['Store']=request('Store');
         $dataa['Product']=request('Product');
          
         CountersType::create($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
      
                        $dataUser['Screen']='انواع العدادات';
           $dataUser['ScreenEn']='Counters Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
       
              if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');
              
          }
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
          public function EditCountersType($id){
   
           $data= $this->validate(request(),[
             'Name'=>'required',
             'Current_Read'=>'required',
             'Store'=>'required',
             'Product'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
            'Current_Read.required' => trans('admin.Current_ReadRequired'),
            'Store.required' => trans('admin.StoreRequired'),
            'Product.required' => trans('admin.ProductRequired'),
         ]);
   
          
          
         $dataa['Name']=request('Name');
            $dataa['NameEn']=request('NameEn');          
         $dataa['Current_Read']=request('Current_Read');
         $dataa['Store']=request('Store');
         $dataa['Product']=request('Product');

         CountersType::where('id',$id)->update($dataa);
          

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                              $dataUser['Screen']='انواع العدادات';
           $dataUser['ScreenEn']='Counters Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

          public function DeleteCountersType($id){
                      
        $del=CountersType::find($id);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                               $dataUser['Screen']='انواع العدادات';
           $dataUser['ScreenEn']='Counters Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   
    
    
    
       //PurchasePetrol
            public function PurchasePetrolPage(){

            $Coins=Coins::all();  

     if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
        
                $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->get();
     
          
             $Employess = Employess::where("EmpSort",1)->where('Active',1)->get();
             $CompanyCars=CompanyCars::all();
 

            $res=PurchasePetrol::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }

                    $Units=Measuerments::all();
  
         return view('admin.Petrol.PurchasePetrol',[
             'Code'=>$Code,
             'Coins'=>$Coins,
             'Stores'=>$Stores,
             'Safes'=>$Safes,
             'Vendors'=>$Vendors,
             'Employess'=>$Employess,
             'Units'=>$Units,
             'CompanyCars'=>$CompanyCars,
          
         ]);
    }
  
      function PurchacesPetrolFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             
      $vendor = $request->get('vendor');             
                                         
    if($search != '' and $store != '')
    {
            
        $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
                $Prods =Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")          
          ->get(); 
        
           $data =ProductUnits::      
            where('Barcode', $search)   
          ->get(); 
            
            
 }else{
            
        $Prods =Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")         
          ->get(); 
        
           $data =ProductUnits::      
            where('Barcode', 'ILIKE', "%{$search}%")    
          ->get();         
            
            
  } 
        
  
                       
     }

         $total_row = $Prods->count();
         $total_row1 = $data->count();
         $total_row2 = $total_row + $total_row1;
      if($total_row2 > 0) 
      { 
          $show=DefaultDataShowHide::orderBy('id','desc')->first();
           $Def=PurchasesDefaultData::orderBy('id','desc')->first();

   
          
         foreach($Prods as $rows){  
               $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first(); 
               $plow=ProductUnits::where('Product',$rows->id)->where('Rate',1)->first(); 
     $purchs=ProductsPurchasePetrol::where('Product',$rows->id)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('Total');
     $countPurchs=ProductsPurchasePetrol::where('Product',$rows->id)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');
  
                  $units=ProductUnits::where('Product',$rows->id)->get();
             
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();        
            $x = ProductsQty::where("Unit",$rr->Unit)
                ->where('P_Code',$rr->Barcode)
                ->orWhere('PP_Code',$rr->Barcode)
                ->orWhere('PPP_Code',$rr->Barcode)
                ->orWhere('PPPP_Code',$rr->Barcode)
                ->orWhere('Product',$rows->id)->first(); 
   

     if(!empty($x)){
        
         if(!empty($purchs)){
           
           $Collect=$purchs  ;   
           $CollectCount=$countPurchs  ;   
             
   if($CollectCount != 0){
     $ty= $Collect /  $CollectCount ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '') * $rr->Rate;
       


                   }else{
                       
                       $pr=$Collect * $rr->Rate;       
                   }         
             
         }else{
          $pr=$x->Price;   
             
         }        
            

            
        }else{
         $pr=$rr->Price;    
        }         
             
    
            if($rows->P_Type == 'Petroll'){
        
    if($rows->Status == 0){


        $st=Stores::find($store);
        
         if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 

                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
   
                       
                   }   
        


        
        
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->id.'">
        </td>
   
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
                        
       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rr->Product()->first()->Tax.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'"  >
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>

        <td>   
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Name.'" > 
         <input type="hidden" id="StorePurch'.$rows->id.'"   class="form-control"  value="'.$store.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
            }
             
             
        }  
          
        foreach($data as $row){  
             $Stores=Stores::all();  
                $rr=ProductUnits::where('Product',$row->Product)->where('Def',1)->first();
               $plow=ProductUnits::where('Product',$row->Product)->where('Rate',1)->first(); 
            
      $purchs=ProductsPurchasePetrol::where('Product',$row->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total');
            
      $countPurchs=ProductsPurchasePetrol::where('Product',$row->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');
      
                     
              $units=ProductUnits::where('Product',$row->Product)->get();
              $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();        
            $x = ProductsQty::where("Unit",$rr->Unit)
                ->where('P_Code',$rr->Barcode)
                ->orWhere('PP_Code',$rr->Barcode)
                ->orWhere('PPP_Code',$rr->Barcode)
                ->orWhere('PPPP_Code',$rr->Barcode)
                ->orWhere('Product',$rows->id)->first(); 

 
  if(!empty($x)){
      
        
         if(!empty($purchs)){
             
            $Collect=$purchs; 
            $CollectCount=$countPurchs ;

  if($CollectCount != 0){
     $ty= $Collect /  $CollectCount ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '') * $rr->Rate;
                       
                   }else{
                       
                       $pr=$Collect * $rr->Rate;       
                   }
                       
         }else{
             
          $pr=$x->Price;   
             
         }        
            
            
        }else{
      
         $pr=$rr->Price;    
        }
       
           if($row->P_Type == 'Petroll'){ 
                     
             if($row->Product()->first()->Status == 0){
             
        $st=Stores::find($store);
               
                 

     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 

                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
       
                       
                   }   

                 
                 
                 
        $output .= '
        
       <tr id="Row'.$row->Product.'">
        <td>
        '.$PrrroName.'
 <input type="hidden"  id="P_Ar_Name'.$row->Product.'" value="'.$row->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$row->Product.'" value="'.$row->P_En_Name.'">
        <input type="hidden"  id="Product'.$row->Product.'" value="'.$row->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$row->Product.'" onchange="UnitCodePurchh('.$row->Product.')">
                <option value="">  '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
       if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$row->Product.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$row->Product.'" value="'.$UniiName.'"> 
         <input type="hidden" id="PurchTax'.$row->Product.'" value="'.$rr->Product()->first()->Tax.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$row->Product.'"   class="form-control" onkeyup="PurchTotal('.$row->Product.')" onclick="PurchTotal('.$row->Product.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$row->Product.'" step="any"  class="form-control" onkeyup="PurchTotal('.$row->Product.')" onclick="PurchTotal('.$row->Product.')" value="'.$pr.'"  >
  <input type="hidden" id="Total'.$row->Product.'"   class="form-control" > 
        </td>
  

        
        <td>
           <input type="hidden" id="StorePurchName'.$row->Product.'"   class="form-control"  value="'.$st->Name.'" >
             <input type="hidden" id="StorePurch'.$rows->id.'"   class="form-control"  value="'.$store.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$row->Product.'" onclick="Fun('.$row->Product.')">
          <i class="fal fa-plus"></i>
          </button>
          
          
          
          
          </td>
        </tr>
        
       
            ';
        }

            
           }
            
      }
          
          
      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     public function AddPurchasesPetrol(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Payment_Method'=>'required',
             'Vendor'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      
            'Safe.required' => trans('admin.SafeRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Status.required' => trans('admin.StatusRequired'),      
            'Vendor.required' => trans('admin.VendorRequired'),      

         ]);

         
         if(request('Payment_Method') == 'Cash'){
                      
            if(!empty(request('Pay'))){
                 
                  $paid = request('Pay') ;
           
             }else{
                 
              $paid = request('Total_Price') ;    
             }
             
        
         }elseif(request('Payment_Method') == 'Later'){
           
                  $paid = request('Pay') ;
             
         }

         
           
          $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='PetrolFiles/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $file=$image_url; 
                 
             }else{
                 $file=null;
             }

         
         
           $ID = DB::table('purchase_petrols')->insertGetId(
        array(

            'Code' => request('Code'),
            'Date' => request('Date'),
            'Later_Due' => request('Later_Due'),
            'Vendor_Bill_Date' => request('Vendor_Bill_Date'),
            'Refernce_Number' => request('Refernce_Number'),
            'Draw' => request('Draw'),
            'Number' => request('Number'),
            'Payment_Method' => request('Payment_Method'),
            'Note' => request('Note'),
            'File' => $file,
            'Car_Number' => request('Car_Number'),
            'Delivery_Method' => request('Delivery_Method'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Qty' => request('Total_Qty'),
            'Total_Price' => request('Total_Price'),
            'Pay' => $paid,
            'Full' => request('Full'),
            'Vendor' => request('Vendor'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'CompanyCar' => request('CompanyCar'),
            'Store' => request('Store'),
            'Recipient' => request('Recipient'),
            'User' => auth()->guard('admin')->user()->id,
        )
    );  
        
     
       
          $c= DB::select("SELECT last_value FROM purchase_petrols_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
               
          if(!empty(request('Unit'))){
            
            StoreCount::truncate(); 
              
              $P_Code=request('P_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Qty=request('Qty');
              $Price=request('Price');
              $Total=request('Total');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $Unit=request('Unit');
             

            for($i=0 ; $i < count($Unit) ; $i++){
        
            $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
            $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();   
                
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['SmallCode']=$plow->Barcode;
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['SmallQty']=$Qty[$i] * $pp->Rate;    
                $uu['Price']=$Price[$i];
                $uu['Total']=$Total[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Petrol']=$ID;
       
               ProductsPurchasePetrol::create($uu); 
         
              if(request('Status') == 1){    
                     $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('P_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first(); 

}

}

}

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty +  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
               $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();    
                
                
        $purchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('Total');     
  $countPurchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('SmallQty');
        
     


     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }

                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }
     
                
                
            $prooooo=Products::find($Product[$i]);     
          $move['Date']=request('Date');
          $move['Type']='مشتريات بنزين';
          $move['TypeEn']='Purchases Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=$StorePurch[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);        
                
                
            }else{
                

                   $id_store = DB::table('products_stores')->insertGetId(
            
        array(
            
            'P_Ar_Name' => $P_Ar_Name[$i],
            'P_En_Name' => $P_En_Name[$i],
            'P_Code' =>   $P_Code[$i],
            'Exp_Date' => null,
            'Product' => $Product[$i],
            'Store' =>$StorePurch[$i],
            'V1' => null,
            'V2' => null,        
            'V_Name' => null,        
            'VV_Name' => null,        
     
        )
    );          
                

                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                 $pqty['Exp_Date']=null;
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Qty']=$Qty[$i] * $pp->Rate;
                    $pqty['Price']=$Price[$i];
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$StorePurch[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Product[$i];
                
           $prooooo=Products::find($Product[$i]); 
                     $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                   $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;
                    $pqty['P_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }
                         
                

              ProductsQty::create($pqty);  
                $prooooo=Products::find($Product[$i]); 
                
                 $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();  
                
                
            $purchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('Total');     
        $countPurchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('SmallQty');
      
     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }              
 
                if($ty != 0){
                   $in=($Qty[$i] * $pp->Rate) * $ty ;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * $ty ;  
                }else{
                  
             $in=($Qty[$i] * $pp->Rate) * 1;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * 1;        
                    
                }
 
            $move['Date']=date('Y-m-d');
         $move['Type']='مشتريات بنزين';
                  $move['TypeEn']='Purchases Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$Qty[$i] * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=$Qty[$i] * $pp->Rate;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');   
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=$StorePurch[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);            
                
   
                
            }
           
              }
                
          if(request('Payment_Method') == 'Cash'){
              
             if(request('Status') == 1){
                
                 $Mkhazns=StoreCount::all();
                
                 if(count($Mkhazns) == 0){
                  
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);     
                     
                 }else{
                     
                     
           $m=StoreCount::where('Store',$StorePurch[$i])->first();           
  
                        if(!empty($m)){
                           
                            
                            $newTot=$m->Total + $Total[$i] ;
                            
                             StoreCount::where('id',$m->id)->update(['Total'=>$newTot]); 
                            
                        }else{
                            
                         
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);      
                            
                        }
  
                 }
  
             }

          }elseif(request('Payment_Method') == 'Later'){
              
                       if(request('Status') == 1){
                
                 $Mkhazns=StoreCount::all();
                
                 if(count($Mkhazns) == 0){
                  
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);     
                     
                 }else{
                     
                     
           $m=StoreCount::where('Store',$StorePurch[$i])->first();           
  
                        if(!empty($m)){
                           
                            
                            $newTot=$m->Total + $Total[$i] ;
                            
                             StoreCount::where('id',$m->id)->update(['Total'=>$newTot]); 
                            
                        }else{
                            
                         
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);      
                            
                        }
  
                 }
  
             }
  
          }
                
                

            }  

              
          }

          
        if(request('Payment_Method') == 'Cash'){  
            
     
          if(request('Status') == 1){     
            $NewMkhazns=StoreCount::all();

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' => 'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total_Price'),
            'Total_Creditor' => request('Total_Price'),
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }
    
        }
                
            
        }elseif(request('Payment_Method') == 'Later'){
 
                  if(request('Status') == 1){
               $NewMkhazns=StoreCount::all(); 
            if(count($NewMkhazns) == 1){
                            
        $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' => 'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $paid,
            'Total_Creditor' => $paid,
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=request('Total_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total_Price');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Price');
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
     $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total_Price');
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
        if($paid != 0 or $paid > 0){      
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=abs($paid);
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=abs($paid);
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * abs($paid);
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=abs($paid);
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=abs($paid);
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * abs($paid);
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }
          }

            }else{  

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' =>'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $paid,
            'Total_Creditor' => $paid,
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
          $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
       
          }
                
             if($paid != 0 or $paid > 0){      
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']= $paid;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
            $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']= abs($paid);
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') *  abs($paid);
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']= abs($paid);
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']= abs($paid);
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') *  abs($paid);
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }    
    
            }
                
        }
           
        }
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='شراء  وقود';
           $dataUser['ScreenEn']='Purchase Petrol';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
     if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('PurchPetrolPrint/'.$ID); } 
            
        
    }
    
     public function PurchPetrolPrint($id){
        $item=PurchasePetrol::find($id);

         return view('admin.Petrol.PurchasesPetrolPrint',['item'=>$item]);
    }
    
     public function PurchasePetrolSechdule(){
        $items=PurchasePetrol::paginate(100);

         return view('admin.Petrol.PurchasePetrolSechdule',['items'=>$items]);
    }
    
        public function DeletePurchasePetrol($id){
                      
        $del=PurchasePetrol::find($id);

       GeneralDaily::where('Code_Type',$del->Code)->where('Type','شراء  وقود')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','شراء  وقود')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مشتريات بنزين')->delete();
      
    $Products=ProductsPurchasePetrol::where('Petrol',$del->id)->get();        
      foreach($Products as $prod){
          
        $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();  
               
              $qq= $unit->Rate * $prod->Qty ;  
          
        $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
          
          if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
              
                    if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
                        
                          if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPPP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
              
              
          }    
                        
              
              
          }
              
              
          }
    
        $newqty=$PR->Qty - $qq ; 
        
          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='شراء  وقود';
           $dataUser['ScreenEn']='Purchase Petrol';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   
        
       public function EditPurchPetrolPage(){

            $Coins=Coins::all();  

     if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
        
                $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->get();
     
          
             $Employess = Employess::where("EmpSort",1)->where('Active',1)->get();
             $CompanyCars=CompanyCars::all();
 

            $item=PurchasePetrol::orderBy('id','desc')->first();
            $Prods=ProductsPurchasePetrol::where('Petrol',$item->id)->get();
           
        

                    $Units=Measuerments::all();
  
         return view('admin.Petrol.EditPurchasePetrol',[
             'item'=>$item,
             'Prods'=>$Prods,
             'Coins'=>$Coins,
             'Stores'=>$Stores,
             'Safes'=>$Safes,
             'Vendors'=>$Vendors,
             'Employess'=>$Employess,
             'Units'=>$Units,
             'CompanyCars'=>$CompanyCars,
          
         ]);
    }
    
         public function PostEditPurchasesPetrol(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Payment_Method'=>'required',
             'Vendor'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      
            'Safe.required' => trans('admin.SafeRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Status.required' => trans('admin.StatusRequired'),      
            'Vendor.required' => trans('admin.VendorRequired'),      

         ]);

         
         if(request('Payment_Method') == 'Cash'){
                      
            if(!empty(request('Pay'))){
                 
                  $paid = request('Pay') ;
           
             }else{
                 
              $paid = request('Total_Price') ;    
             }
             
        
         }elseif(request('Payment_Method') == 'Later'){
           
                  $paid = request('Pay') ;
             
         }

         
           
          $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='PetrolFiles/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $file=$image_url; 
                 
             }else{
                 $file=request('Files');
             }

         
         $ID=request('ID');
      
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Later_Due'] = request('Later_Due');
            $data['Vendor_Bill_Date'] = request('Vendor_Bill_Date');
            $data['Refernce_Number'] = request('Refernce_Number');
            $data['Draw'] = request('Draw');
            $data['Number'] = request('Number');
            $data['Payment_Method'] = request('Payment_Method');
            $data['Note'] = request('Note');
            $data['File'] = $file;
            $data['Car_Number'] = request('Car_Number');
            $data['Delivery_Method'] = request('Delivery_Method');
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Qty'] = request('Total_Qty');
            $data['Total_Price'] = request('Total_Price');
            $data['Pay'] = $paid;
            $data['Full'] = request('Full');
            $data['Vendor'] = request('Vendor');
            $data['Coin'] = request('Coin');
            $data['Safe'] = request('Safe');
            $data['CompanyCar'] = request('CompanyCar');
            $data['Store'] = request('Store');
            $data['Recipient'] = request('Recipient');
            $data['User'] = auth()->guard('admin')->user()->id;
  
             PurchasePetrol::where('id',$ID)->update($data);
     
              $CodeT=request('Code'); 
               

              
          if(!empty(request('Unit'))){
                         $del=PurchasePetrol::find($ID);  
                   GeneralDaily::where('Code_Type',$del->Code)->where('Type','شراء  وقود')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','شراء  وقود')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مشتريات بنزين')->delete();
      
    $Products=ProductsPurchasePetrol::where('Petrol',$del->id)->get();        
      foreach($Products as $prod){
          
        $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();  
               
              $qq= $unit->Rate * $prod->Qty ;  
          
        $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
          
          if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
              
                    if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
                        
                          if(empty($PR)){
             
              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPPP_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
              
              
          }    
                        
              
              
          }
              
              
          }
    
        $newqty=$PR->Qty - $qq ; 
        
          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

    ProductsPurchasePetrol::where('Petrol',$del->id)->delete();    
            StoreCount::truncate(); 
              
              $P_Code=request('P_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Qty=request('Qty');
              $Price=request('Price');
              $Total=request('Total');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $Unit=request('Unit');
             

            for($i=0 ; $i < count($Unit) ; $i++){
        
            $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
            $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();   
                
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['SmallCode']=$plow->Barcode;
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['SmallQty']=$Qty[$i] * $pp->Rate;    
                $uu['Price']=$Price[$i];
                $uu['Total']=$Total[$i];
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Petrol']=$ID;
       
               ProductsPurchasePetrol::create($uu); 
         
              if(request('Status') == 1){    
                     $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('P_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first(); 

}

}

}

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty +  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
               $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();    
                
                
        $purchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('Total');     
  $countPurchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('SmallQty');
        
     


     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }

                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }
     
                
                
            $prooooo=Products::find($Product[$i]);     
          $move['Date']=request('Date');
          $move['Type']='مشتريات بنزين';
          $move['TypeEn']='Purchases Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=$StorePurch[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);        
                
                
            }else{
                

                   $id_store = DB::table('products_stores')->insertGetId(
            
        array(
            
            'P_Ar_Name' => $P_Ar_Name[$i],
            'P_En_Name' => $P_En_Name[$i],
            'P_Code' =>   $P_Code[$i],
            'Exp_Date' => null,
            'Product' => $Product[$i],
            'Store' =>$StorePurch[$i],
            'V1' => null,
            'V2' => null,        
            'V_Name' => null,        
            'VV_Name' => null,        
     
        )
    );          
                

                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                 $pqty['Exp_Date']=null;
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Qty']=$Qty[$i] * $pp->Rate;
                    $pqty['Price']=$Price[$i];
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$StorePurch[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Product[$i];
                
           $prooooo=Products::find($Product[$i]); 
                     $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                   $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;
                    $pqty['P_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }
                         
                

              ProductsQty::create($pqty);  
                $prooooo=Products::find($Product[$i]); 
                
                 $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();  
                
                
            $purchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('Total');     
        $countPurchs=ProductsPurchasePetrol::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',$StorePurch[$i])->get()->sum('SmallQty');
      
     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }              
 
                if($ty != 0){
                   $in=($Qty[$i] * $pp->Rate) * $ty ;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * $ty ;  
                }else{
                  
             $in=($Qty[$i] * $pp->Rate) * 1;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * 1;        
                    
                }
 
            $move['Date']=date('Y-m-d');
         $move['Type']='مشتريات بنزين';
                  $move['TypeEn']='Purchases Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$Qty[$i] * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=$Qty[$i] * $pp->Rate;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');   
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=$StorePurch[$i];
          $move['Product']=$Product[$i]; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);            
                
   
                
            }
           
              }
                
          if(request('Payment_Method') == 'Cash'){
              
             if(request('Status') == 1){
                
                 $Mkhazns=StoreCount::all();
                
                 if(count($Mkhazns) == 0){
                  
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);     
                     
                 }else{
                     
                     
           $m=StoreCount::where('Store',$StorePurch[$i])->first();           
  
                        if(!empty($m)){
                           
                            
                            $newTot=$m->Total + $Total[$i] ;
                            
                             StoreCount::where('id',$m->id)->update(['Total'=>$newTot]); 
                            
                        }else{
                            
                         
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);      
                            
                        }
  
                 }
  
             }

          }elseif(request('Payment_Method') == 'Later'){
              
                       if(request('Status') == 1){
                
                 $Mkhazns=StoreCount::all();
                
                 if(count($Mkhazns) == 0){
                  
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);     
                     
                 }else{
                     
                     
           $m=StoreCount::where('Store',$StorePurch[$i])->first();           
  
                        if(!empty($m)){
                           
                            
                            $newTot=$m->Total + $Total[$i] ;
                            
                             StoreCount::where('id',$m->id)->update(['Total'=>$newTot]); 
                            
                        }else{
                            
                         
                    $s['Store']=$StorePurch[$i]; 
                    $s['Total']=$Total[$i]; 
                 StoreCount::create($s);      
                            
                        }
  
                 }
  
             }
  
          }
                
                

            }  

              
          }

          
        if(request('Payment_Method') == 'Cash'){  
            
     
          if(request('Status') == 1){     
            $NewMkhazns=StoreCount::all();

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' => 'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total_Price'),
            'Total_Creditor' => request('Total_Price'),
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }
    
        }
                
            
        }elseif(request('Payment_Method') == 'Later'){
 
                  if(request('Status') == 1){
               $NewMkhazns=StoreCount::all(); 
            if(count($NewMkhazns) == 1){
                            
        $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' => 'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $paid,
            'Total_Creditor' => $paid,
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
      $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=request('Total_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total_Price');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Price');
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
     $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total_Price');
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
        if($paid != 0 or $paid > 0){      
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=abs($paid);
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=abs($paid);
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * abs($paid);
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=abs($paid);
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=abs($paid);
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * abs($paid);
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }
          }

            }else{  

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'شراء  وقود',
            'TypeEn' =>'Purchase Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => $paid,
            'Total_Creditor' => $paid,
            'Note' => request('Note'),
  
        )
    );
           
              
              
          foreach($NewMkhazns as $new){

         $store=Stores::find($new->Store);         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$new->Total;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=$new->Total;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $new->Total;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$new->Total;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
          $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$new->Total;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $new->Total;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
       
          }
                
             if($paid != 0 or $paid > 0){      
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']= $paid;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
            $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']= abs($paid);
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') *  abs($paid);
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']= abs($paid);
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='شراء  وقود';
        $Gen['TypeEn']='Purchase Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']= abs($paid);
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') *  abs($paid);
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
              
          }    
    
            }
                
        }
           
        }
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='شراء  وقود';
           $dataUser['ScreenEn']='Purchase Petrol';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
     if(request('SP') == 0){  return redirect('PurchasePetrolSechdule'); }elseif(request('SP') == 1){  return redirect('PurchPetrolPrint/'.$ID); } 
            
        
    }
    
    
    //SalesPetrolPage
         public function SalesPetrolPage(){

            $Coins=Coins::all();  

     if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          

             $Employess = Employess::where("EmpSort",1)->where('Active',1)->get();
             $Workers = Employess::where('Emp_Type','Worker')->where("EmpSort",1)->where('Active',1)->get();
           
 

            $res=SalesPetrol::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }

         return view('admin.Petrol.SalesPetrol',[
             'Code'=>$Code,
             'Coins'=>$Coins,
             'Stores'=>$Stores,
             'Safes'=>$Safes,
             'Employess'=>$Employess,
             'Workers'=>$Workers,
         ]);
    }
    
    
      public function StoreCounterFilter(Request $request) {
            $id = $request->get('store'); 
          
             if(app()->getLocale() == 'ar' ){ 
       $states = CountersType::where("Store",$id)->pluck("Name","id");
             }else{
                 
           $states = CountersType::where("Store",$id)->pluck("NameEn","id");        
             }
       return response()->json($states);
           
    }
    
    
          function CounterDataFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $Counter = $request->get('Counter');             
          
                                         
    if($Counter != '')
    {

                $row =CountersType::find($Counter); 
                
     }

        
      if(!empty($row)) 
      { 
          $Customers=Customers::all();
          $Bones=BonesType::all();
          $Recipts=ReciptsType::all();
          $Cars=CompanyCars::all();
          $price=ProductUnits::where('Product',$row->Product)->first();
          
                 if(app()->getLocale() == 'ar' ){ 
                      $counter=$row->Name; 
                      $product=$row->Product()->first()->P_Ar_Name; 
                   
                   }else{
                         $counter=$row->NameEn;  
                         $product=$row->Product()->first()->P_En_Name;  
                       
                   }  
          
            $output .= '
            
                   <div class="row text-center">
                                <div class="col-md-3"></div>
                                 <div class="col-md-6">
            <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100">
                                        <tbody class="text-center" >
                                        <tr class="table-primary">
                                            <td colspan="2">'.$counter.'</td>
                                        <input type="hidden" name="Counter_Name" value="'.$counter.'">     
                                        </tr>
                                        <tr class="table-danger">
                                            <td colspan="2">'.$product.'</td>
                 <input type="hidden" name="Petrol_Name" value="'.$product.'">    
                                        </tr>
                                        <tr class="table-info">
                                            <td>'.trans('admin.Pervious_Read').' </td>
                                            <td>
        <input type="number" class="form-control" id="Pervious_Read" disabled   value="'.$row->Current_Read.'" >
        <input type="hidden" id="Pervious_ReadHide" name="Pervious_Read" value="'.$row->Current_Read.'" >   
                                            </td>
                                            
                                        </tr>
                                        <tr class="table-warning">
                                            <td>'.trans('admin.Current_Read').'</td>
                                            <td>
                                            
    <input type="number" step="any" class="form-control" id="Current_Raed" onkeyup="ChangePrice()" onclick="ChangePrice()" ame="Current_Raed">                            
                                            </td>
                                          
                                        </tr>
                                                <tr class="table-warning">
                                            <td>'.trans('admin.Consumption').'</td>
                                            <td>
                 <input type="number" class="form-control" id="Consumption" disabled  value="0" >                        <input type="hidden" id="ConsumptionHide" name="Consumption" value="0">                
                                            </td>
                                          
                                        </tr>
                                        <tr>
                                            <td>'.trans('admin.Price').'</td>
                                            <td>
                       <input type="number" class="form-control" id="Value" disabled  value="'.$price->Price.'" >                        <input type="hidden" id="ValueHide" name="Value" value="'.$price->Price.'">
                                            </td>
                             
                                        </tr>
                                               <tr>
                                            <td>'.trans('admin.Total').'</td>
                                            <td>
                           <input type="number" class="form-control" id="Total" disabled  value="0" >                        <input type="hidden" id="TotalHide" name="Total" value="0">                     
                                            </td>
                                     
                                        </tr>
                                        </tbody>
                                      
                                    </table>
                                     </div>
                                  <div class="col-md-3"></div>         
                                         </div>  
                                    <div class="row text-center">
            <div class="col-md-12"><h2>'.trans('admin.Supply_Sechdule').'</h2></div>
                                        
            <div class="col-md-6 mt-2">'.trans('admin.Cash').'</div>             
            <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Cash" value="0"></div>          
                    <div class="col-md-6 mt-2">'.trans('admin.Calibers').'</div>             
          <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Calibers" value="0"></div> 
           <div class="col-md-12 mt-2">  <h4>'.trans('admin.Bones').'</h4> </div>
          '; 
                        
          
          
          foreach($Bones as $bone){
              
                     if(app()->getLocale() == 'ar' ){ 
                      $xName=$bone->Name; 
                   
                   }else{
                         $xName=$bone->NameEn;  
                       
                   }  
              
           $output .= '      
           <div class="col-md-6 mt-2">
                   '.$xName.'
                        <input type="hidden" name="Bone[]" value="'.$bone->id.'">                
                                        </div>             
        <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Bone_Amount[]" value="0"></div>        ';
              
          }
          
             $output .= '     
     
                 <div class="col-md-12 mt-2">  <h4>'.trans('admin.CompanyCars').'</h4> </div>
    ';
          
                 foreach($Cars as $car){
              
                       if(app()->getLocale() == 'ar' ){ 
                      $xxName=$car->Name; 
                   
                   }else{
                         $xxName=$car->NameEn;  
                       
                   }       
                     
           $output .= '     
           <div class="col-md-6 mt-2">
                   '.$xxName.'
                        <input type="hidden" name="Car[]" value="'.$car->id.'">                
                                        </div>             
        <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Car_Amount[]" value="0"></div>        ';
              
          }
          
                       $output .= '     

            <div class="col-md-12 mt-2">  <h4>'.trans('admin.Clients').'</h4> </div>  
    ';
                        foreach($Customers as $cust){
                            
                                     if(app()->getLocale() == 'ar' ){ 
                      $xxxName=$cust->Name; 
                   
                   }else{
                         $xxxName=$cust->NameEn;  
                       
                   }  
              
           $output .= '      
          
           <div class="col-md-6 mt-2">
                   '.$xxxName.'
                        <input type="hidden" name="Customer[]" value="'.$cust->id.'">                
                                        </div>             
        <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Customer_Amount[]" value="0"></div>        ';
              
          }
              
                               $output .= '     
        
             <div class="col-md-12 mt-2">  <h4>'.trans('admin.Reciptss').'</h4> </div>  
    ';
                             foreach($Recipts as $res){
                                 
                                       if(app()->getLocale() == 'ar' ){ 
                      $xxxxName=$res->Name; 
                   
                   }else{
                         $xxxxName=$res->NameEn;  
                       
                   }                  
                                 
              
           $output .= '      
             
           <div class="col-md-6 mt-2">
                   '.$xxxxName.'
                        <input type="hidden" name="Recipt[]" value="'.$res->id.'">                
                                        </div>             
        <div class="col-md-6 mt-2"><input type="number" step="any" onkeyup="XX()" onclick="XX()" class="form-control IN" name="Recipt_Amount[]" value="0"></div>        ';
              
          }
 
          
 $output .= '
  
                                   </div>          
                                       
            
            
            ';  
          
      }else
      {
       $output = '
        <div class="col-md-3"> '.trans('admin.No_Data_Find').'  </div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
    
          public function ConsumptionFilter(Request $request) {
            $id = $request->get('Counter'); 
            $Store = $request->get('Store'); 
              
             $count=CountersType::find($id);
             
                           $Quantity =ProductsQty::
                where('Store',$Store)    
                ->where('Product',$count->Product)     
                ->first(); 

              
       $states =[];
              
       $states +=['Num'=>$Quantity->Qty];
              
              
       return response()->json($states);
           
    }

      public function AddSalesPetrol(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      
            'Safe.required' => trans('admin.SafeRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Status.required' => trans('admin.StatusRequired'),      
            'Vendor.required' => trans('admin.VendorRequired'),      

         ]);

         
           $ID = DB::table('sales_petrols')->insertGetId(
        array(

            'Code' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Counter_Name' => request('Counter_Name'),
            'Pervious_Read' => request('Pervious_Read'),
            'Petrol_Name' => request('Petrol_Name'),
            'Current_Raed' => request('Current_Raed'),
            'Value' => request('Value'),
            'Total' => request('Total'),
            'Consumption' => request('Consumption'),
            'Cash' => request('Cash'),
            'Calibers' => request('Calibers'),
            'Counter' => request('Counter'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Store' => request('Store'),
            'Recipient' => request('Recipient'),
            'User' => auth()->guard('admin')->user()->id,
        )
    );  
        
     
                   $c= DB::select("SELECT last_value FROM sales_petrols_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
          
          
          $Counter=CountersType::find(request('Counter'));
            $pp=ProductUnits::where('Product',$Counter->Product)->first();    
            $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();   
                 
         
          
                        $Quantity =ProductsQty::
                where('Store',request('Store'))    
                ->where('Product',$Counter->Product)     
                ->first(); 

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Product',$Counter->Product)->first();  
                
           $qq= $unit->Rate *  request('Consumption') ;
                
           $newqty=$Quantity->Qty -  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
               $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();    
                
                
        $purchs=ProductsPurchasePetrol::where('Product',$Counter->Product)->where('SmallCode',$plow->Barcode)->where('Store',request('Store'))->get()->sum('Total');     
  $countPurchs=ProductsPurchasePetrol::where('Product',$Counter->Product)->where('SmallCode',$plow->Barcode)->where('Store',request('Store'))->get()->sum('SmallQty');
        

     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }

                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }

            $prooooo=Products::find($Counter->Product);     
          $move['Date']=date('Y-m-d');
          $move['Type']='مبيعات بنزين';
          $move['TypeEn']='Sales Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$plow->P_Ar_Name;
          $move['P_En_Name']=$plow->P_En_Name;
          $move['P_Code']=$plow->P_Code;
          $move['Unit']=$plow->Unit;
          $move['QTY']=request('Consumption');    
          $move['Group']=$prooooo->Group;
          $move['Store']=request('Store');
          $move['Product']=$plow->Product; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);        
  
            }
           
 
          if(!empty(request('Worker'))){

              $Worker=request('Worker');

            for($i=0 ; $i < count($Worker) ; $i++){

                $uu['Worker']=$Worker[$i];
                $uu['SalesPetrol']=$ID;       
               WorkersSalesPetrol::create($uu); 
            }  

              
          }

            if(!empty(request('Bone'))){

              $Bone=request('Bone');
              $Bone_Amount=request('Bone_Amount');

            for($i=0 ; $i < count($Bone) ; $i++){
                
                if($Bone_Amount[$i] != 0 and $Bone_Amount[$i] != '' and $Bone_Amount[$i] != null){
                $uu['Bone']=$Bone[$i];
                $uu['Bone_Amount']=$Bone_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               BonesSalesPetrol::create($uu); 
                }
            }  

              
          }
          
   if(!empty(request('Car'))){

              $Car=request('Car');
              $Car_Amount=request('Car_Amount');

            for($i=0 ; $i < count($Car) ; $i++){
                
                if($Car_Amount[$i] != 0 and $Car_Amount[$i] != '' and $Car_Amount[$i] != null){
                $uu['Car']=$Car[$i];
                $uu['Car_Amount']=$Car_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               CarsSalesPetrol::create($uu); 
                }
            }  

              
          }
          
             if(!empty(request('Customer'))){

              $Customer=request('Customer');
              $Customer_Amount=request('Customer_Amount');

            for($i=0 ; $i < count($Customer) ; $i++){
                
                if($Customer_Amount[$i] != 0 and $Customer_Amount[$i] != '' and $Customer_Amount[$i] != null){
                $uu['Customer']=$Customer[$i];
                $uu['Customer_Amount']=$Customer_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               ClientSalesPetrol::create($uu); 
                }
            }  



              
          }
          
                    if(!empty(request('Recipt'))){

              $Recipt=request('Recipt');
              $Recipt_Amount=request('Recipt_Amount');



            for($i=0 ; $i < count($Recipt) ; $i++){
                
                if($Recipt_Amount[$i] != 0 and $Recipt_Amount[$i] != '' and $Recipt_Amount[$i] != null){
                $uu['Recipt']=$Recipt[$i];
                $uu['Recipt_Amount']=$Recipt_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               ReciptsSalesPetrol::create($uu); 
                }
            }  
   
          }
          

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'مبيعات وقود',
            'TypeEn' =>'Sales Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total'),
            'Total_Creditor' => request('Total'),
            'Note' => request('Note'),
  
        )
    );

      

         $store=Stores::find(request('Store'));         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total');
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total');
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=52;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
         $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Total');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=52;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
      
          
    
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cash');
        $PRODUCTSS['Account']=34;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
          $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cash');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cash');
        $Gen['Account']=34;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Cash');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
                $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Cash');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Cash');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
              
          $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total');
        $PRODUCTSS['Account']=48;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
              $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total');
        $Gen['Account']=48;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

          $Mo3yara=AcccountingManual::where('Name','حساب معايره')->first();  
         
    
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Cash');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=34;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
         $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Cash');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Cash');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=34;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);          

    $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Calibers');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Mo3yara->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Calibers');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Calibers');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Mo3yara->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);          

            if(!empty(request('Bone'))){

              $Bone=request('Bone');
              $Bone_Amount=request('Bone_Amount');

            
              
            for($i=0 ; $i < count($Bone) ; $i++){
                
                if($Bone_Amount[$i] != 0 and $Bone_Amount[$i] != '' and $Bone_Amount[$i] != null){
         
             $bn=BonesType::find($Bone[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();         
                    
                   $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Bone_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Bone_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Bone_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                    
                }
            }  

              
          }
          
           if(!empty(request('Car'))){

              $Car=request('Car');
              $Car_Amount=request('Car_Amount');

            for($i=0 ; $i < count($Car) ; $i++){
                
                if($Car_Amount[$i] != 0 and $Car_Amount[$i] != '' and $Car_Amount[$i] != null){
        
                    $bn=CompanyCars::find($Car[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();      
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Car_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
             $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Car_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Car_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  

              
          }
          
             if(!empty(request('Customer'))){

              $Customer=request('Customer');
              $Customer_Amount=request('Customer_Amount');

            for($i=0 ; $i < count($Customer) ; $i++){
                
                if($Customer_Amount[$i] != 0 and $Customer_Amount[$i] != '' and $Customer_Amount[$i] != null){
            
                         $bn=Customers::find($Customer[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first(); 
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Customer_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
          $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Customer_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Customer_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  



              
          }
          
            if(!empty(request('Recipt'))){

              $Recipt=request('Recipt');
              $Recipt_Amount=request('Recipt_Amount');


            for($i=0 ; $i < count($Recipt) ; $i++){
                
                if($Recipt_Amount[$i] != 0 and $Recipt_Amount[$i] != '' and $Recipt_Amount[$i] != null){
             
           $bn=ReciptsType::find($Recipt[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();                     
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Recipt_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
         $Gen['Type']='مبيعات وقود';
        $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Recipt_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Recipt_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  
   
          }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='مبيعات وقود';
           $dataUser['ScreenEn']='Sales Petrol';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
     if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('SalesPetrolPrint/'.$ID); } 
            
        
    }
    
       public function SalesPetrolPrint($id){
        $item=SalesPetrol::find($id);

         return view('admin.Petrol.SalesPetrolPetrolPrint',['item'=>$item]);
    }
    
     public function SalesPetrolSechdule(){
        $items=SalesPetrol::paginate(100);

         return view('admin.Petrol.SalesPetrolSechdule',['items'=>$items]);
    }
    
 public function DeleteSalesPetrol($id){
                      
        $del=SalesPetrol::find($id);

       GeneralDaily::where('Code_Type',$del->Code)->where('Type','مبيعات وقود')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','مبيعات وقود')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مبيعات بنزين')->delete();
      
             $Counter=CountersType::find($del->Counter);
            $pp=ProductUnits::where('Product',$Counter->Product)->first();    
            $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();   
                 
         
            $Quantity =ProductsQty::
                where('Store',$del->Store)    
                ->where('Product',$Counter->Product)     
                ->first(); 

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Product',$Counter->Product)->first();  
                
           $qq= $unit->Rate *  request('Consumption') ;
                
           $newqty=$Quantity->Qty +  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
             
            }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='مبيعات وقود';
           $dataUser['ScreenEn']='Sales Petrol';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }  
    
      public function EditSalesPetrol(){

          $id=request('ID');
            $Coins=Coins::all();  

     if(auth()->guard('admin')->user()->emp == 0){
         
         $Stores=Stores::all();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->store)){
                
                   if(auth()->guard('admin')->user()->pos_stores == 0){
                       
                          $Stores=Stores::where('id',auth()->guard('admin')->user()->store)->get();
                   }else{
                       
                     $Stores=Stores::all();    
                   }    
                
                
            }else{
                
                 $Stores=Stores::all();
            }
         
     }   
     
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          

             $Employess = Employess::where("EmpSort",1)->where('Active',1)->get();
             $Workers = Employess::where('Emp_Type','Worker')->where("EmpSort",1)->where('Active',1)->get();

            $item=SalesPetrol::find($id);
           
               $Wors=WorkersSalesPetrol::where('SalesPetrol',$item->id)->get();  
       $Bones=BonesSalesPetrol::where('SalesPetrol',$item->id)->get();                         
                $Cars=CarsSalesPetrol::where('SalesPetrol',$item->id)->get();                         
                $Customers=ClientSalesPetrol::where('SalesPetrol',$item->id)->get();                         
                $Recipts=ReciptsSalesPetrol::where('SalesPetrol',$item->id)->get();  

         return view('admin.Petrol.EdiSalesPetrol',[
             'Coins'=>$Coins,
             'item'=>$item,
             'Stores'=>$Stores,
             'Safes'=>$Safes,
             'Employess'=>$Employess,
             'Workers'=>$Workers,
             'Wors'=>$Wors,
             'Bones'=>$Bones,
             'Cars'=>$Cars,
             'Customers'=>$Customers,
             'Recipts'=>$Recipts,
         ]);
    }

      public function PostEditSalesPetrol(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      
            'Safe.required' => trans('admin.SafeRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      
            'Payment_Method.required' => trans('admin.Payment_MethodRequired'),      
            'Status.required' => trans('admin.StatusRequired'),      
            'Vendor.required' => trans('admin.VendorRequired'),      

         ]);

          $ID=request('ID');
         
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Draw'] = request('Draw');
            $data['Note'] = request('Note');
            $data['Counter_Name'] = request('Counter_Name');
            $data['Pervious_Read'] = request('Pervious_Read');
            $data['Petrol_Name'] = request('Petrol_Name');
            $data['Current_Raed'] = request('Current_Raed');
            $data['Value'] = request('Value');
            $data['Total'] = request('Total');
            $data['Consumption'] = request('Consumption');
            $data['Cash'] = request('Cash');
            $data['Calibers'] = request('Calibers');
            $data['Counter'] = request('Counter');
            $data['Coin'] = request('Coin');
            $data['Safe'] = request('Safe');
            $data['Store'] = request('Store');
            $data['Recipient'] = request('Recipient');
            $data['User'] = auth()->guard('admin')->user()->id;
    
     SalesPetrol::where('id',$ID)->update($data);
          
          
        GeneralDaily::where('Code_Type',$del->Code)->where('Type','مبيعات وقود')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','مبيعات وقود')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مبيعات بنزين')->delete();
      
             $Counter=CountersType::find($del->Counter);
            $pp=ProductUnits::where('Product',$Counter->Product)->first();    
            $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();   
                 
         
            $Quantity =ProductsQty::
                where('Store',$del->Store)    
                ->where('Product',$Counter->Product)     
                ->first(); 

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Product',$Counter->Product)->first();  
                
           $qq= $unit->Rate *  request('Consumption') ;
                
           $newqty=$Quantity->Qty +  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
             
            }
  
          
          
          
              $CodeT=request('Code'); 
          
          
          $Counter=CountersType::find(request('Counter'));
            $pp=ProductUnits::where('Product',$Counter->Product)->first();    
            $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();   
                 
         
          
                        $Quantity =ProductsQty::
                where('Store',request('Store'))    
                ->where('Product',$Counter->Product)     
                ->first(); 

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Product',$Counter->Product)->first();  
                
           $qq= $unit->Rate *  request('Consumption') ;
                
           $newqty=$Quantity->Qty -  $qq ; 
               
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
               $plow=ProductUnits::where('Product',$Counter->Product)->where('Rate',1)->first();    
                
                
        $purchs=ProductsPurchasePetrol::where('Product',$Counter->Product)->where('SmallCode',$plow->Barcode)->where('Store',request('Store'))->get()->sum('Total');     
  $countPurchs=ProductsPurchasePetrol::where('Product',$Counter->Product)->where('SmallCode',$plow->Barcode)->where('Store',request('Store'))->get()->sum('SmallQty');
        

     $Collect=$purchs; 
            $CollectCount=$countPurchs  ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }

                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }

            $prooooo=Products::find($Counter->Product);     
          $move['Date']=date('Y-m-d');
          $move['Type']='مبيعات بنزين';
          $move['TypeEn']='Sales Petrol';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$plow->P_Ar_Name;
          $move['P_En_Name']=$plow->P_En_Name;
          $move['P_Code']=$plow->P_Code;
          $move['Unit']=$plow->Unit;
          $move['QTY']=request('Consumption');    
          $move['Group']=$prooooo->Group;
          $move['Store']=request('Store');
          $move['Product']=$plow->Product; 
          $move['V1']=null;  
          $move['V2']=null;   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);        
  
            }
           
 
          if(!empty(request('Worker'))){

              $Worker=request('Worker');

            for($i=0 ; $i < count($Worker) ; $i++){

                $uu['Worker']=$Worker[$i];
                $uu['SalesPetrol']=$ID;       
               WorkersSalesPetrol::create($uu); 
            }  

              
          }

            if(!empty(request('Bone'))){

              $Bone=request('Bone');
              $Bone_Amount=request('Bone_Amount');

            for($i=0 ; $i < count($Bone) ; $i++){
                
                if($Bone_Amount[$i] != 0 and $Bone_Amount[$i] != '' and $Bone_Amount[$i] != null){
                $uu['Bone']=$Bone[$i];
                $uu['Bone_Amount']=$Bone_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               BonesSalesPetrol::create($uu); 
                }
            }  

              
          }
          
   if(!empty(request('Car'))){

              $Car=request('Car');
              $Car_Amount=request('Car_Amount');

            for($i=0 ; $i < count($Car) ; $i++){
                
                if($Car_Amount[$i] != 0 and $Car_Amount[$i] != '' and $Car_Amount[$i] != null){
                $uu['Car']=$Car[$i];
                $uu['Car_Amount']=$Car_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               CarsSalesPetrol::create($uu); 
                }
            }  

              
          }
          
             if(!empty(request('Customer'))){

              $Customer=request('Customer');
              $Customer_Amount=request('Customer_Amount');

            for($i=0 ; $i < count($Customer) ; $i++){
                
                if($Customer_Amount[$i] != 0 and $Customer_Amount[$i] != '' and $Customer_Amount[$i] != null){
                $uu['Customer']=$Customer[$i];
                $uu['Customer_Amount']=$Customer_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               ClientSalesPetrol::create($uu); 
                }
            }  



              
          }
          
                    if(!empty(request('Recipt'))){

              $Recipt=request('Recipt');
              $Recipt_Amount=request('Recipt_Amount');



            for($i=0 ; $i < count($Recipt) ; $i++){
                
                if($Recipt_Amount[$i] != 0 and $Recipt_Amount[$i] != '' and $Recipt_Amount[$i] != null){
                $uu['Recipt']=$Recipt[$i];
                $uu['Recipt_Amount']=$Recipt_Amount[$i];
                $uu['SalesPetrol']=$ID;       
               ReciptsSalesPetrol::create($uu); 
                }
            }  
   
          }
          

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
                   'Type' => 'مبيعات وقود',             'TypeEn' =>'Sales Petrol',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total'),
            'Total_Creditor' => request('Total'),
            'Note' => request('Note'),
  
        )
    );

      

         $store=Stores::find(request('Store'));         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total');
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total');
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=52;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Total');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=52;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
      
          
    
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Cash');
        $PRODUCTSS['Account']=34;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Cash');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Cash');
        $Gen['Account']=34;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Cash');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Cash');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Cash');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
              
          $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total');
        $PRODUCTSS['Account']=48;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total');
        $Gen['Account']=48;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

          $Mo3yara=AcccountingManual::where('Name','حساب معايره')->first();  
         
    
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Cash');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=34;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Cash');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Cash');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=34;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);          

    $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Calibers');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Mo3yara->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=request('Calibers');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Calibers');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Mo3yara->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);          

            if(!empty(request('Bone'))){

              $Bone=request('Bone');
              $Bone_Amount=request('Bone_Amount');

            
              
            for($i=0 ; $i < count($Bone) ; $i++){
                
                if($Bone_Amount[$i] != 0 and $Bone_Amount[$i] != '' and $Bone_Amount[$i] != null){
         
             $bn=BonesType::find($Bone[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();         
                    
                   $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Bone_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Bone_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Bone_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                    
                }
            }  

              
          }
          
           if(!empty(request('Car'))){

              $Car=request('Car');
              $Car_Amount=request('Car_Amount');

            for($i=0 ; $i < count($Car) ; $i++){
                
                if($Car_Amount[$i] != 0 and $Car_Amount[$i] != '' and $Car_Amount[$i] != null){
        
                    $bn=CompanyCars::find($Car[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();      
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Car_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Car_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Car_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  

              
          }
          
             if(!empty(request('Customer'))){

              $Customer=request('Customer');
              $Customer_Amount=request('Customer_Amount');

            for($i=0 ; $i < count($Customer) ; $i++){
                
                if($Customer_Amount[$i] != 0 and $Customer_Amount[$i] != '' and $Customer_Amount[$i] != null){
            
                         $bn=Customers::find($Customer[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first(); 
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Customer_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Customer_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Customer_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  



              
          }
          
            if(!empty(request('Recipt'))){

              $Recipt=request('Recipt');
              $Recipt_Amount=request('Recipt_Amount');


            for($i=0 ; $i < count($Recipt) ; $i++){
                
                if($Recipt_Amount[$i] != 0 and $Recipt_Amount[$i] != '' and $Recipt_Amount[$i] != null){
             
           $bn=ReciptsType::find($Recipt[$i]);
               $Account=AcccountingManual::where('id',$bn->Account)->first();                     
                    
      $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=$Recipt_Amount[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
           $Gen['Type']='مبيعات وقود';         $Gen['TypeEn']='Sales Petrol';
        $Gen['Debitor']=$Recipt_Amount[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Recipt_Amount[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account->id;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);            
                    
                }
            }  
   
          }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='مبيعات وقود';
           $dataUser['ScreenEn']='Sales Petrol';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
     if(request('SP') == 0){  return redirect('SalesPetrolSechdule'); }elseif(request('SP') == 1){  return redirect('SalesPetrolPrint/'.$ID); } 
            
        
    }
    

}

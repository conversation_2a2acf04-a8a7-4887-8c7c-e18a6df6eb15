<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SecretariatQty extends Model
{
    use HasFactory;
         protected $table = 'secretariat_qties';
      protected $fillable = [
        'Product_Code',
        'PP_Code',
        'PPP_Code',
        'PPPP_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Qty',
        'Product',
        'V1',
        'V2',
        'Store',
        'Unit',
     
    ];

         public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
  
}

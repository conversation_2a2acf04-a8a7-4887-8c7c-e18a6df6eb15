<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OpeningEntries extends Model
{
    use HasFactory;
        protected $table = 'opening_entries';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Coin',
        'Cost_Center',
        'Total_Debaitor',
        'Total_Creditor',
        'SecAccount',
        'Note',
        'Capital',
        'Status',
       
    ];

          public function SecAccount()
    {
        return $this->belongsTo(AcccountingManual::class,'SecAccount');
    }
    
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
            public function OpeningEntriesDetails()
    {
        return $this->hasOne(OpeningEntriesDetails::class);
    }
}

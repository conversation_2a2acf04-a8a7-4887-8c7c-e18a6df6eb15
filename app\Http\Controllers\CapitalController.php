<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Capital;
use App\Models\UsersMoves;
use App\Models\Partners;
use App\Models\GeneralDaily;
use App\Models\AcccountingManual;
use App\Models\SpendProfits;
use App\Models\CostCenter;
use App\Models\Coins;
use App\Models\Journalizing;
use App\Models\Branches;
use App\Models\JournalizingDetails;
use App\Models\ModuleSettingsNum;
use DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

 

class CapitalController extends Controller
{
    
    function __construct()
{
$this->middleware('permission:اضافه راس المال', ['only' =>['CapitalPage','AddCapital']]);   
$this->middleware('permission:الشركاء', ['only' =>['PartnersPage','AddPartner','DeletePartner']]);   
$this->middleware('permission:صرف ارباح', ['only' =>['Spend_ProfitsPage','PartnerFilter','AddSpendProfit','PrintSpendProfits']]);   
$this->middleware('permission:الفروع', ['only' =>['BranchesPage','AddBranches','EditBranches','DeleteBranches']]);   
}   
    
    //Capital
public function CapitalPage(){
        $item=Capital::orderBy('id','desc')->first();
         return view('admin.Capital.Capital',[
             'item'=>$item,                                            
         ]);
    }
    
public function AddCapital(){
   
            
         $data['Authorized_Capital']=request('Authorized_Capital');
         $data['Source_Capital']=request('Source_Capital');
         $data['Shares_Number']=request('Shares_Number');
         $data['Nominal_Value_of_Shares']=request('Nominal_Value_of_Shares');
         $data['Actual_Share_Value']=request('Actual_Share_Value');
         $data['Actual_Capital']=request('Actual_Capital');

         Capital::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Capital');
           $dataUser['Type']=trans('admin.AddNew');
           $dataUser['Explain']=trans('admin.Capital');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
    //Partners
public function PartnersPage(){
        $items=Partners::all();
         
           $TotalErydatD=0;  
     $TotalErydatC=0;  
     $TotalTakloftD=0;  
     $TotalTakloftC=0;  
     $TotalMsrofatD=0;  
     $TotalMsrofatC=0;  
     $TotalTakloftDTakolfaaaaaaaat=0;  
     $TotalTakloftCTakolfaaaaaaaat=0;  
          $TotalErydatDErydaaaat=0;  
     $TotalErydatCErydaaaat=0;  
           $TotalMasrofaaaaaatD=0;  
     $TotalMasrofaaaaatC=0;          
           $to=date('Y-m-d');    
        
         
         
         
         
       $AErydaaat=AcccountingManual::where('id',18)->first();
        
        if($AErydaaat->Parent == 0){
        
          $words=$AErydaaat->Code.'0';  
            
        }else{
            
           $AErydaaat=$AEryd->Code; 
            
        }
        
$ERydaat = AcccountingManual::where('Code', 'like', $words.'%')
    ->where('Type', 1)
    ->get();
   
        
    $ATaklfaaat=AcccountingManual::where('id',19)->first();
        
        if($ATaklfaaat->Parent == 0){
        
          $wordss=$ATaklfaaat->Code.'0';  
            
        }else{
            
           $wordss=$ATaklfaaat->Code; 
            
        }
        
$Taklfaat = AcccountingManual::where('Code', 'like', $wordss.'%')
    ->where('Type', 1)
    ->get();
            
        
        
        $AMasroffffffffat=AcccountingManual::where('id',20)->first();
        
        if($AMasroffffffffat->Parent == 0){
        
          $wordss=$AMasroffffffffat->Code.'0';  
            
        }else{
            
           $wordss=$AMasroffffffffat->Code; 
            
        }
        
$Masrofaat = AcccountingManual::where('Code', 'like', $wordss.'%')
    ->where('Type', 1)
    ->get();
            
          
         
    foreach($ERydaat as $rowM)
       {
           
       
               $TotalDeb =GeneralDaily::where('Account',$rowM->id)      
          ->where('Date','<=' , $to) 
          ->get()->sum('Debitor_Coin'); 
           
           
               $TotalCred =GeneralDaily::where('Account',$rowM->id)       
           ->where('Date','<=' , $to) 
          ->get()->sum('Creditor_Coin');
              
           
           $zM=$TotalDeb - $TotalCred ;
           
           if($zM == 0){
               
                    $xM=0;
                   $yM=0;
    $TotalErydatDErydaaaat += 0;
$TotalErydatCErydaaaat += 0;
           }elseif($zM < 0){
               
                $xM=0;
                   $yM=abs($zM); 
$TotalErydatDErydaaaat += 0;
$TotalErydatCErydaaaat += abs($zM);
               
           }elseif($zM > 0){
               
                $xM=$zM;
                   $yM=0; 
    $TotalErydatDErydaaaat += $zM;
$TotalErydatCErydaaaat += 0;
               
           }
     
       }
            
                    

       $d=  $TotalErydatCErydaaaat -  $TotalErydatDErydaaaat ;         
            
    
            
    foreach($Taklfaat as $rowM)
       {
           
       
               $TotalDeb =GeneralDaily::where('Account',$rowM->id)      
          ->where('Date','<=' , $to) 
          ->get()->sum('Debitor_Coin'); 
           
           
               $TotalCred =GeneralDaily::where('Account',$rowM->id)       
           ->where('Date','<=' , $to) 
          ->get()->sum('Creditor_Coin');
              
           
           $zM=$TotalDeb - $TotalCred ;
           
           if($zM == 0){
               
                    $xM=0;
                   $yM=0;
    $TotalTakloftDTakolfaaaaaaaat += 0;
$TotalTakloftCTakolfaaaaaaaat += 0;
           }elseif($zM < 0){
               
                $xM=0;
                   $yM=abs($zM); 
$TotalTakloftDTakolfaaaaaaaat += 0;
$TotalTakloftCTakolfaaaaaaaat += abs($zM);
               
           }elseif($zM > 0){
               
                $xM=$zM;
                   $yM=0; 
    $TotalTakloftDTakolfaaaaaaaat += $zM;
$TotalTakloftCTakolfaaaaaaaat += 0;
               
           }
     
       }
          
        
  $dd=  $TotalTakloftDTakolfaaaaaaaat -  $TotalTakloftCTakolfaaaaaaaat ;      
       
      
        $Dif = $d   -  $dd   ;   

            
                
    foreach($Masrofaat as $rowM)
       {
           
       
               $TotalDeb =GeneralDaily::where('Account',$rowM->id)      
          ->where('Date','<=' , $to) 
          ->get()->sum('Debitor_Coin'); 
           
           
               $TotalCred =GeneralDaily::where('Account',$rowM->id)       
           ->where('Date','<=' , $to) 
          ->get()->sum('Creditor_Coin');
              
           
           $zM=$TotalDeb - $TotalCred ;
           
           if($zM == 0){
               
                    $xM=0;
                   $yM=0;
    $TotalMasrofaaaaaatD += 0;
$TotalMasrofaaaaatC += 0;
           }elseif($zM < 0){
               
                $xM=0;
                   $yM=abs($zM); 
$TotalMasrofaaaaaatD += 0;
$TotalMasrofaaaaatC += abs($zM);
               
           }elseif($zM > 0){
               
                $xM=$zM;
                   $yM=0; 
    $TotalMasrofaaaaaatD += $zM;
$TotalMasrofaaaaatC += 0;
               
           }
     
       }
                  


       $ddd=  $TotalMasrofaaaaaatD -  $TotalMasrofaaaaatC ;      
$fg= $dd + $ddd;
        if($d >  $fg){
         if($fg  <  0 ){
            
            $DifProf =$d + ($dd  +   $ddd)  ;      
        }else{
            
              $DifProf = $d -  ($dd  +   $ddd)  ;         
            
        }        
        }elseif($d <  $fg){
            
        if($fg  <  0 ){
            
            $DifProf =$d + ($dd  +   $ddd)  ;      
        }else{
            
              $DifProf = $d -  ($dd  +   $ddd)  ;         
            
        }    
            
            
        }elseif($d ==  $fg){
            
           $DifProf = $d -  ($dd  +   $ddd)  ;    
            
        }            

         
            $AMsrfoat=AcccountingManual::where('id',17)->first();
        
        if($AMsrfoat->Parent == 0){
        
          $wordsss=$AMsrfoat->Code.'0';  
            
        }else{
            
           $wordsss=$AMsrfoat->Code; 
            
        }
        
$AccountsMasrofat = AcccountingManual::where('Code', 'like', $wordsss.'%')
    ->where('Type', 1)
    ->get();
         
         
         
         
      foreach($AccountsMasrofat as $rowM)
       {
           
       
               $TotalDeb =GeneralDaily::where('Account',$rowM->id)      
          ->where('Date','<=' , $to) 
          ->get()->sum('Debitor_Coin'); 
           
           
               $TotalCred =GeneralDaily::where('Account',$rowM->id)       
           ->where('Date','<=' , $to) 
          ->get()->sum('Creditor_Coin');
              
           
           $zM=$TotalDeb - $TotalCred ;
           
           if($zM == 0){
               
                    $xM=0;
                   $yM=0;
    $TotalMsrofatD += 0;
$TotalMsrofatC += 0;
           }elseif($zM < 0){
               
                $xM=0;
                   $yM=abs($zM); 
$TotalMsrofatD += 0;
$TotalMsrofatC += abs($zM);
               
           }elseif($zM > 0){
               
                $xM=$zM;
                   $yM=0; 
    $TotalMsrofatD += $zM;
$TotalMsrofatC += 0;
               
           }
           

     
       }
            
     
           $difff=($TotalMsrofatC - $TotalMsrofatD) + $DifProf   ;     
            
         foreach($items as $item){
             
            $shares=$item->Shares_Number; 
             
           $spend=SpendProfits::where('Partner',$item->id)->get()->sum('Amount');
             
             $profPre= $difff / $shares ;
             $safy= $DifProf / $shares ;
             
             $up['Profits_Precentage']=$profPre;
             $up['Profits']=$safy;
             $up['Withdraw_Profits']=$spend;
             $up['Remaining_Profits']=$safy - $spend;

       Partners::where('id',$item->id)->update($up);


             
         }


         
         return view('admin.Capital.Partners',[
             'items'=>$items,                                            
             'DifProf'=>$DifProf,                                            
             'difff'=>$difff,                                            
         ]);
    }
    
public function AddPartner(){
 
      
               $count=AcccountingManual::orderBy('id','desc')->where('Parent',46)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',46)->first();    
            $codee=AcccountingManual::find(46);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                        $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Name');
         $dataX['Type']=1;
         $dataX['Parent']=46;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

            
         $data['Name']=request('Name');
         $data['Shares_Number']=request('Shares_Number');
         $data['Nominal_Value_of_Shares']=request('Nominal_Value_of_Shares');
         $data['Actual_Share_Value']=request('Actual_Share_Value');
         $data['Profits_Precentage']=request('Profits_Precentage');
         $data['Profits']=request('Profits');
         $data['Withdraw_Profits']=request('Withdraw_Profits');
         $data['Remaining_Profits']=request('Remaining_Profits');
         $data['Account']=$Acc->id;

         Partners::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Partners');
           $dataUser['Type']=trans('admin.AddNew');
           $dataUser['Explain']=request('Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
public function DeletePartner($id){
                      
        $del=Partners::find($id);
       AcccountingManual::where('id',$del->Account)->delete(); 
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Partners');
           $dataUser['Type']=trans('admin.Delete');
            $dataUser['Explain']=$del->Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
//Spend Profits
public function Spend_ProfitsPage(){
        $items=SpendProfits::paginate(100);
         
             $CostCenters=CostCenter::all();
          
            $Coins=Coins::all();  
         
         $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
         
             $Partners=Partners::all();
         
         
               $res=SpendProfits::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         
         return view('admin.Capital.SpendProfits',[
             'items'=>$items,                                            
             'Code'=>$Code,                                            
             'CostCenters'=>$CostCenters,                                            
             'Coins'=>$Coins,                                            
             'Safes'=>$Safes,                                            
             'Partners'=>$Partners,                                            
         ]);
    }
    
public function PartnerFilter($id){
      
          $spend=SpendProfits::where('Partner',$id)->get()->sum('Amount');
     $states=[];  
      
       $part=Partners::find($id);
      
      
  $states += ["spending" => $part->Profits - $spend];   
     
      
      
       return response()->json($states);
           
    }
    
public function AddSpendProfit(){
 
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Draw']=request('Draw');
         $data['Amount']=request('Amount');
         $data['Remaining_Profit']=request('Remaining_Profit');
         $data['Partner']=request('Partner');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['Cost_Center']=request('Cost_Center');
         $data['User']=auth()->guard('admin')->user()->id;
         SpendProfits::create($data);

            $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
        array(
            'Code' => $Code,
            'Type' => trans('admin.Spend_Profits'),
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' =>null,
        )
    );

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
        $Gen['Type']=trans('admin.Spend_Profits');
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;
         GeneralDaily::create($Gen);      

    $Partner=Partners::find(request('Partner'));
    
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Partner->Account;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
        $Gen['Type']=trans('admin.Spend_Profits');
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Partner->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;
         GeneralDaily::create($Gen);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Spend_Profits');
           $dataUser['Type']=trans('admin.AddNew');
           $dataUser['Explain']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }  
           
public function PrintSpendProfits($id){
        $item=SpendProfits::find($id);
         return view('admin.Capital.SpendProfitsPrint',[
             'item'=>$item,                                                                                  
         ]);
    }    
 
       //======  Branches ======= 
        public function BranchesPage(){
        $items=Branches::all();
         return view('admin.Capital.Branches',['items'=>$items]);
    }
    
     public function AddBranches(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
         $Module=ModuleSettingsNum::orderBy('id','desc')->first();
         
         if($Module->Branch_Select == 1){
          
             $count=Branches::count();
             
             if($Module->Branch_Num <= $count){
              
             session()->flash('error',trans('admin.Alert_Maximum_Add'));
             return back();
             }
             
             
         }
         
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Letter']=request('Letter');
         $data['Location']=request('Location');
         $data['Code']=request('Code');
         $data['Budget']=request('Budget');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Branches::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الفروع';
           $dataUser['ScreenEn']='Branches';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditBranches($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
                    $data['Location']=request('Location');
                    $data['Letter']=request('Letter');
                    $data['Code']=request('Code');
                    $data['Budget']=request('Budget');
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Branches::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='الفروع';
           $dataUser['ScreenEn']='Branches';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteBranches($id){
                      
        $del=Branches::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='الفروع';
           $dataUser['ScreenEn']='Branches';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    
    



    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QualityDetails extends Model
{
    use HasFactory;
      protected $table = 'quality_details';
      protected $fillable = [
       'Exmine_Type',
       'Exmine_Unit',
       'Exmine_Allow_From',  
       'Exmine_Allow_To',
       'Result',
       'Accept',
       'Exmine',
       'Quality',
     
    ];
    
    
          public function Exmine()
    {
        return $this->belongsTo(ExaminationsTypes::class,'Exmine');
    }
    
              public function Exmine_Unit()
    {
        return $this->belongsTo(Measuerments::class,'Exmine_Unit');
    }
    
         public function Quality()
    {
        return $this->belongsTo(Quality::class,'Quality');
    }

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantIndexStyleII extends Model
{
    use HasFactory;
          protected $table = 'resturant_index_style_i_i_s';
      protected $fillable = [
        'About_Body_BG_Type',
        'About_Body_BG_Image',
        'About_Body_BG_Color',
        'About_Txt_Color',
        'Gallery_Border_Color',
          
        'Reviews_Image_1',
        'Reviews_Image_2',
        'Reviews_Nxt_Prev_Color',
        'Reviews_Nxt_Prev_BG',
        'Reviews_Nxt_Prev_Border',
        'Reviews_Txt_Color',
        'Reviews_Body_BG_Type',
        'Reviews_Body_BG_Image',
        'Reviews_Body_BG_Color',
          
        'Reservations_Body_BG_Type',
        'Reservations_Body_BG_Image',
        'Reservations_Body_BG_Color',
        'Reservations_Box_BG',
        'Reservations_Input_BG',
        'Reservations_Input_Color',
        'Reservations_Btn_Color',
        'Reservations_Btn_BG',
          
        'Blogs_Home_Body_BG_Type',
        'Blogs_Home_Body_BG_Image', 
        'Blogs_Home_Body_BG_Color', 
        'Blogs_Home_Box_BG_Color', 
        'Blogs_Home_Box_Border_Color', 
        'Blogs_Home_Txt_Color', 
        'Blogs_Home_Txt_Hover_Color', 
        'Blogs_Home_Btn_BG', 
        'Blogs_Home_Btn_Color', 
     
       
    ];
}

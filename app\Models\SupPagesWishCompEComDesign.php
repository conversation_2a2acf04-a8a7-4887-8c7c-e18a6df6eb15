<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupPagesWishCompEComDesign extends Model
{
    use HasFactory;
    protected $table = 'sup_pages_wish_comp_e_com_designs';
      protected $fillable = [
        'Wish_Title_BG_Color',
        'Wish_Title_Txt_Color',
        'Wish_Box_BG_Color',
        'Wish_Box_Border_Color',
        'Wish_Box_Border_Type',
        'Wish_Box_Txt_Color',
        'Wish_Box_Button_BG_Color',
        'Wish_Box_Button_Txt_Color',
        'Wish_Box_Button_BG_Hover_Color',
        'Wish_Box_Button_Txt_Hover_Color',  
          
          'Compare_Box_BG_Color',
        'Compare_Box_Txt_Color',
        'Compare_Box_Price_Hover_Color',
        'Compare_Box_Delete_Txt_Color',
      

    ];
}

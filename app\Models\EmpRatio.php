<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpRatio extends Model
{
    use HasFactory;
            protected $table = 'emp_ratios';
      protected $fillable = [
        'From',
        'To',
        'Salary',
        'Rate',
        'Type',
        'Typee',
        'Emp',
    
    ];
    
    
       public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

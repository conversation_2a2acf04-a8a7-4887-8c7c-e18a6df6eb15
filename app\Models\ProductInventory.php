<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductInventory extends Model
{
    use HasFactory;
             protected $table = 'product_inventories';
      protected $fillable = [
        'Av_Qty',
        'P_Code',
        'Inventory',
        'Deficit',
        'Excess',
        'Inv_ID',
        'Product',
        'Price',
        'TotalDificitP',
        'TotalExcessP',
        'V1',
        'V2',
        'Unit',
   
    ];
    
           public function Inv_ID()
    {
        return $this->belongsTo(Inventory::class,'Inv_ID');
    }
    
               public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
               public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
               public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
               public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttendanceEmp extends Model
{
    use HasFactory;
             protected $table = 'attendance_emps';
      protected $fillable = [
        'In_Time',
        'Date',
        'Month',
        'Note',
        'Attend',
        'Emp',

       
    ];
    
        public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
          public function Attend()
    {
        return $this->belongsTo(Attendance::class,'Attend');
    }
    
}

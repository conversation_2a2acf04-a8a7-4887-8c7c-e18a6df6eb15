<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\AcccountingManual;
use App\Models\GeneralDaily;
use DB ;
class IncomListExport implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->from;

         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $safe=$storex['safe'];
         $account=$storex['account'];
         $subAccount=$storex['subAccount'];
    
        
$items = AcccountingManual::orderBy('Code','asc')
      
           
        ->when(!empty($subAccount), function ($query) use ($subAccount) {
        return $query->whereIn('id', $subAccount);
    })        
          ->get(); 
       
             $result = array();
             $resultDetails = array();
        foreach($items as $item){
            
                                  $subAccount=$item->id;

            
                                                
                            $TotD=GeneralDaily::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                                
                       ->whereIn('Type',['سند قبض','دفع شيك وارد','القيود اليومية','تحويلات الخزائن'])
                                                ->get()->sum('Debitor_Coin');
                                                
                                              $TotC=GeneralDaily::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                                
                       ->whereIn('Type',['سند قبض','دفع شيك وارد','القيود اليومية','تحويلات الخزائن'])
                                                ->get()->sum('Creditor_Coin');             
                                                
            
           
            $Tot=$TotD - $TotC ;
            
            
            
                            if(app()->getLocale() == 'ar'){
                   $Name=$item->Name;
                  
                }else{
                        $Name=$item->NameEn;
                  
                    
                }

            
            if($Tot != 0){
               
   $result[] = array(
              'Name'=>$Name,
              'Total' =>$Tot,

           );
            }
        }
                                                
                     
        return collect($result);
    }
    

    public function headings(): array
    {
        return [
          'Name',
          'Total'

        
        ];
    }
    
    
    

}

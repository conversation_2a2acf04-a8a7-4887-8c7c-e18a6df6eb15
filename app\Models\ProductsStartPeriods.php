<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsStartPeriods extends Model
{
    use HasFactory;
        protected $table = 'products_start_periods';
      protected $fillable = [
        'P_Code',
        'Qty',
        'SmallQty',
        'SmallCode',  
        'Price',
        'Total',
        'Date',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'User',
        'Old_Qty',
        'Exp_Date',
        'SP_ID',
        'Unit',
        'Product',
        'Store',
        'V1',
        'V2',
        'Patch_Number',
       
    ];
    
    
    
        public function SP_ID()
    {
        return $this->belongsTo(StartPeriods::class,'SP_ID');
    }
    
         public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
         public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
         public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
         public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
           public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
            public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
}

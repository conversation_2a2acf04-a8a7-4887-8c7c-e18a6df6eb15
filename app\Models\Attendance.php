<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Attendance extends Model
{
    use HasFactory;
         protected $table = 'attendances';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Note',
        'User',
        'Status',

       
    ];
    
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
                       public function AttendanceEmp()
    {
        return $this->hasOne(AttendanceEmp::class);
    }
    
                       public function Departure()
    {
        return $this->hasOne(Departure::class);
    }
 
    
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingDefaultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_defaults', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Delegate');
            $table->string('Vendor');
            $table->string('Client');
            $table->string('Type');
            $table->string('Status');
            $table->string('Breakable');
            $table->string('Coin');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_defaults');
    }
}
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\Admin;
use App\Models\Sales;
use App\Models\Purchases;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class OwnerController extends Controller
{
    

function __construct()
{

$this->middleware('permission:تقارير المالك', ['only' => ['UserLogPage','EmpLocationsPage']]);
$this->middleware('permission:طلباتي', ['only' => ['OrdersPage']]);
$this->middleware('permission:حركه المستخدمين', ['only' => ['UserLogPage']]);
$this->middleware('permission:مواقع الموظفين', ['only' => ['EmpLocationsPage']]);

}
    
    
     public function UserLogPage(){
         
        $items=UsersMoves::orderBy('id','desc')->paginate(50);

        $Screens=UsersMoves::select('Screen','ScreenEn')->distinct(['Screen'])->get();
        $Types=UsersMoves::select('Type','TypeEn')->distinct(['Type'])->get();
    
        $Users=Admin::where('hidden',0)->get();
         return view('admin.Owner.UserLog',[
             'items'=>$items,                                            
             'Users'=>$Users,                                            
             'Screens'=>$Screens,                                            
             'Types'=>$Types,                                            
         
         ]);
    }
    
         function UserLogFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $from = $request->get('from');             
      $to = $request->get('to');             
      $sort = $request->get('sort');             
      $user = $request->get('user');             
      $Screen = $request->get('Screen');             
                               
            if($user != ''){
            
        $u= 'User' ; 
        $uR=  $user;        
         $uName= "where" ;    
             
         }else{

         $u= 'id' ; 
        $uR=  'asc' ;      
          $uName=   "orderBy" ;
         }
        
                  if($Screen != ''){
            
        $SS= 'Screen' ; 
        $SSR=  $Screen;        
         $SSName= "where" ;    
             
         }else{

         $SS= 'id' ; 
        $SSR=  'asc' ;      
          $SSName=   "orderBy" ;
         }
         
          
            if($sort != ''){
            
        $t= 'Type' ; 
        $tR=  $sort;        
         $tName= "where" ;    
             
         }else{

         $t= 'id' ; 
        $tR=  'asc' ;      
          $tName=   "orderBy" ;
         }
        
          if($from != '' and $to != ''){
            
        $d= 'Date' ; 
        $dR=  [$from, $to];        
         $dName= "whereBetween" ;    
            
          }elseif($from != '' and $to == ''){
              
             $d= 'Date' ; 
        $dR=  [$from, date('Y-m-d')];        
         $dName= "whereBetween" ;     
              
              
          }elseif($from == '' and $to != ''){
              
              $d= 'Date' ; 
        $dR=  [ '1-1-2020' , $to];        
         $dName= "whereBetween" ;     
              
              
         }elseif($from == '' and $to == ''){

         $d= 'id' ; 
        $dR=  'asc' ;      
          $dName=   "orderBy" ;
         }
         
       $Prods=UsersMoves::orderBY('id','asc')
             ->$uName($u,$uR)
             ->$tName($t,$tR) 
             ->$dName($d,$dR) 
             ->$SSName($SS,$SSR) 
             ->get();

         
         $total_row = $Prods->count();
      if($total_row > 0) 
      { 
          
          
         foreach($Prods as $rows){  
             
                if(app()->getLocale() == 'ar' ){ 
                
                    $userr=$rows->User()->first()->name;
                    $screeen=$rows->Screen;
                    $typeee=$rows->Type;
                    $explaain=$rows->Explain;
                }else{
                   
                                  $userr=$rows->User()->first()->nameEn;
                    $screeen=$rows->ScreenEn;
                    $typeee=$rows->TypeEn;
                    $explaain=$rows->ExplainEn;
                    
                }

        $output .='     
        <tr>
                                                <td>'.$userr.'</td>
                                                <td>'.$rows->Date.'</td>
                                                <td>'.$rows->Time.'</td>
                                                <td>'.$screeen.'</td>
                                                <td>'.$typeee.'</td>
                                                <td>'.$explaain.'</td>

        </tr>
        
            ';
        }
 
          

      }
      else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     public function EmpLocationsPage(){
  
        $Users=Admin::where('hidden',0)->where('emp','!=',0)->get();
         return view('admin.Owner.EmpLocations',[                                          
             'Users'=>$Users,                                            
         ]);
    }
    
    //Orders
     public function OrdersPage(){
         
      $items=Sales::orderBy('id','desc')
          ->where('Status',1)
          ->where('Ship',auth()->guard('admin')->user()->ship()->first()->Account)
          ->get();
         
          $itemss=Purchases::orderBy('id','desc')
          ->where('Status',1)
          ->where('Ship',auth()->guard('admin')->user()->ship()->first()->Account)
          ->get();
         

         return view('admin.Owner.Orders',[
             'items'=>$items,                                            
             'itemss'=>$itemss,                                            
                                      
         
         ]);
    }
    
      public function PendingSales($id){
          
          Sales::where('id',$id)->update(['ShipStatus'=>0]);
          return back();
          
      }
    
     public function RecivedShipCompSales($id){
          
          Sales::where('id',$id)->update(['ShipStatus'=>1]);
          return back();
          
      }
    
     public function RecivedClientSales($id){
          
          Sales::where('id',$id)->update(['ShipStatus'=>2]);
          return back();
          
      }
    
      public function PendingPurch($id){
          
          Purchases::where('id',$id)->update(['ShipStatus'=>0]);
          return back();
          
      }
    
     public function RecivedShipCompPurch($id){
          
          Purchases::where('id',$id)->update(['ShipStatus'=>1]);
          return back();
          
      }
    
     public function RecivedClientPurch($id){
          
          Purchases::where('id',$id)->update(['ShipStatus'=>2]);
          return back();
          
      }
   
}

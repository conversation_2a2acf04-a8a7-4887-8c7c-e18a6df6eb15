<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupPagesEComDesign extends Model
{
    use HasFactory;
     protected $table = 'sup_pages_e_com_designs';
      protected $fillable = [
        'About_Title_Color',
        'About_Txt_Color',
          
        'Blogs_Title_Color',
        'Blogs_Txt_Color',
        'Blogs_Hover_Txt_Color',
          
        'Contact_Title_Color',
        'Contact_Txt_Color',
        'Contact_Form_Input_Border_Color',
        'Contact_Form_Input_Txt_Color',
        'Contact_Form_Button_BG_Color',  
          'Contact_Form_Button_Txt_Color',
        'Contact_Form_Button_Hover_BG_Color',
        'Contact_Form_Button_Hover_Txt_Color',
          
        'Faq_Title_Color',
        'Faq_Q_BG_Color',
        'Faq_Q_Txt_Color',
        'Faq_A_Line_Color',
        'Faq_A_BG_Color',
        'Faq_A_Txt_Color',    
          
          'MyAccount_Box_BG_Color',
        'MyAccount_Box_Button_BG_Color',
        'MyAccount_Box_Button_Txt_Color',
        'MyAccount_Box_Button_Hover_BG_Color',
        'MyAccount_Box_Button_Hover_Txt_Color',
        'MyAccount_Box_Input_Border_Color',
        'MyAccount_Box_Input_Txt_Color',
      

    ];
}

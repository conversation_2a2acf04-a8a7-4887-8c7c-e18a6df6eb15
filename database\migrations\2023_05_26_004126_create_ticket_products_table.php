<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ticket_products', function (Blueprint $table) {
            $table->id();

            $table->longText('Product_Code')->nullable();
			$table->longText('P_Ar_Name')->nullable();
			$table->longText('P_En_Name')->nullable();
            $table->longText('Price')->nullable();
            $table->longText('Weight')->nullable();
            $table->longText('Length')->nullable();
            $table->longText('Width')->nullable();
            $table->longText('Height')->nullable();
			$table->longText('Qty')->nullable();
			$table->longText('Unit')->nullable();
			$table->longText('Total')->nullable();
			$table->longText('Store')->nullable();
			$table->longText('Product')->nullable();
			$table->longText('Ticket')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ticket_products');
    }
}

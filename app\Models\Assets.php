<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Assets extends Model
{
    use HasFactory;
         protected $table = 'assets';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Asset_Type',
        'Depreciation_Method',
        'Asset_Type_En',
        'Depreciation_Method_En',
        'Purchases_Date',
        'Operation_Date',
        'Cost',
        'Previous_Depreciation',
        'Asset_Net',
        'Annual_Depreciation_Ratio',
        'Annual_Depreciation',
        'Life_Span',
        'Image',
        'Note',
        'Depreciation_Expenses',
        'Depreciation_Complex',
        'Main_Account',
        'Account',
        'User',
        'Draw',
        'Coin',
        'Cost_Center',
          'Branch',
          'Sort_Asset',
          'Vendor',
          'Safe',
          'Ehlak',
          'Payment_Method',
          'M1',
          'M2',
          'M3',
          'M4',
          'M5',
          'M6',
          'M7',
          'M8',
          'M9',
          'M10',
          'M11',
          'M12',

     
    ];
    
    
             public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
                 public function Ehlak()
    {
        return $this->belongsTo(AcccountingManual::class,'Ehlak');
    }
    
        public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
    
    
           public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
       public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
         public function Depreciation_Expenses()
    {
        return $this->belongsTo(AcccountingManual::class,'Depreciation_Expenses');
    }
    
             public function Depreciation_Complex()
    {
        return $this->belongsTo(AcccountingManual::class,'Depreciation_Complex');
    }
    
             public function Main_Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Main_Account');
    }
    
             public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
    
            public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeachersGroups extends Model
{
    use HasFactory;
      protected $table = 'teachers_groups';
      protected $fillable = [

                'Teacher',
                'Group',

           
            
 
          
    ];
    
    
        public function Teacher()
    {
        return $this->belongsTo(Teachers::class,'Teacher');
    }    
        public function Group()
    {
        return $this->belongsTo(StudentGroup::class,'Group');
    }    
    
}

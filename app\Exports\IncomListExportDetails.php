<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\AcccountingManual;
use App\Models\GeneralDaily;
use DB ;
class IncomListExportDetails implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->from;

         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $safe=$storex['safe'];
         $account=$storex['account'];
         $subAccount=$storex['subAccount'];
    
        
$items = AcccountingManual::orderBy('Code','asc')
      
           
        ->when(!empty($subAccount), function ($query) use ($subAccount) {
        return $query->whereIn('id', $subAccount);
    })        
          ->get(); 
       
             $result = array();
             $resultDetails = array();
        foreach($items as $item){
            
                                 $subAccount=$item->id;
                                              $lists=GeneralDaily::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                                
                                    ->whereIn('Type',['سند قبض','دفع شيك وارد','القيود اليومية','تحويلات الخزائن'])                
                    
                                                ->get();
                                                
                                $TotD=GeneralDaily::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                                
                       ->whereIn('Type',['سند قبض','دفع شيك وارد','القيود اليومية','تحويلات الخزائن'])
                                                ->get()->sum('Debitor_Coin');
                                                
                                              $TotC=GeneralDaily::whereBetween('Date',[$from,$to]) 
                                
                                     
                               ->when(!empty($subAccount), function ($query) use ($subAccount) {
                                            return $query->where('Account', $subAccount);
                                        }) 
                                                
                       ->whereIn('Type',['سند قبض','دفع شيك وارد','القيود اليومية','تحويلات الخزائن'])
                                                ->get()->sum('Creditor_Coin');             
                                                
           


     $Tot=$TotD - $TotC ;
            

            
            foreach($lists as $detail){
                
                
                if(app()->getLocale() == 'ar'){
           
                     $Name=$item->Name;
                     $TypeName=$detail->Type;
                     $AccountName=$detail->Account()->first()->Name;
                     $CoinName=$detail->Coin()->first()->Arabic_Name;
                }else{

                  $Name=$item->NameEn;  
                 $TypeName=$detail->TypeEn;    
                    $AccountName=$detail->Account()->first()->NameEn;
                     $CoinName=$detail->Coin()->first()->English_Name;    
                    
                }

                          if($Tot != 0){
               
   $resultDetails[] = array(
       
       
              'Name'=>$Name,
              'Total' =>$Tot,
              'Code'=>$detail->Code,
              'Date' =>$detail->Date,
              'Type' =>$TypeName,
              'Code_Type' =>$detail->Code_Type,
              'Debitor' =>$detail->Debitor,
              'Creditor' =>$detail->Creditor,    
              'Statement'=>$detail->Statement,
              'Draw' =>$detail->Draw,
              'Debitor_Coin' =>$detail->Debitor_Coin,
              'Creditor_Coin' =>$detail->Creditor_Coin,
              'Account' =>$AccountName,
              'Coin' =>$CoinName,
             
                                              
        
           );
            }  
                
            }
            

            
           
        }
                                                
                     
        return collect($resultDetails);
    }
    

    public function headings(): array
    {
        return [
          'Name',
          'Total',
          'Code',
          'Date',
          'Type',
          'Code_Type',
          'Debitor',
          'Creditor',         
           'Statement',
          'Draw',
          'Debitor_Coin',
          'Creditor_Coin',
          'Account',
          'Coin'

        ];
    }
    
    
    

}

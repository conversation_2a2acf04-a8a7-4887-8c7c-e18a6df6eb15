<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Countris extends Model
{
    use HasFactory;
          protected $table = 'countris';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Flag',
        'Safe',
        'Coin',
        'Store',
           'Code',
           'SearchCode',
      
    ];
    
             public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
                 public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
                 public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
}

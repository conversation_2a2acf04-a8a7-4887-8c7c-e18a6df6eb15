<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StoresDefaultDataTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('stores_default_data')->delete();
        
        \DB::table('stores_default_data')->insert(array (
            0 => 
            array (
                'id' => 3,
                'Group' => 33,
                'Unit' => 10,
                'Tax' => 1,
                'Coin' => 1,
                'Account_Excess' => 30,
                'Account_Dificit' => 30,
                'Store' => 4,
                'created_at' => '2022-04-13 02:46:29',
                'updated_at' => '2022-04-13 02:52:12',
                'Type' => 'Completed',
                'Style' => '6',
                'StoresTarnsferPrice' => '0',
                'Guide_Product_Cost' => '0',
                'Client_Store_Account' => '0',
                'Show_Ship' => NULL,
                'StoresTarnsferHide' => NULL,
                'CodeType' => NULL,
            ),
        ));
        
        
    }
}
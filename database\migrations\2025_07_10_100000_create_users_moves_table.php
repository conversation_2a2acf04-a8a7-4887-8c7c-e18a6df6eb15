<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersMovesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users_moves', function (Blueprint $table) {
            $table->increments('id');
            $table->string('User');
            $table->string('Date');
            $table->string('Time');
            $table->string('Screen');
            $table->string('ScreenEn');
            $table->string('Type');
            $table->string('TypeEn');
            $table->string('Explain');
            $table->string('ExplainEn');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users_moves');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingList extends Model
{
    use HasFactory;
       protected $table = 'shipping_lists';
      protected $fillable = [

                'Ticket_Store',
                'Driver',
                'Car_Store',
                'Travel_Area',
                'Access_Area',
                'Date_Travel',
                'Date_Arrival',
                'Car_Number',
                'Code',
                'Date',
                'Total_Cash',
                'Total_Later',
                'Total_Price',
                'Tickets_Numbers',
                'Status',
              
    ];
    
    
        public function Driver()
    {
        return $this->belongsTo(Employess::class,'Driver');
    }
        
            public function Ticket_Store()
    {
        return $this->belongsTo(Stores::class,'Ticket_Store');
    }
    
               public function Car_Store()
    {
        return $this->belongsTo(Stores::class,'Car_Store');
    }
    
    
               public function Travel_Area()
    {
        return $this->belongsTo(Governrate::class,'Travel_Area');
    }
    
            public function Access_Area()
    {
        return $this->belongsTo(Governrate::class,'Access_Area');
    }
    
   
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DesviceCases extends Model
{
    use HasFactory;
        protected $table = 'desvice_cases';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',            
    ];
    
    
            public function ReciptMaintaince()
    {
        return $this->hasOne(ReciptMaintaince::class);
    }
    
    
}

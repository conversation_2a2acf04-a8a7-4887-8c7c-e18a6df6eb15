<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentReceipts extends Model
{
    use HasFactory;
       protected $table = 'shipment_receipts';
      protected $fillable = [

                'Recived_Store',
                'Code',
                'Date',
                'Total_Cash',
                'Total_Later',
                'Total_Price',
                'Tickets_Numbers',
                'Status',
                'ShippingList',
    
    ];
    
    
    
        
            public function Recived_Store()
    {
        return $this->belongsTo(Stores::class,'Recived_Store');
    }
    
    
            public function ShippingList()
    {
        return $this->belongsTo(ShippingList::class,'ShippingList');
    }
}

<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use DB;
use App\Models\Modules;
use App\Models\StoreCount;
class RoleController extends Controller
{

function __construct()
{

$this->middleware('permission:صلاحيات المستخدمين', ['only' => ['AdminsPremationsPage','AddPrem','DeletePrem','EditPrem']]);

}

    //Admins Premations 
      public function AdminsPremationsPage(Request $request){
            $roles = Role::orderBy('id','DESC')->paginate(10);
        
          
$Modules=Modules::orderBy('id','desc')->first();
              if($Modules->Capital == 1){
                  $cR=15;
              }else{
                 $cR=null; 
                  
              } 

            if($Modules->Accounts == 1){
                  $AR=4;
                  $ARR=8;
              }else{
                 $AR=null; 
                 $ARR=null; 
                  
              } 
          
            if($Modules->Stores == 1){
                  $SR=2;
                  $SRR=3;
                  $SRRR=9;
                  $SRRRR=6;
                  $SRRRRR=7;
              }else{
                  $SR=null;
                  $SRR=null;
                  $SRRR=null;
                  $SRRRR=null;
                  $SRRRRR=null;
                  
              } 
          
            if($Modules->CRM == 1){
                  $cmR=5;
              }else{
                 $cmR=null; 
                  
              } 
          
            if($Modules->HR == 1){
                  $hR=10;
                  $hRReport=22;
              }else{
                 $hR=null; 
                 $hRReport=null; 
                  
              } 
          
            if($Modules->Manufacturing == 1){
                  $mnR=14;
              }else{
                 $mnR=null; 
                  
              } 
          
            if($Modules->Maintenance == 1){
                  $mR=13;
              }else{
                 $mR=null; 
                  
              } 
          
            if($Modules->Secretariat == 1){
                  $SCR=16;
              }else{
                 $SCR=null; 
                  
              } 
  
           if($Modules->Petrol == 1){
                  $PPP=17;
              }else{
                 $PPP=null; 
                  
              } 
          
            if($Modules->ECommerce == 1){
                  $ECOM=18;
              }else{
                 $ECOM=null; 
                  
              } 
                if($Modules->Shipping == 1){
                  $SH=19;
              }else{
                 $SH=null; 
                  
              }  
          
           if($Modules->Bill_Electronic == 1){
                  $EB=20;
              }else{
                 $EB=null; 
                  
              }  
          
          
           if($Modules->Hotels == 1){
                  $H=21;
              }else{
                 $H=null; 
                  
              }            
           if($Modules->Resturant == 1){
                  $Rst=23;
              }else{
                 $Rst=null; 
                  
              }  
          
          
             if($Modules->Traning_Center == 1){
                 $traning=24;
              }else{
                 $traning=null; 
                  
              }  
          
             if($Modules->Translate == 1){
                 $trans=25;
              }else{
                 $trans=null; 
                  
              }  
          
          
          

                $permission = Permission::orderBy('Main','asc')
                    ->whereIn('Main',[1,11,12,$cR,$AR,$ARR,$SR,$SRR,$SRRR,$SRRRR,$SRRRRR,$cmR,$hR,$mnR,$mR,$SCR,$PPP,$ECOM,$SH,$EB,$H,$hRReport,$Rst,$traning,$trans])
                    ->get();
          
          
          
        return view('admin.AdminsPremations',['roles'=>$roles,'permission'=>$permission])
                    ->with('i', ($request->input('page', 1) - 1) * 5);  
    }
       
    public function AddPrem(Request $request)
{
$this->validate($request, [
'name' => 'required|unique:roles,name',
'permission' => 'required',
]);
        
        
     
        
$role = Role::create(['name' => $request->input('name'),'nameEn' => $request->input('nameEn')]);    
$role->syncPermissions($request->input('permission'));
        
           session()->flash('success',trans('admin.Added_Successfully'));
             return back();
}
    
    public function DeletePrem($id)
{
DB::table("roles")->where('id',$id)->delete();
     session()->flash('error',trans('admin.Deleted'));
             return back();
        
        
}
    
    
public function EditPrem(Request $request, $id)
{
$this->validate($request, [
'name' => 'required',
'permission' => 'required',
]);
$role = Role::find($id);
$role->name = $request->input('name');
$role->nameEn = $request->input('nameEn');
$role->save();
$role->syncPermissions($request->input('permission'));

         session()->flash('success',trans('admin.Updated'));
             return back();
    
}
    
    

}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMaintainceDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('maintaince_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Company')->nullable();
            $table->string('Device_Type')->nullable();
            $table->string('Device_Case')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Client')->nullable();
            $table->string('Sure')->nullable();
            $table->string('Eng')->nullable();
            $table->string('Recipient')->nullable();
            $table->string('Store')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('maintaince_default_data');
    }
}
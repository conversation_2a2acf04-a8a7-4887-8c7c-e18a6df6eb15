<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePurchasesDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('purchases_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Payment_Method');
            $table->string('Status');
            $table->string('V_and_C');
            $table->string('Mainus')->nullable();
            $table->string('Price_Sale')->nullable();
            $table->integer('Safe');
            $table->integer('Vendor');
            $table->integer('Delegate');
            $table->integer('Store');
            $table->integer('Coin');
            $table->timestamps();
            $table->string('Brand')->nullable();
            $table->string('Group')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Expire')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Shift_Pass')->nullable();
            $table->string('Empp')->nullable();
            $table->string('Discount')->nullable();
            $table->string('Delivery')->nullable();
            $table->string('Execute_Precent')->nullable();
            $table->string('StoresQty')->nullable();
            $table->string('DelegateEmp')->nullable();
            $table->string('TaxType')->nullable();
            $table->string('Quality_Qty')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('purchases_default_data');
    }
}
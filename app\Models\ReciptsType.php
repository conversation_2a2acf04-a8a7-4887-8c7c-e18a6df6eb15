<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReciptsType extends Model
{
    use HasFactory;
                    protected $table = 'recipts_types';
      protected $fillable = [
        'Name',
        'NameEn',
        'Account',

     
    ];
    
    
      public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
}

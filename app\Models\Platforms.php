<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Platforms extends Model
{
    use HasFactory;
        protected $table = 'platforms';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
   
    ];
    
        public function Campaigns()
    {
        return $this->hasOne(Campaigns::class);
    }
    
          public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
    
}

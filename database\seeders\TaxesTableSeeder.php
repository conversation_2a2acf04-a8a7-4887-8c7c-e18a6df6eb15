<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class TaxesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('taxes')->delete();
        
        \DB::table('taxes')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Code' => '1',
                'Name' => 'بدون ضريبه',
                'Rate' => '0',
                'Type' => '2',
                'created_at' => '2021-07-01 07:16:47',
                'updated_at' => '2021-07-01 07:16:47',
                'Hide' => '1',
                'Account' => 112,
                'CodeTax' => NULL,
                'SubType' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'Code' => '2',
                'Name' => 'القيمه المضافه',
                'Rate' => '14',
                'Type' => '1',
                'created_at' => '2021-07-01 07:17:08',
                'updated_at' => '2021-07-01 07:17:36',
                'Hide' => '0',
                'Account' => 40,
                'CodeTax' => NULL,
                'SubType' => NULL,
            ),
        ));
        
        
    }
}
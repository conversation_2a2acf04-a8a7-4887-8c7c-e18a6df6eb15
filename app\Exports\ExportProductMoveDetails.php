<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductMoves;
use DB;
class ExportProductMoveDetails implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->store;

         $storee=$storex['store'];
         $from=$storex['from'];
         $to=$storex['to'];
         $type=$storex['type'];
         $safe=$storex['safe'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $user=$storex['user'];
         $branch=$storex['branch'];
         $product_Name=$storex['product_Name'];
         $product_Code=$storex['product_Code'];
         $payment_method=$storex['payment_method'];
    
   
                if(app()->getLocale() == 'ar' ){ 
          $prods = DB::table('product_moves')->whereBetween('product_moves.Date',[$from,$to])
              
                      ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('product_moves.Store',$storee);
    })
       

                 ->when(!empty($type), function ($query) use ($type) {
        return $query->whereIn('product_moves.Type', $type);
    })
          
 
                  ->when(!empty($payment_method), function ($query) use ($payment_method) {
        return $query->whereIn('product_moves.Payment_Method', $payment_method);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('product_moves.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('product_moves.Brand', $brand);
    })  
       
       
       
                   ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('product_moves.User', $user);
    }) 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('product_moves.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('product_moves.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('product_moves.P_Code', $product_Code);
    })  
              
                     ->join('measuerments', function ($join) {
    
            $join->on('product_moves.Unit', '=', 'measuerments.id');
        })  
              
       
              
                ->join('stores', function ($join) {
    
            $join->on('product_moves.Store', '=', 'stores.id');
        })
              
                   ->join('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
              
              
                  ->join('products', function ($join) {
    
            $join->on('product_moves.Product', '=', 'products.id');
        })
            
                ->leftJoin('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })   
              
              ->leftJoin('safes_banks', function ($join) {
    
            $join->on('product_moves.Safe', '=', 'safes_banks.Account');
        })
          ->leftJoin('admins', function ($join) {
    
            $join->on('product_moves.User', '=', 'admins.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           
        
->select('product_moves.P_Code'
         ,'product_moves.P_Ar_Name'
         ,'measuerments.Name as UnitName'
         ,'product_moves.QTY as UnitQty'
         ,'product_moves.Date'
         ,'product_moves.Type'
         ,'product_moves.Bill_Num'
 
         ,'product_moves.Incom'
         ,'product_moves.CostIn'

  
         ,'product_moves.Outcom'
         ,'product_moves.CostOut'

         ,'product_moves.Current'
         ,'product_moves.CostCurrent'
    
         ,'items_groups.Name as GroupName'
         ,'stores.Name as StoreName'
         ,'admins.name as AdminName'
         ,'brands.Name as BrandsName'
         ,'safes_banks.Name as SafesName'
         ,'branches.Arabic_Name as BranchName'
         ,'product_moves.Payment_Method'
        )
                  ->get();
                    
                    
                }else{
                    
               $prods = DB::table('product_moves')->whereBetween('product_moves.Date',[$from,$to])
              
                      ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('product_moves.Store',$storee);
    })
       

                 ->when(!empty($type), function ($query) use ($type) {
        return $query->whereIn('product_moves.Type', $type);
    })
          
 
                  ->when(!empty($payment_method), function ($query) use ($payment_method) {
        return $query->whereIn('product_moves.Payment_Method', $payment_method);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('product_moves.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('product_moves.Brand', $brand);
    })  
       
       
       
                   ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('product_moves.User', $user);
    }) 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('product_moves.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('product_moves.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('product_moves.P_Code', $product_Code);
    })  
              
                     ->join('measuerments', function ($join) {
    
            $join->on('product_moves.Unit', '=', 'measuerments.id');
        })  
              
       
              
                ->join('stores', function ($join) {
    
            $join->on('product_moves.Store', '=', 'stores.id');
        })
              
                   ->join('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
              
              
                  ->join('products', function ($join) {
    
            $join->on('product_moves.Product', '=', 'products.id');
        })
            
                ->leftJoin('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })   
              
              ->leftJoin('safes_banks', function ($join) {
    
            $join->on('product_moves.Safe', '=', 'safes_banks.Account');
        })
          ->leftJoin('admins', function ($join) {
    
            $join->on('product_moves.User', '=', 'admins.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           
        
->select('product_moves.P_Code'
         ,'product_moves.P_En_Name'
         ,'measuerments.NameEn as UnitName'
         ,'product_moves.QTY as UnitQty'
         ,'product_moves.Date'
         ,'product_moves.Type'
         ,'product_moves.Bill_Num'
 
         ,'product_moves.Incom'
         ,'product_moves.CostIn'

  
         ,'product_moves.Outcom'
         ,'product_moves.CostOut'

         ,'product_moves.Current'
         ,'product_moves.CostCurrent'
    
         ,'items_groups.NameEn as GroupName'
         ,'stores.NameEn as StoreName'
         ,'admins.nameEn as AdminName'
         ,'brands.NameEn as BrandsName'
         ,'safes_banks.NameEn as SafesName'
         ,'branches.English_Name as BranchName'
         ,'product_moves.Payment_Method'
        )
                  ->get();
                             
                    
                    
                }

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Name',
          'Unit',
          'Unit Qty',
          'Date',
          'Type',
          'Bill_Num',

          'Qty',
          'Goods_Cost',


          'OQty',
          'OGoods_Cost',


          'CQty',
          'CGoods_Cost',

          'Group',
          'Store',
          'User',
          'Brand',
          'Safe',
          'Branch',
          'Payment_Method'
        ];
    }
    
    
    


















}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingOrder extends Model
{
    use HasFactory;
               protected $table = 'manufacturing_orders';
      protected $fillable = [
        'Code',
        'NewCode',
        'Date',
        'Model',
        'Name_Outcome',
        'Except_Qty',
        'Total_Required_Qty',
        'Status',
        'For_Client',
        'Client',
        'Client_Phone',
        'Client_Address',
        'Delegate',
        'Delegate_Phone',
        'Recived_Date',
        'Manufacture_Request_Code',
        'Recipient',
           
   
    ];
    
        public function Model()
    {
        return $this->belongsTo(ManufacturingModel::class,'Model');
    }
    
    
       public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
           public function Manufacture_Request_Code()
    {
        return $this->belongsTo(ManufacturingRequest::class,'Manufacture_Request_Code');
    }

           public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }

    
 
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CountersType extends Model
{
    use HasFactory;
            protected $table = 'counters_types';
      protected $fillable = [

                    'Name',
                    'NameEn',
                    'Current_Read',
                    'Store',
                    'Product',
                  
          
    ];
    
        public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    



}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateItemsGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('items_groups', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Code');
            $table->string('Name');
            $table->string('Type');
            $table->string('Parent');
            $table->string('Note')->nullable();
            $table->string('Image')->nullable();
            $table->timestamps();
            $table->string('Discount')->nullable();
            $table->string('Store_Show')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('items_groups');
    }
}
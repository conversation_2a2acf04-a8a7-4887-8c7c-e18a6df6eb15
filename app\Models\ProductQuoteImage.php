<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductQuoteImage extends Model
{
    use HasFactory;
       protected $table = 'product_quote_images';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Qty',
        'Price',  
        'Discount',
        'TDiscount',
        'Tax',
        'Total_Bf_Tax',
        'Total',
        'Total_Tax',
        'Product',
        'V1',
        'V2',
        'Unit',
        'QuoteImage',

    ];

    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
               public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
            public function QuoteImage()
    {
        return $this->belongsTo(QuoteImage::class,'QuoteImage');
    }
    
}

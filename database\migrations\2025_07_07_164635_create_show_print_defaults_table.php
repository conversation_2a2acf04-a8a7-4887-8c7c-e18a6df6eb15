<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShowPrintDefaultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('show_print_defaults', function (Blueprint $table) {
            $table->increments('id');
            $table->string('CompanyName');
            $table->string('ProductName');
            $table->string('ProductPrice');
            $table->string('Coin');
            $table->string('Unit');
            $table->string('Group');
            $table->string('Code');
            $table->string('Logo');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('show_print_defaults');
    }
}
<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductMoves;
class ExportCompareSalesPrice implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->store;

         $storee=$storex['store'];
         $from=$storex['from'];
         $to=$storex['to'];
         $type=$storex['type'];
         $safe=$storex['safe'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $user=$storex['user'];
         $branch=$storex['branch'];
         $product_Name=$storex['product_Name'];
         $product_Code=$storex['product_Code'];
         $delegate=$storex['delegate'];
    
   

      $items=ProductMoves::whereBetween('Date',[$from,$to])
          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store',$storee);
    })
       

                 ->when(!empty($type), function ($query) use ($type) {
        return $query->whereIn('Type', $type);
    })
       
                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('Safe', $safe);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
                             ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('Delegate', $delegate);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
       
       
                   ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('User', $user);
    }) 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })  
        ->get();   
       
              
        

        $result = array();
        foreach($items as $record){
            
             if(!empty($record->Unit()->first()->Name)){
                 
                    if(app()->getLocale() == 'ar' ){ 
                   $Unit=$record->Unit()->first()->Name; 
                    }else{
                        
                   $Unit=$record->Unit()->first()->NameEn;        
                    }
                }else{
                    $Unit='';
                }
            
                    if(!empty($record->Brand()->first()->Name)){
                        
                               if(app()->getLocale() == 'ar' ){ 
                   $BRAND=$record->Brand()->first()->Name;
                               }else{
                            $BRAND=$record->Brand()->first()->NameEn;           
                                   
                               }
                }else{
                    $BRAND='';
                }
            
                   if(!empty($record->Group()->first()->Name)){
                       
                         if(app()->getLocale() == 'ar' ){ 
                    $Group=$record->Group()->first()->Name;
                         }else{
                       $Group=$record->Group()->first()->NameEn;         
                         }
                }else{
                    $Group='';
                }
            
                   if(!empty($record->Store()->first()->Name)){
                       
                                if(app()->getLocale() == 'ar' ){ 
                    $Store=$record->Store()->first()->Name;
                                }else{
                             $Store=$record->Store()->first()->NameEn;         
                                    
                                }
                }else{
                    $Store='';
                }
            
                   if(!empty($record->Safe()->first()->Name)){
                       
                                 if(app()->getLocale() == 'ar' ){ 
                    $Safe=$record->Safe()->first()->Name;
                                 }else{
                                     
                             $Safe=$record->Safe()->first()->NameEn;          
                                 }
                }else{
                    $Safe='';
                }
            
                     if(!empty($record->Branch()->first()->Arabic_Name)){
                         
                        if(app()->getLocale() == 'ar' ){       
                   $BRANCH=$record->Branch()->first()->Arabic_Name; 
                        }else{
                            
                        $BRANCH=$record->Branch()->first()->English_Name;        
                        }
                }else{
                    $BRANCH='';
                }
              
    if(!empty($record->Delegate()->first()->Name)){
           if(app()->getLocale() == 'ar' ){ 
                   $DELE=$record->Delegate()->first()->Name; 
           }else{
                  $DELE=$record->Delegate()->first()->NameEn;     
               
           }
                }else{
                    $DELE='';
                }


            
if($record->Incom != 0){
$IncomCostPer=$record->CostIn / $record->Incom;
}else{
 $IncomCostPer=$record->CostIn / 1 ;   
}

if($record->Outcom != 0){
$OutcomCostPer=$record->CostOut / $record->Outcom;
}else{
 $OutcomCostPer=$record->CostOut / 1 ;   
}

if($record->Current != 0){
$CurrentCostPer=$record->CostCurrent / $record->Current;
}else{
 $CurrentCostPer=$record->CostCurrent / 1 ;   
}
   
            
            


   
                                                  if($record->Current != 0){
                                              $cost=$record->CostCurrent / $record->Current ;  
                                                  }else{
                                                 $cost=$record->CostCurrent / 1 ;   
                                                    } 
                                            
                                       
                                              $M7kk=$record->SalePrice - $cost;   
                             
                                        $Mtlop=$record->ProductPrice - $cost;            
                                         
                                        $profit=$Mtlop - $M7kk ;
            
            
                  if(app()->getLocale() == 'ar' ){ 
                      
                      $useName=$record->User()->first()->name;
                  }else{
                      
                        $useName=$record->User()->first()->nameEn; 
                  }

           $result[] = array(
              'Code'=>$record->P_Code,
              'Name' => $record->P_Ar_Name,
              'Unit' => $Unit .' '.($record->QTY),
              'Date' => $record->Date,
              'Type' => $record->Type,
              'Bill_Num' => $record->Bill_Num,
              'Incom' => '',
              'Qty' => $record->Incom,
              'Goods_Cost' => $record->CostIn,
              'Cost_Per_One' =>$IncomCostPer,
              'Outcom' => '',
              'OQty' => $record->Outcom,
              'OGoods_Cost' => $record->CostOut,
              'OCost_Per_One' => $OutcomCostPer,
              'Balance' => '',
              'CQty' => $record->Current,
              'CGoods_Cost' => $record->CostCurrent,
              'CCost_Per_One' =>$CurrentCostPer ,
              'Group' => $Group,
              'Store' => $Store,
              'User' => $useName,
              'Brand' => $BRAND,
              'Safe' => $Safe,
              'Branch' => $BRANCH,
              'SalePrice' => $record->SalePrice,
              'ProductPrice' => $record->ProductPrice,
              'TotalAchieveProfit' => $M7kk,
              'TotalProfitRequested' => $Mtlop,
              'ProfitDif' => $profit,
              'Delegate' => $DELE
                                              
        
           );
        }

        return collect($result);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Name',
          'Unit',
          'Date',
          'Type',
          'Bill_Num',
          'Incom',
          'Qty',
          'Goods_Cost',
          'Cost_Per_One',
          'Outcom',
          'OQty',
          'OGoods_Cost',
          'OCost_Per_One',
          'Balance',
          'CQty',
          'CGoods_Cost',
          'CCost_Per_One',
          'Group',
          'Store',
          'User',
          'Brand',
          'Safe',
          'Branch',
          'SalePrice',
          'ProductPrice',
          'TotalAchieveProfit',
          'TotalProfitRequested',
          'ProfitDif',
          'Delegate'
        ];
    }
    
 

}

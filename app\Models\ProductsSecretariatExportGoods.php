<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsSecretariatExportGoods extends Model
{
    use HasFactory;
        protected $table = 'products_secretariat_export_goods';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Qty',
        'Date',
        'Product',
        'V1',
        'V2',
        'Export',
        'Unit',
        'Store',
        'Note',
        'RecivedQty',
        'Impotence',
        'Price',
        'Total',
    
    ];

         public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }
    
 
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
    
            public function Export()
    {
        return $this->belongsTo(SecretariatExportGoods::class,'Export');
    }
    
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ProductUnit implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         //DB::table('products')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('product_units')->insert([

            'Rate'	   =>$value[1]
            ,'Barcode'  =>$value[2]
            ,'Price'  =>$value[3]
            ,'Price_Two'  =>$value[4]
            ,'Price_Three'  =>$value[5]
            ,'P_Ar_Name'  =>$value[6]
            ,'P_En_Name'  =>$value[7]
            ,'P_Type'  =>$value[8]
            ,'Unit'  =>$value[9]
            ,'Product'  =>$value[10]
            ,'created_at'  =>$value[11]
            ,'updated_at'  =>$value[12]
            ,'Def'  =>$value[13]
        
           

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

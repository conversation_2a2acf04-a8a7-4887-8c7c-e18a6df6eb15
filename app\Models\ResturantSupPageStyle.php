<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantSupPageStyle extends Model
{
    use HasFactory;
       protected $table = 'resturant_sup_page_styles';
      protected $fillable = [
          
        'Blogs_BG_Type',
        'Blogs_BG_Image',
        'Blogs_BG_Color',
        'Blogs_Title_Color',
        'Blogs_Top_Image',
        'Blogs_Box_BG',
        'Blogs_Box_Border',
        'Blogs_Box_Txt_Color',
        'Blogs_Box_Txt_Hover_Color',
        'Blogs_Btn_Color',
        'Blogs_Btn_BG',
        'Blogs_Details_Box_BG',
        'Blogs_Details_Box_Border',
        'Blogs_Details_Box_Txt_Color',
          
        'Reviews_Top_Image',
        'Reviews_Title_Color',
        'Reviews_BG_Type',
        'Reviews_BG_Image',
        'Reviews_BG_Color',
        'Reviews_Box_BG',
        'Reviews_Box_Border',
        'Reviews_Icon_Color',
          
        'Gallery_Top_Image',
        'Gallery_Title_Color', 
        'Gallery__BG_Type', 
        'Gallery__BG_Image', 
        'Gallery__BG_Color', 
          
        'Terms_Privacy_Top_Image', 
        'Terms_Privacy_Title_Color', 
        'Terms_Privacy_Box_BG', 
        'Terms_Privacy_Box_Border', 
     
       
    ];
}

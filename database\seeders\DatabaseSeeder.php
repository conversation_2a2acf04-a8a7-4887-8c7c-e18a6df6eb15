<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
 
        $this->call(AdminsTableSeeder::class);
        $this->call(AcccountingManualsTableSeeder::class);
        $this->call(WorkDepartmentsTableSeeder::class);
        $this->call(PlatformsTableSeeder::class);
        $this->call(ActivitesTableSeeder::class);
       $this->call(AboutsTableSeeder::class);
        $this->call(WebSlidersTableSeeder::class);
        $this->call(BefroeFootersTableSeeder::class);
        $this->call(QRSTableSeeder::class);
        $this->call(ItemsGroupsTableSeeder::class);
        $this->call(VirablesTableSeeder::class);
        $this->call(BrandsTableSeeder::class);
        $this->call(BranchesTableSeeder::class);
        $this->call(ArticlesTableSeeder::class);
        $this->call(ChecksTypesTableSeeder::class);
        $this->call(CoinsTableSeeder::class);
        $this->call(ModulesTableSeeder::class);
        $this->call(ModuleSettingsNumsTableSeeder::class);
        $this->call(StoresTableSeeder::class);
        $this->call(CostCentersTableSeeder::class);
        $this->call(CapitalsTableSeeder::class);
        $this->call(CampaignsTableSeeder::class);
        $this->call(ContactUSTableSeeder::class);
        $this->call(MeasuermentsTableSeeder::class);
        $this->call(InterviewsTypesTableSeeder::class);
        $this->call(ManufacturingHallsTableSeeder::class);
        $this->call(SafesBanksTableSeeder::class);
        $this->call(ShippingTypesTableSeeder::class);
        $this->call(ShippingStatusesTableSeeder::class);
        $this->call(VendorsStatementsColumnsTableSeeder::class);
        $this->call(VendorsStatementsColumnSechdulesTableSeeder::class);
        $this->call(VendorAccountStatementColumnsTableSeeder::class);
        $this->call(VendorAccountStatementColumnSechdulesTableSeeder::class);
        $this->call(StoresTransferColumnsSechdulesTableSeeder::class);
        $this->call(SalesBillsColumnsSechdulesTableSeeder::class);
        $this->call(SafeTransferColumnsSechdulesTableSeeder::class);
        $this->call(PurchBillsColumnsSechdulesTableSeeder::class);
        $this->call(MostSalesProductsColumnsTableSeeder::class);
        $this->call(InstallCompaniesSalesBillsColumnsSechdulesTableSeeder::class);
        $this->call(InstallCompaniesSalesBillsColumnsTableSeeder::class);
        $this->call(MostSalesProductsColumnSechdulesTableSeeder::class);
        $this->call(ClientsStatementsColumnsTableSeeder::class);
        $this->call(ProductMovesColumnsSechdulesTableSeeder::class);
        $this->call(ComparePricesColumnsSechdulesTableSeeder::class);
        $this->call(ClientAccountStatementColumnsTableSeeder::class);
        $this->call(ClientAccountStatementColumnSechdulesTableSeeder::class);
        $this->call(ClientsStatementsColumnSechdulesTableSeeder::class);
        $this->call(ComparePricesColumnsTableSeeder::class);
        $this->call(ProductMovesColumnsTableSeeder::class);
        $this->call(ProDetailsImgsTableSeeder::class);
        $this->call(PolicesTableSeeder::class);
        $this->call(ClientStatusesTableSeeder::class);
        $this->call(PurchBillsColumnsTableSeeder::class);
        $this->call(ProfitSalesProductColumnsTableSeeder::class);
        $this->call(ProfitSalesProductColumnSechdulrsTableSeeder::class);
        $this->call(ProductTypeDefaultsTableSeeder::class);
        $this->call(SafeTransferColumnsTableSeeder::class);
        $this->call(SalesBillsColumnsTableSeeder::class);
        $this->call(StoresTransferColumnsTableSeeder::class);
        $this->call(StoresMovesColumnsSechdulesTableSeeder::class);
        $this->call(ExpensesListColumnsTableSeeder::class);
        $this->call(ExpensesListColumnSechdulesTableSeeder::class);
        $this->call(StoresMovesColumnsTableSeeder::class);
        $this->call(TransltorsTableSeeder::class);
        $this->call(ManufactureCompaniesTableSeeder::class);
        $this->call(MaintainceColorsTableSeeder::class);
        $this->call(JobsTypesTableSeeder::class);
        $this->call(FAQSTableSeeder::class);
        $this->call(DevicesTypesiesTableSeeder::class);
        $this->call(CouponCodesTableSeeder::class);
        $this->call(DeviceDescripsTableSeeder::class);
        $this->call(DesviceCasesTableSeeder::class);
        $this->call(CustomersGroupsTableSeeder::class);
        $this->call(CountrisTableSeeder::class);
        $this->call(GovernratesTableSeeder::class);
        $this->call(CitiesTableSeeder::class);
        $this->call(SelectAPISTableSeeder::class);
        $this->call(TermsMaintaincesTableSeeder::class);
        $this->call(TermsTableSeeder::class);
        $this->call(SocialMediaTableSeeder::class);
        $this->call(TaxesTableSeeder::class);
        $this->call(EmployessesTableSeeder::class);
        $this->call(CustomersTableSeeder::class);
         $this->call(PlacesTableSeeder::class);
        $this->call(SubVirablesTableSeeder::class);
        $this->call(BarcodeShowsTableSeeder::class);
        $this->call(BarcodeSettingsTableSeeder::class);
        $this->call(CompanyDataTableSeeder::class);
        $this->call(StoresDefaultDataTableSeeder::class);
        $this->call(ShowPrintDefaultsTableSeeder::class);
        $this->call(ShippingDefaultsTableSeeder::class);
        $this->call(SalesDefaultDataTableSeeder::class);
        $this->call(PurchasesDefaultDataTableSeeder::class);
        $this->call(ManufacturingDefaultDataTableSeeder::class);
        $this->call(MaintainceDefaultDataTableSeeder::class);
        $this->call(DefaultDataShowHidesTableSeeder::class);
        $this->call(CrmDefaultDataTableSeeder::class);
        $this->call(AccountsDefaultDataTableSeeder::class);
        $this->call(PermissionsTableSeeder::class);
        $this->call(RolesTableSeeder::class);
        $this->call(RoleHasPermissionsTableSeeder::class);
        $this->call(ModelHasRolesTableSeeder::class);
        //$this->call(CreateAdminUserSeeder::class);
     
    }
}

<?php

namespace App\Exports;

use App\Models\ItemsGroups;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

use App\Models\Customers;
use App\Models\ProductSales;
use DB;
class ExportSalesCustomersGroupsFilter implements FromCollection ,WithHeadings , WithChunkReading
{
 
    
     private $From=[] ;

    public function __construct($From=0) 
    {
        $this->From = $From;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
 
      set_time_limit(0);

        $storex=$this->From;
        $Fromm =  $storex['From'];
        $To =  $storex['To'];
        $group =  $storex['group'];
        $Client =  $storex['Client'];
     
          $G=ItemsGroups::find($group);
        
        
           if(app()->getLocale() == 'ar' ){ 
        $items=DB::table('customers')  
               ->join('product_sales', 'product_sales.Client', '=', 'customers.Account')   
                ->where('product_sales.Group',$group) 
            
               ->join('products', function ($join) {
    
            $join->on('product_sales.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
               ->select('customers.Name', 'product_sales.Qty','product_sales.Total','items_groups.Name as GroupName')       
            ->get();   
        
           }else{
               
              $items=DB::table('customers')  
               ->join('product_sales', 'product_sales.Client', '=', 'customers.Account')   
                ->where('product_sales.Group',$group) 
            
               ->join('products', function ($join) {
    
            $join->on('product_sales.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
               ->select('customers.NameEn', 'product_sales.Qty','product_sales.Total','items_groups.NameEn as GroupName')       
            ->get();        
               
           }
   

        return collect($items);
    }
    

    public function headings(): array
    {

           $Arrays=array();
         array_push($Arrays,
                     'Client',
              'Total_Qty',
              'Total_Price',
              'Group'

               );    

        
   
        return $Arrays;
    }
    
    
    

    
    
              public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }  
    

}

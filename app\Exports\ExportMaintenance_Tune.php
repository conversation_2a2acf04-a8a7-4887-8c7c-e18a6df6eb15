<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use DB ;
class ExportMaintenance_Tune implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
     
        $storex=$this->from;
        $from =  $storex['from'];
        $to =  $storex['to'];
        $Store =  $storex['Store'];
        $Recipient =  $storex['Recipient'];
        $Eng =  $storex['Eng'];
        $Branch =  $storex['Branch'];
        $Group =  $storex['Group'];
        $Brand =  $storex['Brand'];
        $User =  $storex['User'];
        $code =  $storex['code'];
        $Name =  $storex['Name'];
      
        
        
        
            if(app()->getLocale() == 'ar' ){
          $prods = DB::table('products_return_maintaince_bills')->whereBetween('products_return_maintaince_bills.Date',[$from,$to])
              
               ->when(!empty($code), function ($query) use ($code) {
        return $query->where('products_return_maintaince_bills.Product_Code',"ILIKE", "%{$code}%");  
               
                })       
        
                 ->when(!empty($Name), function ($query) use ($Name) {
        return $query->where('products_return_maintaince_bills.P_Ar_Name',"ILIKE", "%{$Name}%")->orWhere('P_En_Name',"ILIKE", "%{$Name}%");  
               
                })      
       
  
        
        ->when(!empty($Recipient), function ($query) use ($Recipient) {
        return $query->where('products_return_maintaince_bills.Recipient',$Recipient);  
               
                })     
        
        ->when(!empty($Eng), function ($query) use ($Eng) {
        return $query->where('products_return_maintaince_bills.Eng',$Eng);  
               
                })     
        
        ->when(!empty($Store), function ($query) use ($Store) {
        return $query->where('products_return_maintaince_bills.Store',$Store);  
               
                })  
                     
        
        ->when(!empty($Branch), function ($query) use ($Branch) {
        return $query->where('products_return_maintaince_bills.Branch',$Branch);  
               
                })      
        
        ->when(!empty($User), function ($query) use ($User) {
        return $query->where('products_return_maintaince_bills.User',$User);  
               
                }) 
        ->when(!empty($Group), function ($query) use ($Group) {
        return $query->where('products_return_maintaince_bills.Group',$Group);  
               
                })  
        ->when(!empty($Brand), function ($query) use ($Brand) {
        return $query->where('products_return_maintaince_bills.Brand',$Brand);  
               
                })  
                

                  
              
               ->leftJoin('employesses', function ($join) {
    
            $join->on('products_return_maintaince_bills.Eng', '=', 'employesses.id');
        })      
              
         ->leftJoin('employesses as Rec', function ($join) {
    
            $join->on('products_return_maintaince_bills.Recipient', '=', 'employesses.id');
        })      
     
            
              ->leftJoin('admins', function ($join) {
    
            $join->on('products_return_maintaince_bills.User', '=', 'admins.id');
        })
              

                   ->leftJoin('stores', function ($join) {
    
            $join->on('products_return_maintaince_bills.Store', '=', 'stores.id');
        })
              
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })

  ->join('products', function ($join) {
    
            $join->on('products_return_maintaince_bills.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           

->select('products_return_maintaince_bills.Date'
         ,'branches.Arabic_Name as Branch'
           ,'stores.Name as StoreName'
         ,'Rec.Name as RecName'
         ,'employesses.Name as EngName'
         ,'admins.name as UserName'
         ,'products_return_maintaince_bills.Product_Code'
         ,'products_return_maintaince_bills.P_Ar_Name'
         ,'items_groups.Name as GroupName'
         ,'brands.Name as BrandName'
         ,'products_return_maintaince_bills.Recived_Qty'
         ,'products_return_maintaince_bills.Price'
         ,'products_return_maintaince_bills.Total'
        )
                  ->get();
            }else{
             
                
      $prods = DB::table('products_return_maintaince_bills')->whereBetween('products_return_maintaince_bills.Date',[$from,$to])
              
               ->when(!empty($code), function ($query) use ($code) {
        return $query->where('products_return_maintaince_bills.Product_Code',"ILIKE", "%{$code}%");  
               
                })       
        
                 ->when(!empty($Name), function ($query) use ($Name) {
        return $query->where('products_return_maintaince_bills.P_Ar_Name',"ILIKE", "%{$Name}%")->orWhere('P_En_Name',"ILIKE", "%{$Name}%");  
               
                })      
       
  
        
        ->when(!empty($Recipient), function ($query) use ($Recipient) {
        return $query->where('products_return_maintaince_bills.Recipient',$Recipient);  
               
                })     
        
        ->when(!empty($Eng), function ($query) use ($Eng) {
        return $query->where('products_return_maintaince_bills.Eng',$Eng);  
               
                })     
        
        ->when(!empty($Store), function ($query) use ($Store) {
        return $query->where('products_return_maintaince_bills.Store',$Store);  
               
                })  
                     
        
        ->when(!empty($Branch), function ($query) use ($Branch) {
        return $query->where('products_return_maintaince_bills.Branch',$Branch);  
               
                })      
        
        ->when(!empty($User), function ($query) use ($User) {
        return $query->where('products_return_maintaince_bills.User',$User);  
               
                }) 
        ->when(!empty($Group), function ($query) use ($Group) {
        return $query->where('products_return_maintaince_bills.Group',$Group);  
               
                })  
        ->when(!empty($Brand), function ($query) use ($Brand) {
        return $query->where('products_return_maintaince_bills.Brand',$Brand);  
               
                })  
                

                  
              
               ->leftJoin('employesses', function ($join) {
    
            $join->on('products_return_maintaince_bills.Eng', '=', 'employesses.id');
        })      
              
         ->leftJoin('employesses as Rec', function ($join) {
    
            $join->on('products_return_maintaince_bills.Recipient', '=', 'employesses.id');
        })      
     
            
              ->leftJoin('admins', function ($join) {
    
            $join->on('products_return_maintaince_bills.User', '=', 'admins.id');
        })
              

                   ->leftJoin('stores', function ($join) {
    
            $join->on('products_return_maintaince_bills.Store', '=', 'stores.id');
        })
              
        ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })

  ->join('products', function ($join) {
    
            $join->on('products_return_maintaince_bills.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           

->select('products_return_maintaince_bills.Date'
         ,'branches.English_Name as Branch'
           ,'stores.NameEn as StoreName'
         ,'Rec.NameEn as RecName'
         ,'employesses.NameEn as EngName'
         ,'admins.nameEn as UserName'
         ,'products_return_maintaince_bills.Product_Code'
         ,'products_return_maintaince_bills.P_Ar_Name'
         ,'items_groups.NameEn as GroupName'
         ,'brands.NameEn as BrandName'
         ,'products_return_maintaince_bills.Recived_Qty'
         ,'products_return_maintaince_bills.Price'
         ,'products_return_maintaince_bills.Total'
        )
                  ->get();            
                
                
                
                
            }
 
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Branch',
          'Store',
          'Recipient',
          'Eng',
          'User',
          'Product_Code',
          'Product_Name',
          'Group',
          'Brand',
          'Qty',
          'Price',
          'Total',
        ];
    }
    
    
    

}

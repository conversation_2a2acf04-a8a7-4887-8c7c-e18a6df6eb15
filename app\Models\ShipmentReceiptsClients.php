<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentReceiptsClients extends Model
{
    use HasFactory;
     protected $table = 'shipment_receipts_clients';
      protected $fillable = [

                'Code',
                'Date',
                'ShipmentReceipts',
                'ShipmentReceiptsList',
                'Total_Price',
                'Total_Qty',
                'Safe',
                'Coin',
                'Draw',
                'Pay',
                'Payment_Method',
    
    ];
    
    
    
        
            public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
            public function ShipmentReceipts()
    {
        return $this->belongsTo(ShipmentReceipts::class,'ShipmentReceipts');
    }
    
                public function ShipmentReceiptsList()
    {
        return $this->belongsTo(Ticket::class,'ShipmentReceiptsList');
    }
    
    

    
   
    
        public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
                public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
}

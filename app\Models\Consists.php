<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Consists extends Model
{
    use HasFactory;
           protected $table = 'consists';
      protected $fillable = [
        'Code',
        'Date',
        'Darw',
        'Products_Number',
        'Total_Qty',
        'Total_Price',
        'Account',
        'Store',
        'Coin',
        'User',
                       'Time',
        'Branch',

      
    ];
    
           public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    

    
              public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
}

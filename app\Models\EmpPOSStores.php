<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpPOSStores extends Model
{
    use HasFactory;
        protected $table = 'emp_p_o_s_stores';
      protected $fillable = [
        'Store',
        'Emp',



     
    ];


           public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }

           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InsurancePaper extends Model
{
    use HasFactory;
        protected $table = 'insurance_papers';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'From',
        'To',   
        'FromEn',
        'ToEn',
        'Note',
        'Due_Date',
        'Amount',
        'Status',
        'Coin',
        'Cost_Center',
        'Account',
        'Bank',
        'User',
          'File',

    ];
    
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
   
            public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
            public function Bank()
    {
        return $this->belongsTo(AcccountingManual::class,'Bank');
    }
    
   
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

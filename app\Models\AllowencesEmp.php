<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AllowencesEmp extends Model
{
    use HasFactory;
          protected $table = 'allowences_emps';
    protected $fillable = [
        'Emp',
        'Allow',
        'AmountAllow',

   
    ];
   
           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
    
             public function Allow()
    {
        return $this->belongsTo(BeneftisTypes::class,'Allow');
    }
}

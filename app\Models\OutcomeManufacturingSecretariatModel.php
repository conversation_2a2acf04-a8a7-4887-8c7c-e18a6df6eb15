<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutcomeManufacturingSecretariatModel extends Model
{
    use HasFactory;
       protected $table = 'outcome_manufacturing_secretariat_models';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'Qty',
        'SmallQty',
        'SmallCode',  
        'Store',
        'Product',
        'Unit',
        'Model',
        'Cost',
        'Workmanship_Price',
    
    
    ];

         public function Store()
    {
        return $this->belongsTo(SecretariatStores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Model()
    {
        return $this->belongsTo(ManufacturingSecretariatModel::class,'Model');
    }
}

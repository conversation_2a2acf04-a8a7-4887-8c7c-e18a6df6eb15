<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubImages extends Model
{
    use HasFactory;
           protected $table = 'sub_images';
      protected $fillable = [

                'Image',
                'Product',
 
          
    ];
    
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    


    
    
}

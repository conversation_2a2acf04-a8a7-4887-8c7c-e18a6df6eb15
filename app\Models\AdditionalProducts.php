<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdditionalProducts extends Model
{
    use HasFactory;
    
         protected $table = 'additional_products';
      protected $fillable = [


                'Additional_Product',
                'Product',
 

    ];
    
        public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }     
    
    public function Additional_Product()
    {
        return $this->belongsTo(Products::class,'Additional_Product');
    }
}

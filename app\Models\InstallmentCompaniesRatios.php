<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstallmentCompaniesRatios extends Model
{
    use HasFactory;
              protected $table = 'installment_companies_ratios';
      protected $fillable = [
        'From',
        'To',
        'Service_Fee',
        'Company_Precent',
        'Company',

    
    ];

         public function Company()
    {
        return $this->belongsTo(InstallmentCompanies::class,'Company');
    }
}

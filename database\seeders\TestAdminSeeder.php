<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Admin;

class TestAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Admin::create([
            'email' => '<EMAIL>',
            'name' => 'Test Admin',
            'password' => bcrypt('password'),
            'phone' => '**********',
            'hidden' => '1',
            'emp' => '0',
            'ship' => '0',
            'vend' => '0',
            'status' => '0',
            'cli' => 0,
            'account' => 0,
            'type' => 'Admin',
            'roles_name' => 'Owner',
            'guest' => '0',
            'pos_product' => '0',
            'Cash' => '0',
            'Delivery' => '0',
            'Cash_Collection' => '0',
            'Cash_Visa' => '0',
            'Installment' => '0',
            'Check' => '0',
            'Later' => '0',
            'InstallmentCompanies' => '0',
        ]);
    }
}

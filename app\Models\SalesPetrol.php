<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesPetrol extends Model
{
    use HasFactory;
       protected $table = 'sales_petrols';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Note',
        'Counter_Name',
        'Pervious_Read',
        'Petrol_Name',
        'Current_Raed',
        'Value',
        'Total',
        'Consumption',
        'Cash',
        'Calibers',
        'Counter',
        'Coin',
        'Safe',
        'Store',
        'Recipient',
        'User',
      
       
    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Counter()
    {
        return $this->belongsTo(CountersType::class,'Counter');
    }
          public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }

          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }


}

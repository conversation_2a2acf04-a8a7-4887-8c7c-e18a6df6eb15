<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OverTimes extends Model
{
    use HasFactory;
        protected $table = 'over_times';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Hour',
     ];
    
    
                    public function RegOverTime()
    {
        return $this->hasOne(RegOverTime::class);
    }
    
}

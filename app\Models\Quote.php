<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quote extends Model
{
    use HasFactory;
    protected $table = 'quotes';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'presenter',
        'annual_interest',
        'monthly_installment',
        'Years_Number',
        'total',
        'installment_Num',
        'Date_First_installment',
        'Later_Due',
        'Sale_Date',
        'Total_Wight_Bill',

    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
              public function ProductsQuote()
    {
        return $this->hasOne(ProductsQuote::class);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quality extends Model
{
    use HasFactory;
    
          protected $table = 'qualities';
      protected $fillable = [
       'Code',
       'Execution_Code',
       'Date',  
       'Outcome_Name',
       'Outcome_Code',
       'Outcome_Qty',
       'Except_Qty',
       'Outcome_Unit',
       'Outcome_Store',
       'Production_Manager',
       'Quality_Manager',
       'ManuExecution',
       'Note',
       'Status',
       'User',
  
                    
    ];
    

    
          public function ManuExecution()
    {
        return $this->belongsTo(ManufacturingExecution::class,'ManuExecution');
    }
    
              public function Production_Manager()
    {
        return $this->belongsTo(Employess::class,'Production_Manager');
    }
    
    
        public function Quality_Manager()
    {
        return $this->belongsTo(Employess::class,'Quality_Manager');
    }
    
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

             public function Outcome_Store()
    {
        return $this->belongsTo(Stores::class,'Outcome_Store');
    }
         public function Outcome_Unit()
    {
        return $this->belongsTo(Measuerments::class,'Outcome_Unit');
    }
    
    



}

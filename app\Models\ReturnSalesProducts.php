<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnSalesProducts extends Model
{
    use HasFactory;
      protected $table = 'return_sales_products';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'Recived_Qty',
        'AvQty',
        'Qty',
        'Price',
        'Discount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
        'Product',
        'Exp_Date',
        'V1',
        'V2',
        'Unit',
        'Return',
        'Date',
        'Code',
        'Refernce_Number',
        'Safe',
        'Client',
        'Executor',
        'Delegate',
        'Coin',
        'User',
        'Cost_Center',
        'Type',    
        'Ship',    
        'Patch_Number',    
        'CoinCode',   
        'CoinPrice',   
        'CoinRate',   
        'AmountEGP',   
        'DiscountAmount',   
        'TaxType',   
        'TaxAmount',   
        'TaxSubType',   
        'TaxRate',   
        'TotalBill',   
        'SalesTotal',   
        'NetTotal',   

    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
            public function Return()
    {
        return $this->belongsTo(ReturnSales::class,'Return');
    }
    
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
           public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
  
                      public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
   
    }
    
                          public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
    
    
    
}

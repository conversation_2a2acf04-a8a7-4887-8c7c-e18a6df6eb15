<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllVendors implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {
   
        
        
           if(app()->getLocale() == 'ar' ){  
           $prods = DB::table('vendors')

               ->leftJoin('places', function ($join) {
    
            $join->on('vendors.Place', '=', 'places.id');
        })   
               
               ->leftJoin('governrates', function ($join) {
    
            $join->on('vendors.Governrate', '=', 'governrates.id');
        })   
               
               ->leftJoin('cities', function ($join) {
    
            $join->on('vendors.City', '=', 'cities.id');
        })  
               
               ->leftJoin('countris', function ($join) {
    
            $join->on('vendors.Nationality', '=', 'countris.id');
        })   
               
               ->leftJoin('items_groups', function ($join) {
    
            $join->on('vendors.Pro_Group', '=', 'items_groups.id');
        })  
               
               ->leftJoin('brands', function ($join) {
    
            $join->on('vendors.Brand', '=', 'brands.id');
        })
               
               
                 ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('vendors.Account', '=', 'acccounting_manuals.id');
        })
               
                 ->leftJoin('employesses', function ($join) {
    
            $join->on('vendors.Responsible', '=', 'employesses.id');
        })



->select('vendors.Code'
         ,'vendors.Name'
         ,'vendors.Phone'
         ,'vendors.Phone2'
         ,'vendors.Commercial_Register'
         ,'vendors.Price_Level'
        ,'acccounting_manuals.Name as Account'
        ,'governrates.Arabic_Name as Governrate'
 ,'cities.Arabic_Name as City'
 ,'places.Arabic_Name as Place'
         ,'vendors.Tax_Registration_Number'
         ,'vendors.Tax_activity_code'
          ,'countris.Arabic_Name as Nationality'
          ,'employesses.Name as Responsible'
          ,'items_groups.Name as Pro_Group'
          ,'brands.Name as Brand'

        )
                  ->get();
           }else{
               
               
                $prods = DB::table('vendors')

               ->leftJoin('places', function ($join) {
    
            $join->on('vendors.Place', '=', 'places.id');
        })   
               
               ->leftJoin('governrates', function ($join) {
    
            $join->on('vendors.Governrate', '=', 'governrates.id');
        })   
               
               ->leftJoin('cities', function ($join) {
    
            $join->on('vendors.City', '=', 'cities.id');
        })  
               
               ->leftJoin('countris', function ($join) {
    
            $join->on('vendors.Nationality', '=', 'countris.id');
        })   
               
               ->leftJoin('items_groups', function ($join) {
    
            $join->on('vendors.Pro_Group', '=', 'items_groups.id');
        })  
               
               ->leftJoin('brands', function ($join) {
    
            $join->on('vendors.Brand', '=', 'brands.id');
        })
               
               
                 ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('vendors.Account', '=', 'acccounting_manuals.id');
        })
               
                 ->leftJoin('employesses', function ($join) {
    
            $join->on('vendors.Responsible', '=', 'employesses.id');
        })



->select('vendors.Code'
         ,'vendors.NameEn'
         ,'vendors.Phone'
         ,'vendors.Phone2'
         ,'vendors.Commercial_Register'
         ,'vendors.Price_Level'
        ,'acccounting_manuals.NameEn as Account'
        ,'governrates.English_Name as Governrate'
 ,'cities.English_Name as City'
 ,'places.English_Name as Place'
         ,'vendors.Tax_Registration_Number'
         ,'vendors.Tax_activity_code'
          ,'countris.English_Name as Nationality'
          ,'employesses.NameEn as Responsible'
          ,'items_groups.NameEn as Pro_Group'
          ,'brands.NameEn as Brand'

        )
                  ->get();    
               
               
           }
      
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Name',
          'Phone',
          'Phone2',
          'Commercial_Register',
          'Price_Level',
          'Account',
          'Governrate',
          'City',
          'Place',
          'Tax_Registration_Number',
          'Tax_activity_code',
          'Nationality',
          'Responsible',
          'Pro_Group',
          'Brand',
    
        
        ];
    }
    
    
    

}

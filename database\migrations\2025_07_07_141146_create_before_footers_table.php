<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBeforeFootersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('before_footers', function (Blueprint $table) {
            $table->id();
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->text('English_Desc')->nullable();
            $table->string('Image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('before_footers');
    }
}

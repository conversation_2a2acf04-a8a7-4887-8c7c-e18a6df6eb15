<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FaultsType extends Model
{
    use HasFactory;
        protected $table = 'faults_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Note',
        'Amount',
            
    ];
    
          public function ReciptMaintainceErrors()
    {
        return $this->hasOne(ReciptMaintainceErrors::class);
    }
    
    
    
}

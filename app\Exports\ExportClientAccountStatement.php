<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportClientAccountStatement implements FromCollection ,WithHeadings 
{
 
    
     private $from=[] ;

    public function __construct($from=0) 
    {
        $this->from = $from;

       
    }

    
  
    public function collection()
    {
   
//lw 3ayz mytkrrsh el qyod  ->distinct(['general_dailies.Code'])
        
        $storex=$this->from;

         $from=$storex['from'];
         $to=$storex['to'];
         $cost_Center=$storex['cost_Center'];
         $coin=$storex['coin'];
         $client=$storex['client'];
         $payment_Method=$storex['payment_Method'];
         $types=$storex['types'];


           if(app()->getLocale() == 'ar' ){ 
        if($payment_Method != null){
             $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المبيعات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('sales', function ($join) {
                  $storex=$this->from;
     $payment_Method=$storex['payment_Method'];
            $join->on('general_dailies.Code_Type', '=', 'sales.Code')
                ->where('sales.Payment_Method','=',$payment_Method);
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
       
        })    
              
              ->leftJoin('employesses as Emp', function ($join) {
    
            $join->on('sales.Executor', '=', 'employesses.id');
       
        })  
              
  
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('sales.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'sales.Time'
         ,'general_dailies.Code_Type'
         ,'sales.Refernce_Number'
         ,'branches.Arabic_Name as BranchName'
         ,'acccounting_manuals.Name as AccountName'
         ,'stores.Name as StoreName'
         ,'safes_banks.Name as SafeName'
         ,'general_dailies.Type'
           ,'sales.Total_Price'
           ,'sales.Total_Discount'
           ,'sales.Total_Taxes'
           ,'sales.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'sales.Due_Date'
         ,'coins.Arabic_Name as CoinName'
          ,'sales.Shift_Code'
          ,'employesses.Name as DelegateName'
          ,'Emp.Name as ExecuteName'
          ,'admins.name as UserName'
           ,'shipping_companies.Name as ShippingName'
           ,'cost_centers.Arabic_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();
        }else{
          $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
  ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المبيعات'])    
              
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('sales', function ($join) {
    
            $join->on('general_dailies.Code_Type', '=', 'sales.Code');
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
       
        })    
              
              ->leftJoin('employesses as Emp', function ($join) {
    
            $join->on('sales.Executor', '=', 'employesses.id');
       
        })  
              
  
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('sales.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

->select('general_dailies.Date'
         ,'sales.Time'
         ,'general_dailies.Code_Type'
         ,'sales.Refernce_Number'
         ,'branches.Arabic_Name as BranchName'
         ,'acccounting_manuals.Name as AccountName'
         ,'stores.Name as StoreName'
         ,'safes_banks.Name as SafeName'
         ,'general_dailies.Type'
           ,'sales.Total_Price'
           ,'sales.Total_Discount'
           ,'sales.Total_Taxes'
           ,'sales.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'sales.Due_Date'
         ,'coins.Arabic_Name as CoinName'
          ,'sales.Shift_Code'
          ,'employesses.Name as DelegateName'
          ,'Emp.Name as ExecuteName'
          ,'admins.name as UserName'
           ,'shipping_companies.Name as ShippingName'
           ,'cost_centers.Arabic_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();

        }
           }else{
               
          if($payment_Method != null){
             $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
   ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المبيعات'])    
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('sales', function ($join) {
                  $storex=$this->from;
     $payment_Method=$storex['payment_Method'];
            $join->on('general_dailies.Code_Type', '=', 'sales.Code')
                ->where('sales.Payment_Method','=',$payment_Method);
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
       
        })    
              
              ->leftJoin('employesses as Emp', function ($join) {
    
            $join->on('sales.Executor', '=', 'employesses.id');
       
        })  
              
  
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('sales.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

  
->select('general_dailies.Date'
         ,'sales.Time'
         ,'general_dailies.Code_Type'
         ,'sales.Refernce_Number'
         ,'branches.English_Name as BranchName'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'stores.NameEn as StoreName'
         ,'safes_banks.NameEn as SafeName'
         ,'general_dailies.Type'
           ,'sales.Total_Price'
           ,'sales.Total_Discount'
           ,'sales.Total_Taxes'
           ,'sales.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'sales.Due_Date'
         ,'coins.English_Name as CoinName'
          ,'sales.Shift_Code'
          ,'employesses.NameEn as DelegateName'
          ,'Emp.NamEne as ExecuteName'
          ,'admins.nameEn as UserName'
           ,'shipping_companies.NameEn as ShippingName'
           ,'cost_centers.English_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();
        }else{
          $prods = DB::table('general_dailies')->whereBetween('general_dailies.Date',[$from,$to])
       
  ->whereIn('general_dailies.Type', ['سند صرف','سند قبض','المبيعات'])    
              
            ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('general_dailies.Cost_Center', $cost_Center);
    })          
        
          
          ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('general_dailies.Coin', $coin);
    })  

                  ->when(!empty($client), function ($query) use ($client) {
        return $query->where('general_dailies.Account', $client);
    })        
              
              
              
            ->leftJoin('sales', function ($join) {
    
            $join->on('general_dailies.Code_Type', '=', 'sales.Code');
        })

              

                ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('general_dailies.Account', '=', 'acccounting_manuals.id');
        })
      
          ->leftJoin('stores', function ($join) {
    
            $join->on('sales.Store', '=', 'stores.id');
        })
              
         ->leftJoin('safes_banks', function ($join) {
    
            $join->on('sales.Safe', '=', 'safes_banks.Account');
        })      
              
                ->leftJoin('coins', function ($join) {
    
            $join->on('general_dailies.Coin', '=', 'coins.id');
        })         
              
              ->leftJoin('employesses', function ($join) {
    
            $join->on('sales.Delegate', '=', 'employesses.id');
       
        })    
              
              ->leftJoin('employesses as Emp', function ($join) {
    
            $join->on('sales.Executor', '=', 'employesses.id');
       
        })  
              
  
              
                 ->leftJoin('admins', function ($join) {
    
            $join->on('general_dailies.userr', '=', 'admins.id');
        })
              
                   ->leftJoin('cost_centers', function ($join) {
    
            $join->on('general_dailies.Cost_Center', '=', 'cost_centers.id');
        })
              
                   ->leftJoin('shipping_companies', function ($join) {
    
            $join->on('sales.Ship', '=', 'shipping_companies.id');
        })
              
              ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })  

->select('general_dailies.Date'
         ,'sales.Time'
         ,'general_dailies.Code_Type'
         ,'sales.Refernce_Number'
         ,'branches.English_Name as BranchName'
         ,'acccounting_manuals.NameEn as AccountName'
         ,'stores.NameEn as StoreName'
         ,'safes_banks.NameEn as SafeName'
         ,'general_dailies.Type'
           ,'sales.Total_Price'
           ,'sales.Total_Discount'
           ,'sales.Total_Taxes'
           ,'sales.Pay'
         ,'general_dailies.Debitor_Coin'
         ,'general_dailies.Creditor_Coin'
         ,'sales.Due_Date'
         ,'coins.English_Name as CoinName'
          ,'sales.Shift_Code'
          ,'employesses.NameEn as DelegateName'
          ,'Emp.NameEn as ExecuteName'
          ,'admins.nameEn as UserName'
           ,'shipping_companies.NameEn as ShippingName'
           ,'cost_centers.English_Name as CostCenterName'
           ,'general_dailies.Statement'


        )
                  ->get();

        }       
               
               
           }
               
               
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Time',
          'Code',
          'Refernce_Number',
          'Branch',
          'Client',
          'Store',
          'Safe',
          'Type',
          'Total_Price',
          'Total_Discount',
          'Total_Tax',
          'Pay',
          'Total_Creditor',
          'Total_Debitor',
          'Due_Date',
          'Coin',
          'Shift_Code',
          'Delegate',
          'Executor',
          'User',
          'Shipping_Compaines',
          'Cost_Center',
          'Note'
        
        ];
    }
    
    
    

}

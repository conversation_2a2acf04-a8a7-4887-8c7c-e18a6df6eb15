<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
class Admin
{    
     public function handle($request, Closure  $next=null,$guard=null)
    {
         if(Auth::guard($guard)->check()){  
             
               return $next($request);
               return redirect('OstAdmin');
        
                    }else{

             return redirect('AdminLogin');
                      }

      
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomChecks extends Model
{
    use HasFactory;
          protected $table = 'incom_checks';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Note',
        'Check_Num',
        'Due_Date',
        'Amount',
        'Status',
        'Reason',
        'Check_Type',
        'Coin',
        'Cost_Center',
        'Account',
        'Bank',
        'Arrest_Account',
        'Bene_Account',
        'User',
          'File',
          'Image',
          'Signture_Name',
          'Bank_Branch',

       
    ];
    
        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
         public function Check_Type()
    {
        return $this->belongsTo(ChecksTypes::class,'Check_Type');
    }
    
            public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
            public function Bank()
    {
        return $this->belongsTo(AcccountingManual::class,'Bank');
    }
    
            public function Arrest_Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Arrest_Account');
    }
    
            public function Bene_Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Bene_Account');
    }
    
            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
    
}

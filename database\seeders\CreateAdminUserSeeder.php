<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Admin;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CreateAdminUserSeeder extends Seeder
{
  
    public function run()
    {
          $user = Admin::find(1);
  

        $role = Role::create(['name' => "owner"]);
   
        $permissions = Permission::pluck('id','id')->all();
  
        $role->syncPermissions($permissions);

        $user->assignRole($role->name);
        
        
        
          $user = Admin::find(11);
  

        $role = Role::create(['name' => "owner"]);
   
        $permissions = Permission::pluck('id','id')->all();
  
        $role->syncPermissions($permissions);

        $user->assignRole($role->name);
        
    }
}





<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partners extends Model
{
    use HasFactory;
      protected $table = 'partners';
      protected $fillable = [
        'Name',
        'Shares_Number',
        'Nominal_Value_of_Shares',
        'Actual_Share_Value',
        'Profits_Precentage',
        'Profits',
        'Withdraw_Profits',
        'Remaining_Profits',
        'Account',
    ];
    
                 public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DevicesTypesy extends Model
{
    use HasFactory;
    
      protected $table = 'devices_typesies';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Company',
            
    ];
    
    public function Company()
    {
        return $this->belongsTo(ManufactureCompany::class,'Company');
    }
    
            public function ReciptMaintaince()
    {
        return $this->hasOne(ReciptMaintaince::class);
    }
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Holidays extends Model
{
    use HasFactory;
      protected $table = 'holidays';
      protected $fillable = [
        'Code',
        'Date',
        'Month',
        'Num_of_Days',
        'Start_Date',
        'Discount',
        'Status',
        'Note',
        'Emp',
        'Type',
        'User',
    ];
    
    
       public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
        public function Type()
    {
        return $this->belongsTo(HolidaysTypes::class,'Type');
    }
    
        public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
    


}

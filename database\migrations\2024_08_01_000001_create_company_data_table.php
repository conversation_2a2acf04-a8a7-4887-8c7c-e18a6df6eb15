<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompanyDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_data', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('Name')->nullable();
            $table->string('Phone1')->nullable();
            $table->string('Phone2')->nullable();
            $table->text('Address')->nullable();
            $table->string('Commercial_Record')->nullable();
            $table->string('Tax_File_Number')->nullable();
            $table->text('Logo')->nullable();
            $table->text('Icon')->nullable();
            $table->text('Print_Text')->nullable();
            $table->timestamps();
            $table->text('Print_Text_Footer')->nullable();
            $table->text('Seal')->nullable();
            $table->text('Print_Text_Footer_Sales')->nullable();
            $table->text('Name_Sales_Bill')->nullable();
            $table->text('Name_Sales_Order_Bill')->nullable();
            $table->text('Print_Text_Footer_Quote')->nullable();
            $table->text('Name_Quote_Bill')->nullable();
            $table->text('Print_Text_Footer_Secretariat')->nullable();
            $table->text('Logo_Store')->nullable();
            $table->string('Phone3')->nullable();
            $table->string('Phone4')->nullable();
            $table->text('Icon_Store')->nullable();
            $table->string('View')->nullable();
            $table->string('Tax_Registration_Number')->nullable();
            $table->string('Tax_activity_code')->nullable();
            $table->string('work_nature')->nullable();
            $table->string('Governrate')->nullable();
            $table->string('City')->nullable();
            $table->string('Place')->nullable();
            $table->string('Nationality')->nullable();
            $table->string('Buliding_Num')->nullable();
            $table->string('Street')->nullable();
            $table->string('Postal_Code')->nullable();
            $table->string('tax_magistrate')->nullable();
            $table->string('Client_ID')->nullable();
            $table->string('Serial_Client_ID')->nullable();
            $table->string('Version_Type')->nullable();
            $table->string('Computer_SN')->nullable();
            $table->string('Invoice_Type')->nullable();
            $table->string('Floor')->nullable();
            $table->string('Room')->nullable();
            $table->string('Landmark')->nullable();
            $table->text('Add_Info')->nullable();
            $table->text('Print_Text_Footer_Manufacturing')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_data');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Places extends Model
{
    use HasFactory;
       protected $table = 'places';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Ship_Price',
        'City',
        'Delivery',
        'SearchCode',
          
   
    ];
    
               public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
                   public function Delivery()
    {
        return $this->belongsTo(Employess::class,'Delivery');
    }
    
   
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;

class StratPeriod implements ToCollection, WithChunkReading , WithBatchInserts
{

    public function collection(Collection $collection)
    {
        
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
            DB::table('start_periods')->insert([

            'Code'	   =>$value[1]
            ,'Date'  =>$value[2]
            ,'Draw'  =>$value[3]
            ,'Note'  =>$value[4]
            ,'Total_Products'  =>$value[5]
            ,'Total_Qty'  =>$value[6]
            ,'Total_Price'  =>$value[7]
            ,'Store'  =>$value[8]    
            ,'Coin'  =>$value[9]
            ,'User'  =>$value[10]
            ,'created_at'  =>$value[11]
            ,'updated_at'  =>$value[12]    

          

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

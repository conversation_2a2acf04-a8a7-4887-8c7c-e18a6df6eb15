<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StoresDefaultData;
use App\Models\WorkDepartments;
use App\Models\ModuleSettingsNum;
use App\Models\OutcomManufacturingModel;
use App\Models\JobsTypes;
use App\Models\DeducationsTypes;
use App\Models\BeneftisTypes;
use App\Models\UsersMoves;
use App\Models\OverTimes;
use App\Models\HolidaysTypes;
use App\Models\Employess;
use App\Models\AcccountingManual;
use App\Models\Admin;
use App\Models\LoanTypes;
use App\Models\Borrowa;
use App\Models\CostCenter;
use App\Models\ProductUnits;
use App\Models\Coins;
use App\Models\JournalizingDetails;
use App\Models\Journalizing;
use App\Models\GeneralDaily;
use App\Models\Entitlement;
use App\Models\Deduction;
use App\Models\Holidays;
use App\Models\Attendance;
use App\Models\Departure;
use App\Models\AttendanceEmp;
use App\Models\DepartureEmp;
use App\Models\RegOverTime;
use App\Models\Settlement;
use App\Models\Loan;
use App\Models\PaySalary;
use App\Models\Stores;
use App\Models\EmpInstallment;
use App\Models\EmpInstallmentDetails;
use App\Models\Sales;
use App\Models\EmpRatio;
use App\Models\ExchangeCommissions;
use App\Models\ReturnMaintainceBill;
use App\Models\ProductSales;
use App\Models\ProductsPurchases;
use App\Models\ReciptMaintaince;
use App\Models\ProductMaintaincBill;
use App\Models\ProductsStartPeriods;
use App\Models\ProductsStoresTransfers;
use App\Models\CrmDefaultData;
use App\Models\PurchasesDefaultData;
use App\Models\SalesDefaultData;
use App\Models\MaintainceDefaultData;
use App\Models\ReciptVoucher;
use App\Models\ReciptVoucherDetails;
use App\Models\EmpPOSStores;
use App\Models\Employment_levels;
use App\Models\Insurance_companies;
use App\Models\Branches;
use App\Models\Countris;
use App\Models\Disclaimer;
use App\Models\ResignationRequest;
use App\Models\AllowencesEmp;
use App\Models\DiscountsEmp;
use App\Models\EmpsProducationQuantity;
use App\Models\EmpsProducationPoint;
use App\Models\AttendencePolicyEmp;
use App\Models\DepaarturePolicyEmp;
use App\Models\ShippingList;
use App\Models\ItemsGroups;
use App\Models\Event;
use App\Models\Notifications;
use App\Models\AccountsDefaultData;
use App\Models\Consists;
use App\Models\Purchases;
use DB;
use Str;
use DateTime;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class HRController extends Controller
{
    
function __construct()
{

$this->middleware('permission:اقسام العمل', ['only' => ['WorkDepartmentsPage','AddWorkDepartments'],'EditWorkDepartments','DeleteWorkDepartments']);
$this->middleware('permission:انواع الوظائف', ['only' => ['Jobs_TypePage','AddJobs_Type','EditJobs_Type','DeleteJobs_Type']]);
$this->middleware('permission:اضافه موظف', ['only' => ['AddEmpPage','PostAddEmp']]);
$this->middleware('permission:جدول الموظفين', ['only' => ['EmpSechdulePage','DeleteEmp','EditEmp','PostEditEmp']]);
$this->middleware('permission:انواع الاجازات', ['only' => ['Holidays_TypesPage','AddHolidays_Types','EditHolidays_Types','DeleteHolidays_Types']]);
$this->middleware('permission:انواع الاستحقاقات', ['only' => ['Benefits_TypesPage','AddBenefits_Types','EditBenefits_Types','DeleteBenefits_Types']]);
$this->middleware('permission:انواع الاستقطاعات', ['only' => ['Deductions_TypesPage','AddDeductions_Types','EditDeductions_Types','DeleteDeductions_Types']]);
$this->middleware('permission:الساعات الاضافيه', ['only' => ['OvertimePage','AddOvertime','EditOvertime','DeleteOvertime']]);
$this->middleware('permission:انواع القروض', ['only' => ['Loan_TypesPage','AddLoan_Types','EditLoan_Types','DeleteLoan_Types']]);
$this->middleware('permission:اضافه سلفه', ['only' => ['AddBorrowPage','PostAddBorrow']]);
$this->middleware('permission:السلف', ['only' => ['BorrowPage','DeleteBorrow']]);
$this->middleware('permission:طلب اجازه', ['only' => ['HolidaysOrderPage','AddHolidaysOrder','EditHolidaysOrder','DeleteHolidaysOrder','TransToHoilday']]);
$this->middleware('permission:الاجازات', ['only' => ['HolidaysPage','AddHolidays','EditHolidays','DeleteHolidays']]);
$this->middleware('permission:الاستحقاقات', ['only' => ['EntitlementsPage','AddEntitlements','EditEntitlements','DeleteEntitlements']]);
$this->middleware('permission:الاستقطاعات', ['only' => ['DeducationPage','AddDeducation','EditDeducation','DeleteDeducation']]);
$this->middleware('permission: الحضور', ['only' => ['AttendancePage','AddAttendance']]);
$this->middleware('permission:جدول الحضور', ['only' => ['AttendanceSechdulePage','DeleteAttendance','EditAttendancePage','PostEditAttendance']]);
$this->middleware('permission:انصراف', ['only' => ['DeparturePage','AddDeparture']]);
$this->middleware('permission:جدول الانصراف', ['only' => ['DepartureSechdulePage','EditDeparturePage','PostEditDeparture']]);
$this->middleware('permission:تسجيل الساعات الاضافيه', ['only' => ['RegOverTimePage','AddRegOverTime','EditRegOverTime','DeleteRegOverTime']]);
$this->middleware('permission:اضافه قرض', ['only' => ['AddLoanPage','PostAddLoan']]);
$this->middleware('permission:القروض', ['only' => ['LoanPage','DeleteLoan']]);
$this->middleware('permission:اقساط الموظفين', ['only' => ['EmpInstallmentPage','EmpInstallBillDone','EmpUnInstallBill','EmpInstallDone','EmpUnInstall']]);
$this->middleware('permission:صرف راتب', ['only' => ['AddSalaryPage','PostAddSalary']]);
$this->middleware('permission:جدول الرواتب', ['only' => ['SalarySechdulesPage','DeletePaySalary']]);
$this->middleware('permission:صرف عمولات', ['only' => ['ExchangeCommissionsPage','PostExchangeCommissions']]);
$this->middleware('permission:جدول صرف العمولات', ['only' => ['ExchangeCommissionsSechdule']]);       
$this->middleware('permission:اهدافي', ['only' => ['MyGoals']]);       
$this->middleware('permission:طلب استقاله', ['only' => ['ResignationRequest']]);       
$this->middleware('permission:طلبات استقاله', ['only' => ['ResignationRequestSechdule']]);       
$this->middleware('permission:اخلاء طرف', ['only' => ['Disclaimer']]);       
    
    
    
}
    
    
               //Mtwst Sa3r Sharaa 
   
          private function AverageCostGet($product,$code,$store){
      
           
  
              
                                $def=StoresDefaultData::orderBy('id','desc')->first();
           
           
           if($def->Cost_Price == 1){
                        $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
           
           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;  
               }else{
            $AVERAGE=1;       
               }
           }
               
   }elseif($def->Cost_Price == 0){
               
     
                 
          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               
                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{
                       
                       
                       if(!empty($PROOStart)){
                           
                             $AVERAGE = $PROOStart->Price ; 
                       }else{
                          $AVERAGE = $rr->Price ;    
                       }
                       
                        
                   }
           
           
               
               
           }elseif($def->Cost_Price == 2){
               
                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

                      $fifo =FifoQty::orderBy('id','asc') 
                 ->where('Store',$store)    
              ->where('Product',$product)  
    ->where('P_Code',$code)   
                ->where('Qty','!=',0)    
                ->first();      
               
           
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')  
                  ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
                   ->where('Qty','!=',0)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
                  ->where('Qty','!=',0)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
      ->where('Qty','!=',0)    
                ->first(); 

}

}

}
    
               
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
           
             
               
           }


           
           return number_format((float)$AVERAGE, 2, '.', '') ;
    }

    
    
          private function AverageCostGetT($product,$code){
      
           
  
              
                                $def=StoresDefaultData::orderBy('id','desc')->first();
           
           
           if($def->Cost_Price == 1){
                        $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
           
           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;  
               }else{
            $AVERAGE=1;       
               }
           }
               
   }elseif($def->Cost_Price == 0){
               
     
                 
          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 
               
                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{
                       
                       
                       if(!empty($PROOStart)){
                           
                             $AVERAGE = $PROOStart->Price ; 
                       }else{
                          $AVERAGE = $rr->Price ;    
                       }
                       
                        
                   }
           
           
               
               
           }elseif($def->Cost_Price == 2){
               
                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

                    $fifo =FifoQty::orderBy('id','asc') 
                
              ->where('Product',$product)  
    ->where('P_Code',$code)   
                ->where('Qty','!=',0)    
                ->first();      
               
           
            
                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')  
           
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
                   ->where('Qty','!=',0)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
            
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
                  ->where('Qty','!=',0)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
         
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
      ->where('Qty','!=',0)    
                ->first(); 

}

}

}
    
               
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
           
             
               
           }


           
           return number_format((float)$AVERAGE, 2, '.', '') ;
    }

     

    private function TestCost($store,$product,$code,$id,$Purchases_Date){
        
        
                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first(); 

            $fifo =FifoQty::
                where('Store',$store)    
                ->where('Product',$product)    
                ->where('P_Code',$code)    
                ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first();      
            
                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)    
                ->where('Product',$product)    
                ->where('PP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPP_Code',$code)    
             ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)    
                ->where('Product',$product)    
                ->where('PPPP_Code',$code)    
               ->where('id','!=',$id)    
                     ->where('Purchases_Date','>',$Purchases_Date)    
                ->first(); 

}

}

}
    
               if(!empty($fifo)){
                   
                   if($fifo->Qty == 0){
                       
               
                       
         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);           
                       
        $AVERAGE = $ty ;         
                       
                   }else{
                  $AVERAGE = $fifo->Cost_Price ;      
                   }
                   
                       
               }else{
                   
                 $AVERAGE = $rr->Price ;    
                   
               }
           
        return $AVERAGE ;
        
        
    }


    
       //======  WorkDepartments ======= 
        public function WorkDepartmentsPage(){
        $itemss=WorkDepartments::all();
       
        $Presdient=WorkDepartments::where('Parent',0)->orderBy('id','desc')->first();
            
         $items=WorkDepartments::where('Parent',$Presdient->id)->get();
            
        $Employess=Employess::where("EmpSort",1)->get();
        $Emp=Employess::where('Department',$Presdient->id)->orderBy('id','desc')->first();
         return view('admin.HR.WorkDepartments',['items'=>$items,'Employess'=>$Employess,'Presdient'=>$Presdient,'Emp'=>$Emp,'itemss'=>$itemss]);
    }
    
     public function AddWorkDepartments(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Parent'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Parent']=request('Parent');
         $data['Budget']=request('Budget');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         WorkDepartments::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='اقسام العمل';
           $dataUser['ScreenEn']='Work Departments';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditWorkDepartments(){ 
             
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         $id=request('ID');
  
           
         if(!empty(request('Parent'))){
             $data['Parent']=request('Parent');
         }else{
            $data['Parent']=request('ParentCurrent'); 
         }
                  
         
         
         $data['Budget']=request('Budget');
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           WorkDepartments::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='اقسام العمل';
           $dataUser['ScreenEn']='Work Departments';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteWorkDepartments(){
              $id=request('ID');         
        $del=WorkDepartments::find($id);
        
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='اقسام العمل';
           $dataUser['ScreenEn']='Work Departments';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
       public function EditDepartment(Request $request){

$states=[];
  $ID = $request->get('countryId'); 
         $item=WorkDepartments::find($ID);
           
              if(app()->getLocale() == 'ar' ){ 
         $states  +=['Ar'=>$item->Arabic_Name,'En'=>$item->English_Name,'parent'=>$item->Parent,'d'=>$item->id,'parentName'=>$item->Parent()->first()->Arabic_Name,'budget'=>$item->Budget];
              }else{
         $states  +=['Ar'=>$item->Arabic_Name,'En'=>$item->English_Name,'parent'=>$item->Parent,'d'=>$item->id,'parentName'=>$item->Parent()->first()->English_Name,'budget'=>$item->Budget];                
                  
              }

           return response()->json($states);
        
    }
    
    
    //Employment_levels
          public function Employment_levels(){
        $items=Employment_levels::all();
         return view('admin.HR.Employment_levels',['items'=>$items]);
    }
    
     public function AddEmployment_levels(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Employment_levels::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='مستويات التوظيف';
           $dataUser['ScreenEn']='Employment levels';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditEmployment_levels($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Employment_levels::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='مستويات التوظيف';
           $dataUser['ScreenEn']='Employment levels';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteEmployment_levels($id){
               
        $del=Employment_levels::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='مستويات التوظيف';
           $dataUser['ScreenEn']='Employment levels';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
//Insurance_companies
              public function Insurance_companies(){
        $items=Insurance_companies::all();
         return view('admin.HR.Insurance_companies',['items'=>$items]);
    }
    
     public function AddInsurance_companies(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');

         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         Insurance_companies::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                       $dataUser['Screen']='شركات التأمين';
           $dataUser['ScreenEn']='Insurance companies';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditInsurance_companies($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');

         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           Insurance_companies::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                       $dataUser['Screen']='شركات التأمين';
           $dataUser['ScreenEn']='Insurance companies';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteInsurance_companies($id){
               
        $del=Insurance_companies::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                              $dataUser['Screen']='شركات التأمين';
           $dataUser['ScreenEn']='Insurance companies';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

           //======  JobsTypes ======= 
        public function Jobs_TypePage(){
        $items=JobsTypes::all();
         return view('admin.HR.JobsTypes',['items'=>$items]);
    }
    
     public function AddJobs_Type(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         JobsTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

           $dataUser['Screen']='انواع الوظائف';
           $dataUser['ScreenEn']='Jobs Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditJobs_Type($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
       
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           JobsTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
    $dataUser['Screen']='انواع الوظائف';
           $dataUser['ScreenEn']='Jobs Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteJobs_Type($id){
                      
        $del=JobsTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
        $dataUser['Screen']='انواع الوظائف';
           $dataUser['ScreenEn']='Jobs Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
               //======  BeneftisTypes ======= 
        public function Benefits_TypesPage(){
        $items=BeneftisTypes::all();
         return view('admin.HR.BeneftisTypes',['items'=>$items]);
    }
    
     public function AddBenefits_Types(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         BeneftisTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

           $dataUser['Screen']='انواع الاستحقاقات';
           $dataUser['ScreenEn']='Benefits Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditBenefits_Types($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
          
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           BeneftisTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='انواع الاستحقاقات';
           $dataUser['ScreenEn']='Benefits Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteBenefits_Types($id){
                      
        $del=BeneftisTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='انواع الاستحقاقات';
           $dataUser['ScreenEn']='Benefits Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

              //======  Deductions_Types ======= 
        public function Deductions_TypesPage(){
        $items=DeducationsTypes::all();
         return view('admin.HR.DeducationsTypes',['items'=>$items]);
    }
    
     public function AddDeductions_Types(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         DeducationsTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='انواع الاستقطاعات';
           $dataUser['ScreenEn']='Deductions Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditDeductions_Types($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
       
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           DeducationsTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
          $dataUser['Screen']='انواع الاستقطاعات';
           $dataUser['ScreenEn']='Deductions Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteDeductions_Types($id){
                      
        $del=DeducationsTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='انواع الاستقطاعات';
           $dataUser['ScreenEn']='Deductions Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
                //======  HolidaysTypes ======= 
        public function Holidays_TypesPage(){
        $items=HolidaysTypes::all();
         return view('admin.HR.HolidaysTypes',['items'=>$items]);
    }
    
     public function AddHolidays_Types(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Days'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
            'Days.required' => trans('admin.DaysNumbersRequired'),    
     
         ]);
   
         
           
         $data['From_Date']=request('From_Date');
         $data['To_Date']=request('To_Date');
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Days']=request('Days');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         HolidaysTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

              $dataUser['Screen']='انواع الاجازات';
           $dataUser['ScreenEn']='Holidays Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditHolidays_Types($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            'Days'=>'required',
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
         'Days.required' => trans('admin.DaysNumbersRequired'),   
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
             $data['Days']=request('Days');
                $data['From_Date']=request('From_Date');
         $data['To_Date']=request('To_Date');
           HolidaysTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='انواع الاجازات';
           $dataUser['ScreenEn']='Holidays Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteHolidays_Types($id){
                      
        $del=HolidaysTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='انواع الاجازات';
           $dataUser['ScreenEn']='Holidays Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    

                    //======  OverTimes ======= 
        public function OvertimePage(){
        $items=OverTimes::all();
         return view('admin.HR.OverTimes',['items'=>$items]);
    }
    
     public function AddOvertime(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Hour'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
            'Hour.required' => trans('admin.CostHourRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         $data['Hour']=request('Hour');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         OverTimes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                $dataUser['Screen']='الساعات الاضافيه';
           $dataUser['ScreenEn']='Overtime';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditOvertime($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'Hour'=>'required',
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
          'Hour.required' => trans('admin.CostHourRequired'),  
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
            $data['Hour']=request('Hour');
           OverTimes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الساعات الاضافيه';
           $dataUser['ScreenEn']='Overtime';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteOvertime($id){
                      
        $del=OverTimes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='الساعات الاضافيه';
           $dataUser['ScreenEn']='Overtime';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
 //======  Employess ======= 
        public function AddEmpPage(){

         $Departments=WorkDepartments::all();
         $JobsTypes=JobsTypes::all();
         $Employment_levels=Employment_levels::all();
         $Insurance_companies=Insurance_companies::all();
         $Branches=Branches::all();
         $Countris=Countris::all();
         $Groups=ItemsGroups::all();

                  $wf=60;
 $Accounts=AcccountingManual::orderBy('Code','asc')->where('Code','like', $wf.'%')->where('Type',0)->get(); 
            
                 $res=Employess::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
          $Stores=Stores::all();
            
            $Aloownces=BeneftisTypes::all();
            $Discounts=DeducationsTypes::all();
         return view('admin.HR.Employee',[
             'Departments'=>$Departments,                                            
             'JobsTypes'=>$JobsTypes,                                            
             'Code'=>$Code,                                            
             'Groups'=>$Groups,                                            
             'Accounts'=>$Accounts,                                            
                'Stores'=>$Stores,                         
                'Countris'=>$Countris,                         
                'Branches'=>$Branches,                         
                'Employment_levels'=>$Employment_levels,                         
                'Insurance_companies'=>$Insurance_companies,                         
                'Aloownces'=>$Aloownces,                         
                'Discounts'=>$Discounts,                         
         
         ]);
    }
    
       public function EmpSechdulePage(){
        $items=Employess::where('EmpSort',1)
            ->where('id','!=',38)
            ->where('id','!=',39)
            ->where('id','!=',40)
            ->where('id','!=',41)
            ->where('id','!=',42)
            ->paginate(100);
         return view('admin.HR.EmployeesSechdule',['items'=>$items]);
    }

           public function JobRequestsSechdule(){
        $items=Employess::where('EmpSort',2)->get();
         return view('admin.HR.JobRequestsSechdule',['items'=>$items]);
    }

       public function PostAddEmp(){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Emp_Type'=>'required',
             'Salary'=>'required',
             'Job'=>'required',
             'Department'=>'required',
             'Account'=>'required',
              'email'=>'email|unique:admins',
     'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Name.required' => trans('admin.NameRequired'),      
            'Emp_Type.required' => trans('admin.Emp_TypeRequired'),      
            'Salary.required' => trans('admin.SalaryRequired'),      
            'Job.required' => trans('admin.JobRequired'),      
            'Department.required' => trans('admin.DepartmentRequired'),      
            'Account.required' => trans('admin.AccountRequired'),      
            'Account_Emp.required' => trans('admin.Account_EmpRequired'), 
              'email.unique' =>trans('admin.emailUnique'),
         ]);

            $count=AcccountingManual::orderBy('id','desc')->where('Parent',request('Account'))->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',request('Account'))->first();    
            $codee=AcccountingManual::find(request('Account'));   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                          $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $dataX['NameEn']=request('NameEn');
          }else{
               $dataX['NameEn']=request('Name'); 
              
          }

         $dataX['Type']=1;
         $dataX['Parent']=request('Account');
         $dataX['Note']=request('Note');
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 

         $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='EmployeesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=null;
             }

                    $image1=request()->file('CV');
          if($image1){            
            $image_name1=Str::random(20);
            $ext1=strtolower($image1->getClientOriginalExtension());
            $image_full_name1=$image_name1 .'.' . $ext1 ;
            $upload_path1='EmployeesImages/';
            $image_url1=$upload_path1.$image_full_name1;
            $success1=$image1->move($upload_path1,$image_full_name1);            
                   }
        
    
             if(!empty($image_url1)){
       
                 $data['CV']=$image_url1; 
                 
             }else{
                 $data['CV']=null;
             }
           
       
                        $image2=request()->file('ID_Image');
          if($image2){            
            $image_name2=Str::random(20);
            $ext2=strtolower($image2->getClientOriginalExtension());
            $image_full_name2=$image_name2 .'.' . $ext2 ;
            $upload_path2='EmployeesImages/';
            $image_url2=$upload_path2.$image_full_name2;
            $success2=$image2->move($upload_path2,$image_full_name2);            
                   }
        
    
             if(!empty($image_url2)){
       
                 $data['ID_Image']=$image_url2; 
                 
             }else{
                 $data['ID_Image']=null;
             }

     
                           $image3=request()->file('Criminal_status');
          if($image3){            
            $image_name3=Str::random(20);
            $ext3=strtolower($image3->getClientOriginalExtension());
            $image_full_name3=$image_name3 .'.' . $ext3 ;
            $upload_path3='EmployeesImages/';
            $image_url3=$upload_path3.$image_full_name3;
            $success3=$image3->move($upload_path3,$image_full_name3);            
                   }
        
    
             if(!empty($image_url3)){
       
                 $data['Criminal_status']=$image_url3; 
                 
             }else{
                 $data['Criminal_status']=null;
             }

    
                           $image4=request()->file('Contract');
          if($image4){            
            $image_name4=Str::random(20);
            $ext4=strtolower($image4->getClientOriginalExtension());
            $image_full_name4=$image_name4 .'.' . $ext4 ;
            $upload_path4='EmployeesImages/';
            $image_url4=$upload_path4.$image_full_name4;
            $success4=$image4->move($upload_path4,$image_full_name4);            
                   }
        
    
             if(!empty($image_url4)){
       
                 $data['Contract']=$image_url4; 
                 
             }else{
                 $data['Contract']=null;
             }

    
                           $image5=request()->file('health_certificate');
          if($image5){            
            $image_name5=Str::random(20);
            $ext5=strtolower($image5->getClientOriginalExtension());
            $image_full_name5=$image_name5 .'.' . $ext5 ;
            $upload_path5='EmployeesImages/';
            $image_url5=$upload_path5.$image_full_name5;
            $success5=$image5->move($upload_path5,$image_full_name5);            
                   }
        
    
             if(!empty($image_url5)){
       
                 $data['health_certificate']=$image_url5; 
                 
             }else{
                 $data['health_certificate']=null;
             }

 
                           $image6=request()->file('Search_Card');
          if($image6){            
            $image_name6=Str::random(20);
            $ext6=strtolower($image6->getClientOriginalExtension());
            $image_full_name6=$image_name6 .'.' . $ext6 ;
            $upload_path6='EmployeesImages/';
            $image_url6=$upload_path6.$image_full_name6;
            $success6=$image6->move($upload_path6,$image_full_name6);            
                   }
        
    
             if(!empty($image_url6)){
       
                 $data['Search_Card']=$image_url6; 
                 
             }else{
                 $data['Search_Card']=null;
             }

    
               
                           $image7=request()->file('Recruitment_certificate');
          if($image7){            
            $image_name7=Str::random(20);
            $ext7=strtolower($image7->getClientOriginalExtension());
            $image_full_name7=$image_name7 .'.' . $ext7 ;
            $upload_path7='EmployeesImages/';
            $image_url7=$upload_path7.$image_full_name7;
            $success7=$image7->move($upload_path7,$image_full_name7);            
                   }
        
    
             if(!empty($image_url7)){
       
                 $data['Recruitment_certificate']=$image_url7; 
                 
             }else{
                 $data['Recruitment_certificate']=null;
             }

      
                           $image8=request()->file('employee_profile');
          if($image8){            
            $image_name8=Str::random(20);
            $ext8=strtolower($image8->getClientOriginalExtension());
            $image_full_name8=$image_name8 .'.' . $ext8 ;
            $upload_path8='EmployeesImages/';
            $image_url8=$upload_path8.$image_full_name8;
            $success8=$image8->move($upload_path8,$image_full_name8);            
                   }
        
    
             if(!empty($image_url8)){
       
                 $data['employee_profile']=$image_url8; 
                 
             }else{
                 $data['employee_profile']=null;
             }


        $data['Code']=request('Code');
        $data['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name'); 
              
          }

        $data['Emp_Type']=request('Emp_Type');
        $data['Salary']=request('Salary');
        $data['Attendence']=request('Attendence');
        $data['Departure']=request('Departure');
        $data['Hours_Numbers']=request('Hours_Numbers');
        $data['Days_Numbers']=request('Days_Numbers');
        $data['Day_Price']=request('Day_Price');
        $data['Precentage_of_Sales']=request('Precentage_of_Sales');
        $data['Precentage_of_Profits']=request('Precentage_of_Profits');
        $data['Precentage_of_Execution']=request('Precentage_of_Execution');
        $data['Note']=request('Note');
        $data['Bank_Account']=request('Bank_Account');
        $data['Qualifications']=request('Qualifications');
        $data['Address']=request('Address');
        $data['Social_Status']=request('Social_Status');
        $data['ID_Number']=request('ID_Number');
        $data['Contract_Start']=request('Contract_Start');
        $data['Contract_End']=request('Contract_End');
        $data['Phone']=request('Phone');
        $data['Phone2']=request('Phone2');
        $data['Email']=request('email');
        $data['Password']=request('Password');
        $data['Job']=request('Job');
        $data['Department']=request('Department');
        $data['Account']=request('Account');
        $data['Price_Level']=request('Price_Level');
        $data['Covenant']=null;
        $data['Commission']=null;
        $data['Account_Emp']=$Acc->id;
        $data['User']=auth()->guard('admin')->user()->id;
        $data['Bill_Num']=request('Bill_Num');
        $data['NumbersOfBill']=request('NumbersOfBill');
           
        $data['EmpSort']=request('EmpSort');
        $data['duration_criminal_investigation']=request('duration_criminal_investigation');
        $data['Birthdate']=request('Birthdate');
        $data['Attitude_recruiting']=request('Attitude_recruiting');
        $data['Job_Number']=request('Job_Number');
        $data['date_resignation']=request('date_resignation');
        $data['Living']=request('Living');
        $data['Branch']=request('Branch');
        $data['Level']=request('Level');
        $data['Religion']=request('Religion');
        $data['Insurance_salary']=request('Insurance_salary');
        $data['Insurance_companies']=request('Insurance_companies');
        $data['Previous_experience']=request('Previous_experience');
        $data['Nationality']=request('Nationality');
        $data['MonthlyTarget']=request('MonthlyTarget');
        $data['QuarterTarget']=request('QuarterTarget');
        $data['SemiTarget']=request('SemiTarget');
        $data['YearlyTarget']=request('YearlyTarget');
        $data['IDExpireDate']=request('IDExpireDate');
        $data['LicensExpireDate']=request('LicensExpireDate');
        $data['PassportExpireDate']=request('PassportExpireDate');
        $data['Pro_Group']=request('Pro_Group');
        $data['SearchCode']=request('SearchCode');
        $data['Active']=1;
           

           
          Employess::create($data);

          $IDD= Employess::orderBy('id','desc')->first();
           
           
           if(!empty(request('Contract_End'))){
                    $event['Start_Date']=request('Contract_End');
         $event['End_Date']=request('Contract_End');
         $event['Event_Ar_Name']='انتهاء  عقد لموظف';
         $event['Event_En_Name']='Contract end';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
    if(!empty(request('IDExpireDate'))){
                    $event['Start_Date']=request('IDExpireDate');
         $event['End_Date']=request('IDExpireDate');
         $event['Event_Ar_Name']='انتهاء  بطاقة لموظف';
         $event['Event_En_Name']='ID Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
               if(!empty(request('LicensExpireDate'))){
                    $event['Start_Date']=request('LicensExpireDate');
         $event['End_Date']=request('LicensExpireDate');
         $event['Event_Ar_Name']='انتهاء  الرخصة لموظف';
         $event['Event_En_Name']='Licens Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
                 if(!empty(request('PassportExpireDate'))){
                    $event['Start_Date']=request('PassportExpireDate');
         $event['End_Date']=request('PassportExpireDate');
         $event['Event_Ar_Name']='انتهاء  جواز سفر لموظف';
         $event['Event_En_Name']='Passport Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
                if(!empty(request('duration_criminal_investigation'))){
                    $event['Start_Date']=request('duration_criminal_investigation');
         $event['End_Date']=request('duration_criminal_investigation');
         $event['Event_Ar_Name']='انتهاء البحث الجنائي لموظف';
         $event['Event_En_Name']='Criminal investigation end';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
           
           
           
           if(!empty(request('POSStores'))){

            $Store=request('POSStores');

            for ($i=0; $i < count($Store); $i++) { 
    
    $uu['Store']=$Store[$i];
     $uu['Emp']=$IDD->id;
EmpPOSStores::create($uu);

            }

           }





            $counttt=AcccountingManual::orderBy('id','desc')->where('Parent',164)->count();        
            $codeCt=AcccountingManual::orderBy('id','desc')->where('Parent',164)->first();    
            $codeeCt=AcccountingManual::find(164);   
 
                if($counttt == 0){
                    
                $xxt=$codeeCt->Code.'01';    
             $dataXYt['Code']=(int) $xxt ;
                      
                }else{
                    
         $y=substr($codeCt->Code, strlen($codeeCt->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xxt= $codeeCt->Code.$NewXY; 
                    
                  $dataXYt['Code']=(int) $xxt;  
       
                }
                
         $dataXYt['Name']=request('Name').' '.' عموله ';
           
                  if(!empty(request('NameEn'))){
           $dataXYt['NameEn']=request('NameEn').' '.' Commission ';
          }else{
             $dataXYt['NameEn']=request('Name').' '.' Commission ';
              
          }

           
     
         $dataXYt['Type']=1;
         $dataXYt['Parent']=164;
         $dataXYt['Note']=request('Note');
         $dataXYt['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXYt);
           
           $mola=AcccountingManual::orderBy('id','desc')->first(); 
           
           
           Employess::where('id',$IDD->id)->update(['Commission'=>$mola->id]);

 
                   $countt=AcccountingManual::orderBy('id','desc')->where('Parent',121)->count();        
            $codeC=AcccountingManual::orderBy('id','desc')->where('Parent',121)->first();    
            $codeeC=AcccountingManual::find(121);   
 
                if($countt == 0){
                    
                $xx=$codeeC->Code.'01';    
             $dataXY['Code']=(int) $xx ;
                      
                }else{
                    
             $y=substr($codeC->Code, strlen($codeeC->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xx= $codeeC->Code.$NewXY; 
                  $dataXY['Code']=(int) $xx;  
       
                }
                
         $dataXY['Name']=request('Name').' '.' عهده ';
           
                         if(!empty(request('NameEn'))){
             $dataXY['NameEn']=request('NameEn').' '.' Custody ';
          }else{
            $dataXY['NameEn']=request('Name').' '.' Custody ';
              
          }
           
      
         $dataXY['Type']=1;
         $dataXY['Parent']=121;
         $dataXY['Note']=request('Note');
         $dataXY['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXY);
           
             $ohda=AcccountingManual::orderBy('id','desc')->first(); 
           Employess::where('id',$IDD->id)->update(['Covenant'=>$ohda->id]);

           
           
           
                 $countt=AcccountingManual::orderBy('id','desc')->where('Parent',43)->count();        
            $codeC=AcccountingManual::orderBy('id','desc')->where('Parent',43)->first();    
            $codeeC=AcccountingManual::find(43);   
 
                if($countt == 0){
                    
                $xx=$codeeC->Code.'01';    
             $dataXYY['Code']=(int) $xx ;
                      
                }else{
                    
        
                    
                $y=substr($codeC->Code, strlen($codeeC->Code));
          
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $xx= $codeeC->Code.$NewXY; 
                  $dataXYY['Code']=(int) $xx;  
       
                }

                
         $dataXYY['Name']=request('Name').' '.' استحقاق ';
           
                                  if(!empty(request('NameEn'))){
              $dataXYY['NameEn']=request('NameEn').' '.' Merit ';
          }else{
              $dataXYY['NameEn']=request('Name').' '.' Merit ';
              
          }
           
           
     
         $dataXYY['Type']=1;
         $dataXYY['Parent']=43;
         $dataXYY['Note']=request('Note');
         $dataXYY['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataXYY);
           
             $esthkak=AcccountingManual::orderBy('id','desc')->first(); 
           Employess::where('id',$IDD->id)->update(['Merit'=>$esthkak->id]);

           
           
           
           
           
        if(!empty(request('from'))){
            
            $f=request('from');
            $t=request('to');
            $s=request('salary');
            $r=request('rate');
            $ty=request('type');
            $tyy=request('typee');
            
            for($i=0 ; $i < count($f) ; $i++){
                
        $da['From']=$f[$i];
        $da['To']=$t[$i];
        $da['Salary']=$s[$i];
        $da['Rate']=$r[$i];
        $da['Type']=$ty[$i];
        $da['Typee']=$tyy[$i];
        $da['Emp']=$IDD->id;
           
           EmpRatio::create($da);

            }
            
        }   

                if(!empty(request('Allow'))){
            
            $Allow=request('Allow');
            $AmountAllow=request('AmountAllow');

            
            for($i=0 ; $i < count($Allow) ; $i++){
                
        $daAll['Allow']=$Allow[$i];
        $daAll['AmountAllow']=$AmountAllow[$i];
        $daAll['Emp']=$IDD->id;
           
           AllowencesEmp::create($daAll);

            }
            
        }   

           
                   if(!empty(request('Discount'))){
            
            $Discount=request('Discount');
            $AmountDiscount=request('AmountDiscount');

            
            for($i=0 ; $i < count($Discount) ; $i++){
                
        $daDis['Discount']=$Discount[$i];
        $daDis['AmountDiscount']=$AmountDiscount[$i];
        $daDis['Emp']=$IDD->id;
           
           DiscountsEmp::create($daDis);

            }
            
        }   

           
           
               if(!empty(request('FromQ'))){
            
            $FromQ=request('FromQ');
            $ToQ=request('ToQ');
            $ValueQ=request('ValueQ');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['FromQ']=$FromQ[$i];
        $daQ['ToQ']=$ToQ[$i];
        $daQ['ValueQ']=$ValueQ[$i];
        $daQ['Emp']=$IDD->id;
           
           EmpsProducationQuantity::create($daQ);

            }
            
        }   
           
           
      if(!empty(request('FromAttendence'))){
            
            $FromQ=request('FromAttendence');
            $ToQ=request('ToAttendence');
            $ValueQ=request('DiscountAttendence');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['From']=$FromQ[$i];
        $daQ['To']=$ToQ[$i];
        $daQ['Discount']=$ValueQ[$i];
        $daQ['Emp']=$IDD->id;
           
           AttendencePolicyEmp::create($daQ);

            }
            
        }   
           
           
                if(!empty(request('FromDeparture'))){
            
            $FromQ=request('FromDeparture');
            $ToQ=request('ToDeparture');
            $ValueQ=request('DiscountDeparture');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['From']=$FromQ[$i];
        $daQ['To']=$ToQ[$i];
        $daQ['Discount']=$ValueQ[$i];
        $daQ['Emp']=$IDD->id;
           
           DepaarturePolicyEmp::create($daQ);

            }
            
        }   
           
           
           
           
           if(request('STORE') == 1){
                                      $Module=ModuleSettingsNum::orderBy('id','desc')->first();
         
         if($Module->Store_Select == 1){
          
             $count=Stores::count();
             
             if($Module->Store_Num <= $count){
              
             session()->flash('error',trans('admin.Alert_Maximum_Add'));
             return back();
             }
             
             
         }
            
           
            $co=AcccountingManual::orderBy('id','desc')->where('Parent',27)->count();        
            $cod=AcccountingManual::orderBy('id','desc')->where('Parent',27)->first();    
            $codd=AcccountingManual::find(27);   
 
                if($co == 0){
                    
                $x=$codd->Code.'01';    
             $data['Code']=(int) $x ;
                      
                }else{
                    
                     $y=substr($cod->Code, strlen($codd->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codd->Code.$NewXY; 
                  $data['Code']=(int) $x;  
       
                }
                
         $data['Name']=request('Name').'  '.' مخزن ';
               
                                      if(!empty(request('NameEn'))){
               $data['NameEn']=request('NameEn').'  '.' Store ';
          }else{
              $data['NameEn']=request('Name').'  '.' Store ';
              
          }
                  
               
      
         $data['Type']=1;
         $data['Parent']=27;
         $data['Note']=null;
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);

         $Accc=AcccountingManual::orderBy('id','desc')->first(); 
        
               
        $rest=Stores::orderBy('id','desc')->first();
           
           if(!empty($rest->Code)){
               
              $Codes=$rest->Code + 1 ; 
               
           }else{
               
              $Codes=1; 
               
           }   

         $dataa['Code']=$Codes;
         $dataa['Date']=date('Y-m-d');
         $dataa['Time']=date("h:i:s a", time());
         $dataa['Name']=request('Name').' '.' مخزن ';
               
                                                 if(!empty(request('NameEn'))){
                  $dataa['NameEn']=request('NameEn').' '.' Store ';
          }else{
               $dataa['NameEn']=request('Name').' '.' Store ';
              
          }
            
   
         $dataa['Phone']=null;
         $dataa['Address']=null;
         $dataa['Account']=$Accc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;
          
         Stores::create($dataa);
               
               
                   $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();        
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();    
            $codee=AcccountingManual::find(24);   
 
                if($count == 0){
                    
                $x=$codee->Code.'01';    
             $dataX['Code']=(int) $x ;
                      
                }else{
                    
                                $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;  
            }         
                $x= $codee->Code.$NewXY; 
                  $dataX['Code']=(int) $x;  
       
                }
                
         $dataX['Name']=request('Name').' عميل مخزن ';
               
            if(!empty(request('NameEn'))){
                  $dataX['NameEn']=request('NameEn').' Client Store ';
          }else{
            $dataX['NameEn']=request('Name').' Client Store ';
              
          }
      
         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($dataX);
        
         $Acc=AcccountingManual::orderBy('id','desc')->first(); 
          
         $store=Stores::orderBy('id','desc')->first(); 

          Stores::where('id',$store->id)->update(['Account_Client'=>$Acc->id]);
               

           }
           

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الموظفين';
           $dataUser['ScreenEn']='Employee';
           $dataUser['Type']='اضافه موظف';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');
                   if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');
              
          }
         
         
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
       public function DeleteEmp($id){
                      
            $Crms=CrmDefaultData::orderBy('id','desc')->first();
  $Purchases=PurchasesDefaultData::orderBy('id','desc')->first();
                $Sales=SalesDefaultData::orderBy('id','desc')->first();
 $Maint=MaintainceDefaultData::orderBy('id','desc')->first();
 $Admin=Admin::where('emp',$id)->first();
           
             if($Crms->Responsible == $id){
         
        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
            if($Purchases->Delegate == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
             if($Purchases->Empp == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
           
           

           
             if($Sales->Delegate == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }

            if($Sales->Empp == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
             if($Maint->Eng == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
            if($Maint->Recipient == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();
             
         }
           
        if(!empty($Admin)) {  
            if($Admin->emp == $id){

        session()->flash('error',trans('admin.Cant_Delete_User_Admin'));
        return back();
             
         }
        }
           
           
             $del=Employess::find($id);
                       
          $xh=GeneralDaily::where('Account',$del->Account_Emp)->orderBy('id','desc')->first();
          $xx=GeneralDaily::where('Account',$del->Covenant)->orderBy('id','desc')->first();
          $xxh=GeneralDaily::where('Account',$del->Commission)->orderBy('id','desc')->first();
          $xxk=GeneralDaily::where('Account',$del->Merit)->orderBy('id','desc')->first();
          $xS=Sales::where('Delegate',$id)->orderBy('id','desc')->first();
          $xP=Purchases::where('Delegate',$id)->orderBy('id','desc')->first();
          $xMM=ReciptMaintaince::where('Eng',$id)->orderBy('id','desc')->first();
          $xM=ReciptMaintaince::where('Recipient',$id)->orderBy('id','desc')->first();

                  if(!empty($xh)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
                  if(!empty($xx)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
                  if(!empty($xxh)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
                  if(!empty($xxk)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
             
                  if(!empty($xS)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
             
                  if(!empty($xP)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
             
                  if(!empty($xMM)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
             
                  if(!empty($xM)){
             
         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();
   
         }           
            
           
      
         AcccountingManual::where('id',$del->Account_Emp)->delete();
         AcccountingManual::where('id',$del->Covenant)->delete();
         AcccountingManual::where('id',$del->Commission)->delete();
         AcccountingManual::where('id',$del->Merit)->delete();
           Event::where('Type_Code',$del->Code)->where('Type','الموظفين')->delete();   
         Admin::where('emp',$del->id)->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='الموظفين';
           $dataUser['ScreenEn']='Employee';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
       
       public function UnActiveEmp($id){
                      
    
           Employess::where('id',$id)->update(['Active'=>0]);

        session()->flash('success',trans('admin.Updated'));
        return back();

           } 
    
           public function ActiveEmp($id){
                      
    
           Employess::where('id',$id)->update(['Active'=>1]);

        session()->flash('success',trans('admin.Updated'));
        return back();

           } 
    
    
    
    

      public function EditEmp($id){

         $Departments=WorkDepartments::all();
         $JobsTypes=JobsTypes::all();
                         $wf=60;
 $Accounts=AcccountingManual::orderBy('Code','asc')->where('Code','like', $wf.'%')->where('Type',0)->get(); 
         
        $item=Employess::find($id);
         $ratios=EmpRatio::where('Emp',$item->id)->get();
          $POSS=EmpPOSStores::where('Emp',$item->id)->get();
          $Qties=EmpsProducationQuantity::where('Emp',$item->id)->get();
                  $Employment_levels=Employment_levels::all();
          $Insurance_companies=Insurance_companies::all();
           $Stores=Stores::all();
           $Branches=Branches::all();
            $Countris=Countris::all();
             $Aloownces=BeneftisTypes::all();
            $Discounts=DeducationsTypes::all();
          
             $alow=AllowencesEmp::where('Emp',$item->id)->get();
             $disco=DiscountsEmp::where('Emp',$item->id)->get();
             $Atts=AttendencePolicyEmp::where('Emp',$item->id)->get();
             $Deps=DepaarturePolicyEmp::where('Emp',$item->id)->get();
           $Groups=ItemsGroups::all();

         return view('admin.HR.EditEmployee',[
             'Departments'=>$Departments,                                            
             'JobsTypes'=>$JobsTypes,                                            
             'item'=>$item,                                            
             'Accounts'=>$Accounts,                                            
             'ratios'=>$ratios,                                            
             'Countris'=>$Countris,                                            
             'Stores'=>$Stores,                      
            'POSS'=>$POSS,   
            'Groups'=>$Groups,   
            'Employment_levels'=>$Employment_levels,   
            'Insurance_companies'=>$Insurance_companies,   
            'Branches'=>$Branches,   
            'Aloownces'=>$Aloownces,   
            'Discounts'=>$Discounts,   
            'alow'=>$alow,   
            'disco'=>$disco,   
            'Qties'=>$Qties,   
            'Atts'=>$Atts,   
            'Deps'=>$Deps,   
         ]);
    }
    
     public function PostEditEmp($id){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Emp_Type'=>'required',
             'Salary'=>'required',
             'Job'=>'required',
             'Department'=>'required',
             'Account'=>'required',
             'Image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Name.required' => trans('admin.NameRequired'),      
            'Emp_Type.required' => trans('admin.Emp_TypeRequired'),      
            'Salary.required' => trans('admin.SalaryRequired'),      
            'Job.required' => trans('admin.JobRequired'),      
            'Department.required' => trans('admin.DepartmentRequired'),      
            'Account.required' => trans('admin.AccountRequired'),      
            'Account_Emp.required' => trans('admin.Account_EmpRequired'),      
         ]);

 
       $del=Employess::find($id);
         
         
         
        AcccountingManual::where('id',$del->Account_Emp)->update(['Name'=>request('Name'),'NameEn'=>request('NameEn')]);
         
 
         
         AcccountingManual::where('id',$del->Covenant)->update(['Name'=>request('Name').' '.' عهده ','NameEn'=>request('NameEn').' '.' Custody ']);
         
      
         AcccountingManual::where('id',$del->Commission)->update(['Name'=>request('Name').' '.' عموله ','NameEn'=>request('NameEn').' '.' Commission ']);
         
        
         
         AcccountingManual::where('id',$del->Merit)->update(['Name'=>request('Name').' '.' استحقاق ','NameEn'=>request('NameEn').' '.' Merit ']);


         
         $image=request()->file('Image');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='EmployeesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['Image']=$image_url; 
                 
             }else{
                 $data['Image']=request('Images');
             }
         
         
             $image1=request()->file('CV');
          if($image1){            
            $image_name1=Str::random(20);
            $ext1=strtolower($image1->getClientOriginalExtension());
            $image_full_name1=$image_name1 .'.' . $ext1 ;
            $upload_path1='EmployeesImages/';
            $image_url1=$upload_path1.$image_full_name1;
            $success1=$image1->move($upload_path1,$image_full_name1);            
                   }
        
    
             if(!empty($image_url1)){
       
                 $data['CV']=$image_url1; 
                 
             }else{
                 $data['CV']=request('CVs');
             }
           
       
                        $image2=request()->file('ID_Image');
          if($image2){            
            $image_name2=Str::random(20);
            $ext2=strtolower($image2->getClientOriginalExtension());
            $image_full_name2=$image_name2 .'.' . $ext2 ;
            $upload_path2='EmployeesImages/';
            $image_url2=$upload_path2.$image_full_name2;
            $success2=$image2->move($upload_path2,$image_full_name2);            
                   }
        
    
             if(!empty($image_url2)){
       
                 $data['ID_Image']=$image_url2; 
                 
             }else{
                 $data['ID_Image']=request('ID_Images');
             }

     
                           $image3=request()->file('Criminal_status');
          if($image3){            
            $image_name3=Str::random(20);
            $ext3=strtolower($image3->getClientOriginalExtension());
            $image_full_name3=$image_name3 .'.' . $ext3 ;
            $upload_path3='EmployeesImages/';
            $image_url3=$upload_path3.$image_full_name3;
            $success3=$image3->move($upload_path3,$image_full_name3);            
                   }
        
    
             if(!empty($image_url3)){
       
                 $data['Criminal_status']=$image_url3; 
                 
             }else{
                 $data['Criminal_status']=request('Criminal_statuss');
             }

    
                           $image4=request()->file('Contract');
          if($image4){            
            $image_name4=Str::random(20);
            $ext4=strtolower($image4->getClientOriginalExtension());
            $image_full_name4=$image_name4 .'.' . $ext4 ;
            $upload_path4='EmployeesImages/';
            $image_url4=$upload_path4.$image_full_name4;
            $success4=$image4->move($upload_path4,$image_full_name4);            
                   }
        
    
             if(!empty($image_url4)){
       
                 $data['Contract']=$image_url4; 
                 
             }else{
                 $data['Contract']=request('Contracts');
             }

    
                           $image5=request()->file('health_certificate');
          if($image5){            
            $image_name5=Str::random(20);
            $ext5=strtolower($image5->getClientOriginalExtension());
            $image_full_name5=$image_name5 .'.' . $ext5 ;
            $upload_path5='EmployeesImages/';
            $image_url5=$upload_path5.$image_full_name5;
            $success5=$image5->move($upload_path5,$image_full_name5);            
                   }
        
    
             if(!empty($image_url5)){
       
                 $data['health_certificate']=$image_url5; 
                 
             }else{
                 $data['health_certificate']=request('health_certificates');
             }

 
                           $image6=request()->file('Search_Card');
          if($image6){            
            $image_name6=Str::random(20);
            $ext6=strtolower($image6->getClientOriginalExtension());
            $image_full_name6=$image_name6 .'.' . $ext6 ;
            $upload_path6='EmployeesImages/';
            $image_url6=$upload_path6.$image_full_name6;
            $success6=$image6->move($upload_path6,$image_full_name6);            
                   }
        
    
             if(!empty($image_url6)){
       
                 $data['Search_Card']=$image_url6; 
                 
             }else{
                 $data['Search_Card']=request('Search_Cards');
             }

    
               
                           $image7=request()->file('Recruitment_certificate');
          if($image7){            
            $image_name7=Str::random(20);
            $ext7=strtolower($image7->getClientOriginalExtension());
            $image_full_name7=$image_name7 .'.' . $ext7 ;
            $upload_path7='EmployeesImages/';
            $image_url7=$upload_path7.$image_full_name7;
            $success7=$image7->move($upload_path7,$image_full_name7);            
                   }
        
    
             if(!empty($image_url7)){
       
                 $data['Recruitment_certificate']=$image_url7; 
                 
             }else{
                 $data['Recruitment_certificate']=request('Recruitment_certificates');
             }

      
                           $image8=request()->file('employee_profile');
          if($image8){            
            $image_name8=Str::random(20);
            $ext8=strtolower($image8->getClientOriginalExtension());
            $image_full_name8=$image_name8 .'.' . $ext8 ;
            $upload_path8='EmployeesImages/';
            $image_url8=$upload_path8.$image_full_name8;
            $success8=$image8->move($upload_path8,$image_full_name8);            
                   }
        
    
             if(!empty($image_url8)){
       
                 $data['employee_profile']=$image_url8; 
                 
             }else{
                 $data['employee_profile']=request('employee_profiles');
             }


        $data['Code']=request('Code');
        $data['Name']=request('Name');
        $data['NameEn']=request('NameEn');
        $data['Emp_Type']=request('Emp_Type');
        $data['Salary']=request('Salary');
        $data['Attendence']=request('Attendence');
        $data['Departure']=request('Departure');
        $data['Hours_Numbers']=request('Hours_Numbers');
        $data['Days_Numbers']=request('Days_Numbers');
        $data['Day_Price']=request('Day_Price');
        $data['Precentage_of_Sales']=request('Precentage_of_Sales');
        $data['Precentage_of_Profits']=request('Precentage_of_Profits');
        $data['Precentage_of_Execution']=request('Precentage_of_Execution');
        $data['Note']=request('Note');
        $data['Bank_Account']=request('Bank_Account');
        $data['Qualifications']=request('Qualifications');
        $data['Address']=request('Address');
        $data['Social_Status']=request('Social_Status');
        $data['ID_Number']=request('ID_Number');
        $data['Contract_Start']=request('Contract_Start');
        $data['Contract_End']=request('Contract_End');
        $data['Phone']=request('Phone');
        $data['Phone2']=request('Phone2');
        $data['Email']=request('Email');
        $data['Password']=request('Password');
        $data['Job']=request('Job');
        $data['Department']=request('Department');
        $data['Account']=request('Account');
        $data['Account_Emp']=request('Account_Emp');
        $data['User']=auth()->guard('admin')->user()->id;
               $data['Price_Level']=request('Price_Level');
             $data['Bill_Num']=request('Bill_Num');
        $data['NumbersOfBill']=request('NumbersOfBill');
         
            $data['EmpSort']=request('EmpSort');
        $data['duration_criminal_investigation']=request('duration_criminal_investigation');
        $data['Birthdate']=request('Birthdate');
        $data['Attitude_recruiting']=request('Attitude_recruiting');
        $data['Job_Number']=request('Job_Number');
        $data['date_resignation']=request('date_resignation');
        $data['Living']=request('Living');
        $data['Branch']=request('Branch');
        $data['Level']=request('Level');
        $data['Religion']=request('Religion');
        $data['Insurance_salary']=request('Insurance_salary');
        $data['Insurance_companies']=request('Insurance_companies');
        $data['Previous_experience']=request('Previous_experience');
        $data['Nationality']=request('Nationality');
        $data['MonthlyTarget']=request('MonthlyTarget');
        $data['QuarterTarget']=request('QuarterTarget');
        $data['SemiTarget']=request('SemiTarget');
        $data['YearlyTarget']=request('YearlyTarget');
        $data['IDExpireDate']=request('IDExpireDate');
        $data['LicensExpireDate']=request('LicensExpireDate');
        $data['PassportExpireDate']=request('PassportExpireDate');
          $data['Pro_Group']=request('Pro_Group');
          $data['SearchCode']=request('SearchCode');
          Employess::where('id',$id)->update($data);

         
         $IDD=Employess::find($id);
         
         
               $del=Employess::find($id);

   Event::where('Type_Code',$del->Code)->where('Type','الموظفين')->delete();

          
           if(!empty(request('Contract_End'))){
                    $event['Start_Date']=request('Contract_End');
         $event['End_Date']=request('Contract_End');
         $event['Event_Ar_Name']='انتهاء  عقد لموظف';
         $event['Event_En_Name']='Contract end';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
    if(!empty(request('IDExpireDate'))){
                    $event['Start_Date']=request('IDExpireDate');
         $event['End_Date']=request('IDExpireDate');
         $event['Event_Ar_Name']='انتهاء  بطاقة لموظف';
         $event['Event_En_Name']='ID Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
               if(!empty(request('LicensExpireDate'))){
                    $event['Start_Date']=request('LicensExpireDate');
         $event['End_Date']=request('LicensExpireDate');
         $event['Event_Ar_Name']='انتهاء  الرخصة لموظف';
         $event['Event_En_Name']='Licens Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
                 if(!empty(request('PassportExpireDate'))){
                    $event['Start_Date']=request('PassportExpireDate');
         $event['End_Date']=request('PassportExpireDate');
         $event['Event_Ar_Name']='انتهاء  جواز سفر لموظف';
         $event['Event_En_Name']='Passport Expired';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
                if(!empty(request('duration_criminal_investigation'))){
                    $event['Start_Date']=request('duration_criminal_investigation');
         $event['End_Date']=request('duration_criminal_investigation');
         $event['Event_Ar_Name']='انتهاء البحث الجنائي لموظف';
         $event['Event_En_Name']='Criminal investigation end';
         $event['Type']='الموظفين';
         $event['Type_ID']=null;
         $event['Type_Code']=request('Code');
         $event['Emp']=$IDD->id;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

           }
           
           
           
           




         
                if(!empty(request('POSStores'))){
EmpPOSStores::where('Emp',$id)->delete();
            $Store=request('POSStores');

            for ($i=0; $i < count($Store); $i++) { 
    
    $uu['Store']=$Store[$i];
     $uu['Emp']=$id;
EmpPOSStores::create($uu);

            }

           }


         
         if(!empty(request('from'))){
            
             EmpRatio::where('Emp',$id)->delete();
             
            $f=request('from');
            $t=request('to');
            $s=request('salary');
            $r=request('rate');
            $ty=request('type');
                      $tyy=request('typee');
            for($i=0 ; $i < count($f) ; $i++){
                
        $da['From']=$f[$i];
        $da['To']=$t[$i];
        $da['Salary']=$s[$i];
        $da['Rate']=$r[$i];
        $da['Type']=$ty[$i];
        $da['Emp']=$id;
        $da['Typee']=$tyy[$i];  
           EmpRatio::create($da);

            }
            
        }   
         
         
                         if(!empty(request('Allow'))){
             AllowencesEmp::where('Emp',$id)->delete();
            $Allow=request('Allow');
            $AmountAllow=request('AmountAllow');

            
            for($i=0 ; $i < count($Allow) ; $i++){
                
        $daAll['Allow']=$Allow[$i];
        $daAll['AmountAllow']=$AmountAllow[$i];
        $daAll['Emp']=$id;
           
           AllowencesEmp::create($daAll);

            }
            
        }   

           
                   if(!empty(request('Discount'))){
             DiscountsEmp::where('Emp',$id)->delete();
            $Discount=request('Discount');
            $AmountDiscount=request('AmountDiscount');

            
            for($i=0 ; $i < count($Discount) ; $i++){
                
        $daDis['Discount']=$Discount[$i];
        $daDis['AmountDiscount']=$AmountDiscount[$i];
        $daDis['Emp']=$id;
           
           DiscountsEmp::create($daDis);

            }
            
        }   

                    
               if(!empty(request('FromQ'))){
                      EmpsProducationQuantity::where('Emp',$id)->delete();
            $FromQ=request('FromQ');
            $ToQ=request('ToQ');
            $ValueQ=request('ValueQ');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['FromQ']=$FromQ[$i];
        $daQ['ToQ']=$ToQ[$i];
        $daQ['ValueQ']=$ValueQ[$i];
        $daQ['Emp']=$id;
           
           EmpsProducationQuantity::create($daQ);

            }
            
        }   
           
                 if(!empty(request('FromAttendence'))){
               AttendencePolicyEmp::where('Emp',$id)->delete();
            $FromQ=request('FromAttendence');
            $ToQ=request('ToAttendence');
            $ValueQ=request('DiscountAttendence');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['From']=$FromQ[$i];
        $daQ['To']=$ToQ[$i];
        $daQ['Discount']=$ValueQ[$i];
        $daQ['Emp']=$id;
           
           AttendencePolicyEmp::create($daQ);

            }
            
        }   
           
           
                if(!empty(request('FromDeparture'))){
               DepaarturePolicyEmp::where('Emp',$id)->delete();
            $FromQ=request('FromDeparture');
            $ToQ=request('ToDeparture');
            $ValueQ=request('DiscountDeparture');

            
            for($i=0 ; $i < count($FromQ) ; $i++){
                
        $daQ['From']=$FromQ[$i];
        $daQ['To']=$ToQ[$i];
        $daQ['Discount']=$ValueQ[$i];
        $daQ['Emp']=$id;
           
           DepaarturePolicyEmp::create($daQ);

            }
            
        }   
           
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='الموظفين';
           $dataUser['ScreenEn']='Employee';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
            return redirect('EmpSechdule');
        
    }
    
    
       public function TransToEmp($id){
                      
        Employess::where('id',$id)->update(['EmpSort'=>1]);
         

        session()->flash('success',trans('admin.TransferedSuccessfully'));
        return back();

           }
    
 
              public function PrintEmp($id){
                      
       $item=Employess::find($id);
  return view('admin.HR.PrintEmp',['item'=>$item]);

           }
    
              public function PrintCardEmp($id){
                      
       $item=Employess::find($id);
  return view('admin.HR.PrintCardEmp',['item'=>$item]);

           }
    
          public function MyGoals(){
                 
                $d = new DateTime('first day of this month');
   
         $fromP=date('Y-m-d', strtotime(date('Y-m')." -1 month"));      
         $ToP=date('Y-m-t',strtotime('last month')); 
              
              
       $from=$d->format('Y-m-d');    
       $to=date('Y-m-t',strtotime('today'));    
       
                $fromQ=date('Y-m-d', strtotime('first day of january this year'));    
       $toQ=date('Y-m-d', strtotime("+3 months", strtotime($fromQ)));  
              
                $fromS=date('Y-m-d', strtotime('first day of january this year'));    
       $toS=date('Y-m-d', strtotime("+6 months", strtotime($fromS)));  
              
                $fromY=date('Y-m-d', strtotime('first day of january this year'));    
       $toY=date('Y-m-d', strtotime('Dec 31'));  
              
              
              
       $previousMonthT=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromP,$ToP])
           ->get()->sum('Total_Price');
            
               $previousMonthTax=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromP,$ToP])
           ->get()->sum('Total_Taxes');
              
               $previousMonthDisc=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromP,$ToP])
           ->get()->sum('Total_Discount');
              
              $previousMonth=  ($previousMonthT +  $previousMonthTax)  - $previousMonthDisc ;
              
              
                    $CurrentMonthT=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$from,$to])
           ->get()->sum('Total_Price');
            
               $CurrentMonthTax=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$from,$to])
           ->get()->sum('Total_Taxes');
              
               $CurrentMonthDisc=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$from,$to])
           ->get()->sum('Total_Discount');
              
              $CurrentMonth=  ($CurrentMonthT +  $CurrentMonthTax)  - $CurrentMonthDisc ;
              
              
               $QMonthT=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromQ,$toQ])
           ->get()->sum('Total_Price');
            
               $QMonthTax=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromQ,$toQ])
           ->get()->sum('Total_Taxes');
              
               $QMonthDisc=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromQ,$toQ])
           ->get()->sum('Total_Discount');
              
              $QMonth=  ($QMonthT +  $QMonthTax)  - $QMonthDisc ;  
              
              
               $SMonthT=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromS,$toS])
           ->get()->sum('Total_Price');
            
               $SMonthTax=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromS,$toS])
           ->get()->sum('Total_Taxes');
              
               $SMonthDisc=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromS,$toS])
           ->get()->sum('Total_Discount');
              
              $SMonth=  ($SMonthT +  $SMonthTax)  - $SMonthDisc ;  
              
              
              
              
                          $YMonthT=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromY,$toY])
           ->get()->sum('Total_Price');
            
               $YMonthTax=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromY,$toY])
           ->get()->sum('Total_Taxes');
              
               $YMonthDisc=Sales::where('Delegate',auth()->guard('admin')->user()->emp)
           ->whereBetween('Date',[$fromY,$toY])
           ->get()->sum('Total_Discount');
              
              $YMonth=  ($YMonthT +  $YMonthTax)  - $YMonthDisc ;  
              
              $Emp=Employess::find(auth()->guard('admin')->user()->emp);
              
  return view('admin.HR.MyGoals',[
      'previousMonth'=>$previousMonth,
      'CurrentMonth'=>$CurrentMonth,
      'QMonth'=>$QMonth,
      'SMonth'=>$SMonth,
      'YMonth'=>$YMonth,
      'Emp'=>$Emp,
  ]);

           }

           //======  Loan_Types ======= 
    public function Loan_TypesPage(){
        $items=LoanTypes::all();
         return view('admin.HR.LoanTypes',['items'=>$items]);
    }
    
     public function AddLoan_Types(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         LoanTypes::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                $dataUser['Screen']='انواع القروض';
           $dataUser['ScreenEn']='Loan Types';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditLoan_Types($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
       
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           LoanTypes::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                     $dataUser['Screen']='انواع القروض';
           $dataUser['ScreenEn']='Loan Types';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteLoan_Types($id){
                      
        $del=LoanTypes::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='انواع القروض';
           $dataUser['ScreenEn']='Loan Types';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    
    
           //======  EmpsProducationPoint ======= 
    public function ProducationPoints(){
        $items=EmpsProducationPoint::paginate(100);
         return view('admin.HR.ProducationPoint',['items'=>$items]);
    }
    
     public function AddProducationPoints(){
        
        $data= $this->validate(request(),[
             'Month'=>'required',
             'Point'=>'required',
             'Emp'=>'required',
             'Date'=>'required',
     
             
               ],[
 
         ]);
   
         
           
         $data['Month']=request('Month');
         $data['Point']=request('Point');
         $data['Emp']=request('Emp');
         $data['Date']=request('Date');
         

         EmpsProducationPoint::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                              $dataUser['Screen']='نقاط الانتاج';
           $dataUser['ScreenEn']='Producation Points';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Month');
           $dataUser['ExplainEn']=request('Month');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditProducationPoints($id){ 
         
       $data= $this->validate(request(),[
             'Month'=>'required',
             'Point'=>'required',
             'Emp'=>'required',
             'Date'=>'required',
     
             
               ],[
 
         ]);
   
         
           
         $data['Month']=request('Month');
         $data['Point']=request('Point');
         $data['Emp']=request('Emp');
         $data['Date']=request('Date');

           EmpsProducationPoint::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                                  $dataUser['Screen']='نقاط الانتاج';
           $dataUser['ScreenEn']='Producation Points';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Month');
           $dataUser['ExplainEn']=request('Month');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteProducationPoints($id){
                      
        $del=EmpsProducationPoint::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                             $dataUser['Screen']='نقاط الانتاج';
           $dataUser['ScreenEn']='Producation Points';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Month;
            $dataUser['ExplainEn']=$del->Month;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
 
    //======  Borrow ======= 
      public function BorrowPage(){
        $items=Borrowa::paginate(100);
         return view('admin.HR.Borrows',[
             'items'=>$items,
         ]);
    }

      public function AddBorrowPage(){
        
          $CostCenters=CostCenter::all();
          
            $Coins=Coins::all();  
          
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
          
                $res=Borrowa::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
          
         return view('admin.HR.AddBorrow',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Code'=>$Code,
         ]);
          
    }

       public function EmpCheck($Emp,$Month) {         
         
    $Sal=Employess::find($Emp);
      $x = Borrowa::where("Month",$Month)->where('Emp',$Emp)->get()->sum('Amount');    

          $states=[];
 $states += ["Total" => $x ,'Salary' => $Sal->Salary];   

       return response()->json($states);
           
    }
    
      public function PostAddBorrow(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
               ],[

     
         ]);
          
          
                 $debt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Debitor');  
           $crdt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Creditor');  
            $dif=$debt - $crdt ;
            $SafyFatora=request('Amount');
          


          if($dif < $SafyFatora){
             
              session()->flash('error',trans('admin.SafeNotEnoughMoney'));
              return back();
              
          }

         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Draw']=request('Draw');
         $data['Note']=request('Note');
         $data['Safe']=request('Safe');
         $data['Emp']=request('Emp');
         $data['Coin']=request('Coin');
         $data['Cost_Center']=request('Cost_Center');
         $data['User']=auth()->guard('admin')->user()->id;

         Borrowa::create($data);
  
    $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    
          
          
                    $c= DB::select("SELECT last_value FROM borrowas_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
          

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'سلفه موظف',
            'TypeEn' => 'Employee Borrow',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Account_Emp;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='سلفه موظف';
        $Gen['TypeEn']='Employee Borrow';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Account_Emp;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
             $Gen['Type']='سلفه موظف';
        $Gen['TypeEn']='Employee Borrow';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='السلف';
           $dataUser['ScreenEn']='Borrows';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function DeleteBorrow($id){
                      
        $del=Borrowa::find($id);
         
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','سلفه موظف')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سلفه موظف')->delete();
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='السلف';
           $dataUser['ScreenEn']='Borrows';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
   
    //======  Entitlement ======= 
     public function EntitlementsPage(){
        $items=Entitlement::paginate(100);
          $Coins=Coins::all();  
          $BeneftisTypes=BeneftisTypes::all();  

        $res=Entitlement::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.Entitlement',[
             'items'=>$items,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'BeneftisTypes'=>$BeneftisTypes,
         ]);
    }
    
     public function AddEntitlements(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Code'=>'required',
             'Draw'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Draw']=request('Draw');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Coin']=request('Coin');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Entitlement::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Entitlements');
                         $dataUser['Screen']='الاستحقاقات';
           $dataUser['ScreenEn']='Entitlements';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

      public function EditEntitlements($id){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Code'=>'required',
             'Draw'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Draw']=request('Draw');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Coin']=request('Coin');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Entitlement::where('id',$id)->update($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='الاستحقاقات';
           $dataUser['ScreenEn']='Entitlements';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

      public function DeleteEntitlements($id){
                      
        $del=Entitlement::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='الاستحقاقات';
           $dataUser['ScreenEn']='Entitlements';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 

    
        //======  Deduction ======= 
     public function DeducationPage(){
        $items=Deduction::paginate(100);
          $Coins=Coins::all();  
          $DeducationsTypes=DeducationsTypes::all();  

        $res=Deduction::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.Deduction',[
             'items'=>$items,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'DeducationsTypes'=>$DeducationsTypes,
         ]);
    }
    
     public function AddDeducation(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Code'=>'required',
             'Draw'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Draw']=request('Draw');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Coin']=request('Coin');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Deduction::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                               $dataUser['Screen']='الاستقطاعات';
           $dataUser['ScreenEn']='Deduction';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

      public function EditDeducation($id){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Code'=>'required',
             'Draw'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Draw']=request('Draw');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Coin']=request('Coin');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Deduction::where('id',$id)->update($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                             $dataUser['Screen']='الاستقطاعات';
           $dataUser['ScreenEn']='Deduction';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

      public function DeleteDeducation($id){
                      
        $del=Deduction::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='الاستقطاعات';
           $dataUser['ScreenEn']='Deduction';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 

    
        //======  Holidays ======= 
     public function HolidaysPage(){
        $items=Holidays::where('Status',0)->paginate(100);

          $HolidaysTypes=HolidaysTypes::all();  

        $res=Holidays::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.Holidays',[
             'items'=>$items,
             'Code'=>$Code,
             'HolidaysTypes'=>$HolidaysTypes,
         ]);
    }
    
     public function AddHolidays(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Num_of_Days'=>'required',
             'Start_Date'=>'required',
             'Discount'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
    
         
         
         $Emp=request('Emp');
         $Code=request('Code');
        for($i=0 ; $i < count($Emp) ;  $i++){    
            
         $data['Code']=$Code;
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Num_of_Days']=request('Num_of_Days');
         $data['Start_Date']=request('Start_Date');
         $data['Discount']=request('Discount');
         $data['Status']=0;
         $data['Note']=request('Note');
         $data['Emp']=$Emp[$i];
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;

         Holidays::create($data);
            $Code++;
            
 
           
         $event['Start_Date']=request('Start_Date');
         $event['End_Date']=date('Y-m-d', strtotime(request('Start_Date'). ' + '.request('Num_of_Days').' days'));
         $event['Event_Ar_Name']='اجازة موظف';
         $event['Event_En_Name']='Employee leave';
         $event['Type']='الاجازات';
         $event['Type_ID']=null;
         $event['Type_Code']=$Code;
         $event['Emp']=$Emp[$i];
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
     
            
        }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                  $dataUser['Screen']='الاجازات';
           $dataUser['ScreenEn']='Holidays';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$Code;
           $dataUser['ExplainEn']=$Code;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

      public function EditHolidays($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Num_of_Days'=>'required',
             'Start_Date'=>'required',
             'Discount'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
        $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Num_of_Days']=request('Num_of_Days');
         $data['Start_Date']=request('Start_Date');
         $data['Discount']=request('Discount');
         $data['Status']=0;
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Holidays::where('id',$id)->update($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='الاجازات';
           $dataUser['ScreenEn']='Holidays';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

      public function DeleteHolidays($id){
                      
        $del=Holidays::find($id);

      Event::where('Type_Code',$del->Code)->where('Type','الاجازات')->delete();        
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                     $dataUser['Screen']='الاجازات';
           $dataUser['ScreenEn']='Holidays';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
       public function HolidaysTypeFilter($type) {         
         
            $typ=HolidaysTypes::find($type);
            $states=[];
            $states += ["Total" => $typ->Days ];   

       return response()->json($states);
           
    }
    
        //======  Holidays Order ======= 
     public function HolidaysOrderPage(){
        $items=Holidays::where('Status',1)->paginate(100);

          $HolidaysTypes=HolidaysTypes::all();  

        $res=Holidays::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.HolidaysOrders',[
             'items'=>$items,
             'Code'=>$Code,
             'HolidaysTypes'=>$HolidaysTypes,
         ]);
    }

     public function AddHolidaysOrder(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Num_of_Days'=>'required',
             'Start_Date'=>'required',
             'Discount'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
    
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Num_of_Days']=request('Num_of_Days');
         $data['Start_Date']=request('Start_Date');
         $data['Discount']=request('Discount');
         $data['Status']=1;
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;

         Holidays::create($data);
         
         
         
            
              
                       $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='طلب اجازة';
         $notii['Noti_En_Name']='Holiday Request';
         $notii['Type']='الاجازات';
  $notii['TypeEn']='Holidays';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Emp');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
             
                notify()->success(trans('admin.HolidayRequest'));
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='طلب اجازه';
           $dataUser['ScreenEn']='Holiday Order';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

      public function EditHolidaysOrder($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Num_of_Days'=>'required',
             'Start_Date'=>'required',
             'Discount'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
   
 
              $del=Holidays::find($id);
          Notifications::where('Type_Code',$del->Code)->where('Type','الاجازات')->delete(); 
          
        $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Num_of_Days']=request('Num_of_Days');
         $data['Start_Date']=request('Start_Date');
         $data['Discount']=request('Discount');
         $data['Status']=1;
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;
  
        
         Holidays::where('id',$id)->update($data);
         
                 
                 
          
                       $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='طلب اجازة';
         $notii['Noti_En_Name']='Holiday Request';
         $notii['Type']='الاجازات';
  $notii['TypeEn']='Holidays';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Emp');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);
             
                notify()->success(trans('admin.HolidayRequest'));
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='طلب اجازه';
           $dataUser['ScreenEn']='Holiday Order';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }

      public function DeleteHolidaysOrder($id){
                      
        $del=Holidays::find($id);
     Notifications::where('Type_Code',$del->Code)->where('Type','الاجازات')->delete(); 

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='طلب اجازه';
           $dataUser['ScreenEn']='Holiday Order';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['Explainِر']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
       public function HolidaysOrderTypeFilter($type) {         
         
            $typ=HolidaysTypes::find($type);
            $states=[];
            $states += ["Total" => $typ->Days ];   

       return response()->json($states);
           
    }
    
       public function TransToHoilday($id){
                      
       Holidays::where('id',$id)->update(['Status'=>0]);
           
                   $del=Holidays::find($id);

           
         $event['Start_Date']=$del->Start_Date;
         $event['End_Date']=date('Y-m-d', strtotime($del->Start_Date. ' + '.$del->Num_of_Days.' days'));
         $event['Event_Ar_Name']='اجازة موظف';
         $event['Event_En_Name']='Employee leave';
         $event['Type']='الاجازات';
         $event['Type_ID']=null;
         $event['Type_Code']=$del->Code;
         $event['Emp']=$del->Emp;
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);
           
        session()->flash('success',trans('admin.Converted'));
        return back();

           } 
  
    
//Attendance
      public function AttendancePage(){

        $res=Attendance::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.Attendance',[
             'Code'=>$Code,
         ]);
    }
    
       public function EmpNameFilter($Emp) {         
         
    $emp=Employess::find($Emp);
     

          $states=[];
           
              if(app()->getLocale() == 'ar' ){ 
 $states += ['name' => $emp->Name];   
              }else{
        $states += ['name' => $emp->NameEn];              
              }

       return response()->json($states);
           
    }

     public function AddAttendance(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
            
     
             
               ],[
            
         ]);
    
        $ID = DB::table('attendances')->insertGetId(
            
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Month' => request('Month'),
            'Note' => request('Note'),
            'Status' => 0,
            'User' =>auth()->guard('admin')->user()->id,
        )
    );
         
         
         if(!empty(request('Emp'))){
             
            $Emp=request('Emp'); 
            $In_Time=request('In_Time'); 
            $NoteEmp=request('NoteEmp'); 
         


         for($i=0 ; $i < count($Emp) ; $i++){
             
             $emp['In_Time']=$In_Time[$i];
             $emp['Date']=request('Date');
             $emp['Month']=request('Month');
             $emp['Note']=$NoteEmp[$i];
             $emp['Attend']=$ID;
             $emp['Emp']=$Emp[$i];
           AttendanceEmp::create($emp);   
         }
         }
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الحضور';
           $dataUser['ScreenEn']='Attendance';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }

     public function AttendanceSechdulePage(){
        $items=Attendance::orderBy('id','desc')->paginate(100);

         return view('admin.HR.AttendanceSechdule',[
             'items'=>$items,
         ]);
    }
    
      public function DeleteAttendance($id){
                      
        $del=Attendance::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
       $dataUser['Screen']='الحضور';
           $dataUser['ScreenEn']='Attendance';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 
    
     public function EditAttendancePage($id){

        $item=Attendance::find($id);
  $details=AttendanceEmp::where('Attend',$item->id)->get();    
         return view('admin.HR.EditAttendance',[
             'item'=>$item,
             'details'=>$details,
         ]);
    }
    
      public function PostEditAttendance($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
            
     
             
               ],[
            
         ]);
    
     
            
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Month'] = request('Month');
            $data['Note'] = request('Note');
            $data['Status'] = 0;
            $data['User'] =auth()->guard('admin')->user()->id;

          Attendance::where('id',$id)->update($data);
         
         if(!empty(request('Emp'))){
             
            AttendanceEmp::where('Attend',$id)->delete(); 
            $Emp=request('Emp'); 
            $In_Time=request('In_Time'); 
            $NoteEmp=request('NoteEmp'); 

         for($i=0 ; $i < count($Emp) ; $i++){
             
             $emp['In_Time']=$In_Time[$i];
             $emp['Date']=request('Date');
             $emp['Month']=request('Month');
             $emp['Note']=$NoteEmp[$i];
             $emp['Attend']=$id;
             $emp['Emp']=$Emp[$i];
           AttendanceEmp::create($emp);   
         }
         }
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='الحضور';
           $dataUser['ScreenEn']='Attendance';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
            return redirect('AttendanceSechdule');
        
    }
    
    
//Departure
      public function DeparturePage($id){

        $item=Attendance::find($id);
  $details=AttendanceEmp::where('Attend',$item->id)->get();    
          
          $res=Departure::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }  
          
          
         return view('admin.HR.Departure',[
             'item'=>$item,
             'details'=>$details,
             'Code'=>$Code,
         ]);
    }
    
      public function AddDeparture($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
            
     
             
               ],[
            
         ]);
    
        $ID = DB::table('departures')->insertGetId(
            
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Month' => request('Month'),
            'Note' => request('Note'),
            'Attend' => $id,
            'User' =>auth()->guard('admin')->user()->id,
        )
    );
         
         
         if(!empty(request('Emp'))){
             
            $Emp=request('Emp'); 
            $In_Time=request('In_Time'); 
            $NoteEmp=request('NoteEmp'); 
            $Out_Time=request('Out_Time'); 
            $Hours_Number=request('Hours_Number'); 
         


         for($i=0 ; $i < count($Emp) ; $i++){
             
             
            $Mwzf=Employess::find($Emp[$i]);
             
$Att = new DateTime($Mwzf->Attendence);
$AttTime = new DateTime($In_Time[$i]);
$DifTimeAtt = $Att->diff($AttTime);
       
              
             
$Dep = new DateTime($Mwzf->Departure);
$DepTime = new DateTime($Out_Time[$i]);
$DifTimeDep = $DepTime->diff($Dep);         
             
$x=$DifTimeAtt->format("%h:%i:%s");
$y=$DifTimeDep->format("%h:%i:%s");
             

             
$tAtt = explode(':', $x);
$tDep = explode(':', $y);
$CountMinAtt= ($tAtt[0]*60) + ($tAtt[1]) + ($tAtt[2]/60);
$CountMinDep= ($tDep[0]*60) + ($tDep[1]) + ($tDep[2]/60);

     
             
$AttValue=AttendencePolicyEmp::where('Emp',$Emp[$i])
    ->where('From','<=',$CountMinAtt)
    ->where('To','>=',$CountMinAtt)
    ->get()
    ->sum('Discount');
             
$DepartValue=DepaarturePolicyEmp::where('Emp',$Emp[$i])
    ->where('From','<=',$CountMinDep)
    ->where('To','>=',$CountMinDep)
    ->get()
    ->sum('Discount');
             
             
             
             
             $emp['In_Time']=$In_Time[$i];
             $emp['Date']=request('Date');
             $emp['Month']=request('Month');
             $emp['Note']=$NoteEmp[$i];
             $emp['Out_Time']=$Out_Time[$i];
             $emp['Hours_Number']=$Hours_Number[$i];
             $emp['Departure']=$ID;
             $emp['Emp']=$Emp[$i];
             
             $emp['Disc_Late']=$AttValue;
             $emp['Disc_Early']=$DepartValue;
             
           DepartureEmp::create($emp);   

         }
         }
         
         Attendance::where('id',$id)->update(['Status'=>1]);
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                $dataUser['Screen']='الانصراف';
           $dataUser['ScreenEn']='Departure';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
            return redirect('DepartureSechdule');
        
    }
    
     public function DepartureSechdulePage(){
        $items=Departure::orderBy('id','desc')->paginate(100);

         return view('admin.HR.DepartureSechdule',[
             'items'=>$items,
         ]);
    }
    
    public function EditDeparturePage($id){

        $item=Departure::find($id);
  $details=DepartureEmp::where('Departure',$item->id)->get();    
         return view('admin.HR.EditDeparture',[
             'item'=>$item,
             'details'=>$details,
         ]);
    }
    
     public function PostEditDeparture($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
            
     
             
               ],[
            
         ]);
    
 
            
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Month'] = request('Month');
            $data['Note'] = request('Note');
            $data['Attend'] = request('Attend');
            $data['User'] =auth()->guard('admin')->user()->id;
  
         Departure::where('id',$id)->update($data);
         
         if(!empty(request('Emp'))){
            DepartureEmp::where('Departure',$id)->delete(); 
            $Emp=request('Emp'); 
            $In_Time=request('In_Time'); 
            $NoteEmp=request('NoteEmp'); 
            $Out_Time=request('Out_Time'); 
            $Hours_Number=request('Hours_Number'); 
         


         for($i=0 ; $i < count($Emp) ; $i++){
             
     $Mwzf=Employess::find($Emp[$i]);
             
$Att = new DateTime($Mwzf->Attendence);
$AttTime = new DateTime($In_Time[$i]);
$DifTimeAtt = $Att->diff($AttTime);
       
              
             
$Dep = new DateTime($Mwzf->Departure);
$DepTime = new DateTime($Out_Time[$i]);
$DifTimeDep = $DepTime->diff($Dep);         
             
$x=$DifTimeAtt->format("%h:%i:%s");
$y=$DifTimeDep->format("%h:%i:%s");
             

             
$tAtt = explode(':', $x);
$tDep = explode(':', $y);
$CountMinAtt= ($tAtt[0]*60) + ($tAtt[1]) + ($tAtt[2]/60);
$CountMinDep= ($tDep[0]*60) + ($tDep[1]) + ($tDep[2]/60);

     
             
$AttValue=AttendencePolicyEmp::where('Emp',$Emp[$i])
    ->where('From','<=',$CountMinAtt)
    ->where('To','>=',$CountMinAtt)
    ->get()
    ->sum('Discount');
             
$DepartValue=DepaarturePolicyEmp::where('Emp',$Emp[$i])
    ->where('From','<=',$CountMinDep)
    ->where('To','>=',$CountMinDep)
    ->get()
    ->sum('Discount');
             
             
          $emp['Disc_Late']=$AttValue;
             $emp['Disc_Early']=$DepartValue;         
             $emp['In_Time']=$In_Time[$i];
             $emp['Date']=request('Date');
             $emp['Month']=request('Month');
             $emp['Note']=$NoteEmp[$i];
             $emp['Out_Time']=$Out_Time[$i];
             $emp['Hours_Number']=$Hours_Number[$i];
             $emp['Departure']=$id;
             $emp['Emp']=$Emp[$i];
           DepartureEmp::create($emp);   

         }
         }
         
  
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
          $dataUser['Screen']='الانصراف';
           $dataUser['ScreenEn']='Departure';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
            return redirect('DepartureSechdule');
        
    }
    
   //RegOverTime
      public function RegOverTimePage(){
        $items=RegOverTime::paginate(100);

          $Types=OverTimes::all();  

        $res=RegOverTime::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.RegOverTime',[
             'items'=>$items,
             'Code'=>$Code,
             'Types'=>$Types,
         ]);
    }
    
       public function AddRegOverTime(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
    
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Hours_Number']=request('Hours_Number');
         $data['Hour_Rate']=request('Hour_Rate');
         $data['Total_Hours']=request('Total_Hours');
         $data['Hour_Cost']=request('Hour_Cost');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;

         RegOverTime::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                             $dataUser['Screen']='تسجيل ساعات اضافيه';
           $dataUser['ScreenEn']='Register OverTime';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditRegOverTime($id){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Month'=>'required',
             'Emp'=>'required',
             'Type'=>'required',
     
             
               ],[
            
         ]);
    
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Hours_Number']=request('Hours_Number');
         $data['Hour_Rate']=request('Hour_Rate');
         $data['Total_Hours']=request('Total_Hours');
         $data['Hour_Cost']=request('Hour_Cost');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['User']=auth()->guard('admin')->user()->id;

         RegOverTime::where('id',$id)->update($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                                $dataUser['Screen']='تسجيل ساعات اضافيه';
           $dataUser['ScreenEn']='Register OverTime';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }
    
      public function DeleteRegOverTime($id){
                      
        $del=RegOverTime::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='تسجيل ساعات اضافيه';
           $dataUser['ScreenEn']='Register OverTime';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 

       public function OverTimeTypeFilter($type,$emp) {         
         
    $empp=Employess::find($emp);
    $typp=OverTimes::find($type);
     
        $z = $empp->Salary /  $empp->Hours_Numbers  ; 

          $states=[];
 $states += ['rate' => $typp->Hour , 'cost' => $z];   

       return response()->json($states);
           
    }
    
//Loan 
    public function AddLoanPage(){
        
          $CostCenters=CostCenter::all();
          
            $Coins=Coins::all();  
          
             $Types=LoanTypes::all();  
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
          
                $res=Loan::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
          
         return view('admin.HR.AddLoan',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Code'=>$Code,
             'Types'=>$Types,
         ]);
          
    }

    public function PostAddLoan(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Amount'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
               ],[

     
         ]);

                   $debt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Debitor');  
           $crdt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Creditor');  
            $dif=$debt - $crdt ;
            $SafyFatora=request('Amount');
          


          if($dif < $SafyFatora){
             
              session()->flash('error',trans('admin.SafeNotEnoughMoney'));
              return back();
              
          }

        
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Amount']=request('Amount');
         $data['Years_Number']=request('Years_Number');
         $data['First_Date']=request('First_Date');
         $data['Install']=request('Install');
         $data['Install_Numbers']=request('Install_Numbers');
         $data['Note']=request('Note');
         $data['Emp']=request('Emp');
         $data['Type']=request('Type');
         $data['Cost_Center']=request('Cost_Center');
         $data['Draw']=request('Draw');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['User']=auth()->guard('admin')->user()->id;

         Loan::create($data);
        
        
        
        
                           $c= DB::select("SELECT last_value FROM loans_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
          
        
        

  
    $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'قرض موظف',
            'TypeEn' => 'Employee Loan',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Covenant;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='قرض موظف';
        $Gen['TypeEn']='Employee Loan';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Covenant;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='قرض موظف';
        $Gen['TypeEn']='Employee Loan';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

          

           $L=Loan::orderBy('id','desc')->first();
              
               $installmentsID = DB::table('emp_installments')->insertGetId(
        array(
            
            'Amount' => request('Amount'),
            'Years_Number' => request('Years_Number'),
            'First_Date' => request('First_Date'),
            'Install' => request('Install'),
            'Install_Numbers' => request('Install_Numbers'),
            'Status' => 0,
            'Emp' => request('Emp'),
            'Loan' => $L->id,          
     
        )
    );  

        
              $resdTotal=request('Amount') ;
              
              $monthly = request('Years_Number') * 12 ;
              
              $valueMonth = $resdTotal / $monthly ;
             
              $date=request('First_Date'); 
            for($i=0 ; $i < $monthly ; $i++){ 
                
            $inst['Date']=$date;  
            $inst['Value']=$valueMonth;  
            $inst['Status']=0;  
            $inst['Emp']=request('Emp');  
            $inst['Install']=$installmentsID;  
        EmpInstallmentDetails::create($inst);
                
                
         $event['Start_Date']=$date;
         $event['End_Date']=$date;
         $event['Event_Ar_Name']='قسط موظف';
         $event['Event_En_Name']='Employee Installment';
         $event['Type']='قسط موظف';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=request('Emp');
         $event['Client']=null;
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);    
                  
                
                
            $date = date('Y-m-d',strtotime('+30 days',strtotime($date))) ;   
                
    
            }
        
        
                   $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='القروض';
           $dataUser['ScreenEn']='Loans';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function LoanPage(){
        $items=Loan::paginate(100);
         return view('admin.HR.Loan',[
             'items'=>$items,
         ]);
    }
    
     public function DeleteLoan($id){
                      
        $del=Loan::find($id);
         
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','قرض موظف')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','قرض موظف')->delete();
           Event::where('Type_Code',$del->Code)->where('Type','قسط موظف')->delete();
         EmpInstallment::where('Loan',$del->id)->delete();
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='القروض';
           $dataUser['ScreenEn']='Loans';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
//EmpInstallmentPage
        public function EmpInstallmentPage(){
        $items=EmpInstallment::paginate(100);
          $Coins=Coins::all();  
       if(auth()->guard('admin')->user()->emp == 0){
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
     }else{
        
            if(!empty(auth()->guard('admin')->user()->safe)){
         $Safes=AcccountingManual::where('id',auth()->guard('admin')->user()->safe)->get();
            }else{
                
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
            }
         
     }
          
          
             $Clients = AcccountingManual::
              where('Parent',121)
              ->where('Type',1)
              ->get();
         return view('admin.HR.EmpInstallment',[
             'items'=>$items,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Clients'=>$Clients,
         
         ]);
    }
    
         public function EmpInstallBillDone($id){
                
          EmpInstallment::where('id',$id)->update(['Status'=>1]);
          
        $del=EmpInstallment::find($id);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='اقساط الموظفين';
           $dataUser['Screen']='Employee Installment';
           $dataUser['Type']='قسط قرض';
           $dataUser['Type']='Loan Installment';
            $dataUser['Explain']=$del->Loan;
            $dataUser['ExplainEn']=$del->Loan;
           UsersMoves::create($dataUser);
         
 
        session()->flash('success',trans('admin.InstallmentDone'));
        return back();

           } 
    
     public function EmpUnInstallBill($id){
                
          EmpInstallment::where('id',$id)->update(['Status'=>0]);
          
        $del=EmpInstallment::find($id);


        session()->flash('error',trans('admin.UnInstallmentDone'));
        return back();

           } 
    
       public function EmpInstallDone(){
                
          $data= $this->validate(request(),[
             'Creditor'=>'required',
             'Account'=>'required',
             'Safe'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
           

               ],[
          
            'Creditor.required' => trans('admin.CreditorRequired'),      
            'Account.required' => trans('admin.AccountRequired'),      
            'Safe.required' => trans('admin.SafeRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.DrawRequired'),      
     
         ]);
         
              
              
             $Creditor = request('Creditor');   
                $AccountCode = request('Account');   
                $Safe = request('Safe');   
                $Coin = request('Coin');   
                $Draw = request('Draw');   

        $res=ReciptVoucher::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }

        $IDD = DB::table('recipt_vouchers')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Date' => date('Y-m-d'),
            'Draw' => $Draw,
            'Coin' => $Coin,
            'Safe' => $Safe,
            'Cost_Center' => null,
            'Total_Creditor' => $Creditor,
            'Shift' => null,
            'Store' => null,
            'Note' => null,
            'User' => auth()->guard('admin')->user()->emp,
  
        )
    );
         
           
           
                    $c= DB::select("SELECT last_value FROM recipt_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $Code=$z;   

        $PRODUCTS['RV_ID']=$IDD;
        $PRODUCTS['Creditor']=$Creditor;
        $PRODUCTS['Account']=$AccountCode;
        $PRODUCTS['Statement']=null;
      
         ReciptVoucherDetails::create($PRODUCTS);      

          
            
           $ress=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($ress->Code)){
               
              $Codee=$ress->Code + 1 ; 
           }else{
              $Codee=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Codee,
            'Type' =>'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' => $Code,
            'Date' => date('Y-m-d'),
            'Draw' => $Draw,
            'Coin' => $Coin,
            'Cost_Center' => null,
            'Total_Debaitor' => $Creditor,
            'Total_Creditor' => $Creditor,
            'Note' => null,
  
        )
    );
         

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Creditor;
        $PRODUCTSS['Account']=$AccountCode;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Codee;
        $Gen['Code_Type']=$Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Creditor;
        $Gen['Statement']=null;
        $Gen['Draw']=$Draw;
        $Gen['Debitor_Coin']= $Draw * 0;
        $Gen['Creditor_Coin']=$Draw * $Creditor;
        $Gen['Account']=$AccountCode;
        $Gen['Coin']= $Coin;
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      

            
        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=$Creditor;
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$Safe;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);      
   
        $Genn['Code']=$Codee;
        $Genn['Code_Type']=$Code;
        $Genn['Date']=date('Y-m-d');
       $Genn['Type']='سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=$Creditor;
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=$Draw;
        $Genn['Debitor_Coin']= $Draw * $Creditor;
        $Genn['Creditor_Coin']=$Draw * 0;
        $Genn['Account']=$Safe;
        $Genn['Coin']= $Coin;
        $Genn['Cost_Center']= null;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);      
        
           
           
           $id=request('ID');   

          EmpInstallmentDetails::where('id',$id)->update(['Status'=>1]);
          
        $del=EmpInstallmentDetails::find($id);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='اقساط الموظفين';
           $dataUser['Screen']='Employee Installment';
           $dataUser['Type']='قسط قرض';
           $dataUser['Type']='Loan Installment';
           $dataUser['Explain']=$del->Install()->first()->Loan;
           $dataUser['Explainِر']=$del->Install()->first()->Loan;
           UsersMoves::create($dataUser);
         
 
        session()->flash('success',trans('admin.InstallmentDone'));
       return redirect('InstallEmpPrint/'.$del->Install.'/'.$id);

           } 
    
     public function EmpUnInstall($id){
                
          EmpInstallmentDetails::where('id',$id)->update(['Status'=>0]);
          
        $del=EmpInstallmentDetails::find($id);


 
        session()->flash('error',trans('admin.UnInstallmentDone'));
        return back();

           } 
    
   
 public function InstallEmpPrint($id,$inst){

             $item=EmpInstallment::find($id);
            $details=EmpInstallmentDetails::find($inst);
         return view('admin.HR.InstallmentEmptPrint',[
             'item'=>$item,
             'details'=>$details,

         ]);
    }
    
//Salary
      public function AddSalaryPage(){
        
          $CostCenters=CostCenter::all();
          
            $Coins=Coins::all();  
          
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
          
                $res=PaySalary::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
          
         return view('admin.HR.AddSalary',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Code'=>$Code,
         ]);
          
    }

 public function SalarySechdulesPage(){
        $items=PaySalary::paginate(100);
         return view('admin.HR.Salaries',[
             'items'=>$items,
         ]);
    }
   
 public function EmpCheckSalary($Emp,$Month) {         
         
      $Sal=Employess::find($Emp);
      $x = Borrowa::where("Month",$Month)->where('Emp',$Emp)->get()->sum('Amount');    
      $pp = EmpsProducationPoint::where("Month",$Month)->where('Emp',$Emp)->get()->sum('Point');    
       $point=0; 
       $newpp=0; 
     
     $Qties=EmpsProducationQuantity::where('Emp',$Emp)->get();
     
foreach($Qties as $qty){
    
    if($qty->FromQ <= $pp){
           if($qty->ToQ <= $pp){
            $newpp=$pp - $qty->ToQ ; 
          $point+=$qty->ValueQ * ($pp - $newpp);  
                       
        }
        
        if($qty->ToQ >= $pp){
            if($newpp != 0){
        $point+=$qty->ValueQ * $newpp;   
            }else{
                
              $point+=$qty->ValueQ * $pp;       
            }
        }
        
    }
    
}
      $PaySalary = PaySalary::where("Month",$Month)->where('Emp',$Emp)->first();    

    $month=$Month.'-01';

    $SETT=Settlement::
       where('Date','>=',$month)
     ->where('Account_Dificit',$Sal->Account_Emp)->get()->sum('Total_Dificit_Price');
 
 $SETT +=Consists::
       where('Date','>=',$month)
     ->where('Account',$Sal->Account_Emp)->get()->sum('Total_Price');
     
     
         $saleslater=Sales::
          where('Status',1)
        ->where('Date','>=',$month)      
          ->where('Delegate',$Emp)
          ->where('Payment_Method','Later')
          ->where('Later_Collection',0)
              ->get() 
              ->sum('Total_Price');
     
    
     
     $LATER= 0  ;
     
     
    $s=0;      
    $xx=0;      
    $xxx=0;      
    $xxxx=0;      
       
     
        $sales=Sales::
          where('Status',1)
          ->where('Delegate',$Emp)
              ->get(); 
          
            $Ex=Sales::
          where('Status',1)
          ->where('Executor',$Emp)
              ->get();  
     
         $ShP=ShippingList::
          where('Status',1)
          ->where('Driver',$Emp)
              ->get();   
          
     
     
     $Ratios=EmpRatio::where('Emp',$Emp)->get();
     
          foreach($sales as $sel){

                $date=$sel->Date;
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $s += $sel->Total_Price;
            }
                
          }
          
           foreach($Ex as $ex){
             
                $date=$ex->Date;
              
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $xx += $ex->Total_Price;
            }
                
          }
          
         foreach($ShP as $shp){
             
                $date=$shp->Date;
              
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $xxxx += $shp->Total_Price;
            }
                
          }
          
          
  
     
        $DED=Deduction::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 
        $ENTIT=Entitlement::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 

        $EmpDets=EmpInstallmentDetails::where('Emp',$Emp)->get() ; 
          
          foreach($EmpDets as $emD){
             
                $date=$emD->Date;
              
              $time=strtotime($date);
              $month=date("Y-m",$time);

            if($month == $Month){  
           $xxx += $emD->Value;
            }
                
          }

 $OVER=RegOverTime::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Amount') ; 
 $Attendence=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Hours_Number') ; 
 $DiscountLate=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Disc_Late') ; 
 $DiscountDeparture=DepartureEmp::where('Month',$Month)->where('Emp',$Emp)->get()->sum('Disc_Early') ; 


        $HourCost=$Sal->Salary / $Sal->Hours_Numbers ; 
        
          $disc= $Attendence * $HourCost ;
          $discT= $Sal->Salary - $disc ;
          
        $Holidays=Holidays::where('Month',$Month)->where('Emp',$Emp)->where('Discount',1)->get()->sum('Num_of_Days') ;   
          
          $WorkDay =  $Sal->Hours_Numbers / 30 ; 
          $HoliDiscount = ($HourCost * $WorkDay) * $Holidays ;
          
     
     if(!empty($PaySalary)){
         
         $New = 1 ;
         
     }else{
         
         $New = 0 ;
     }
     
    $e=0;
    $PS=0;
    $PEX=0;
      foreach($Ratios as $r){

             if($r->Typee == 1){
                  
            if($r->From  <=  $xxxx  and $r->To  >=  $xxxx ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $xxxx ;   
           $e += $zz;    
                
            }else{
                
              $e+=0;      
                
            }   
                  
                  
              }

          
           if($r->Typee == 1){
                  
            if($r->From  <=  $s  and $r->To  >=  $s ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $s ;   
           $PS += $zz;    
                
            }else{
                
              $PS+=0;      
                
            }   
                  
                  
              }
          
          
           if($r->Typee == 2){
                  
            if($r->From  <=  $xx  and $r->To  >=  $xx ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $xx ;   
           $PEX += $zz;    
                
            }else{
                
              $PEX+=0;      
                
            }   
                  
                  
              }
                
          }
     
     



     
     
     $Allowances=AllowencesEmp::where('Emp',$Emp)->get()->sum('AmountAllow');
$Discounts=DiscountsEmp::where('Emp',$Emp)->get()->sum('AmountDiscount');

          $states=[];
 $states += ["Total" => $x ,'Salary' => $Sal->Salary , 'Sales' => $PS, 'Exec' =>$PEX , 'Dedu' =>$DED , 'Entit' =>$ENTIT , 'Loan' =>$xxx , 'Over' =>$OVER , 'HWork' =>$Sal->Hours_Numbers , 'Att' =>$Attendence  , 'AttDisc' =>$discT , 'Holi' =>$Holidays  , 'HoliDisc' =>$HoliDiscount,'New'=>$New,'Settle'=>$SETT,'later'=>$LATER,'Allowances'=>$Allowances,'Discounts'=>$Discounts,'Points'=>$point,'DiscountLate'=>$DiscountLate,'DiscountDeparture'=>$DiscountDeparture,'Shipping_Precent'=>$e];   


       return response()->json($states);
           
    }
    
     public function PostAddSalary(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Month'=>'required',
             'Resduial_Salary'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
               ],[

     
         ]);
         
                          $debt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Debitor');  
           $crdt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Creditor');  
            $dif=$debt - $crdt ;
            $SafyFatora=request('Resduial_Salary');
          


          if($dif < $SafyFatora){
             
              session()->flash('error',trans('admin.SafeNotEnoughMoney'));
              return back();
              
          }

         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=request('Month');
         $data['Salary']=request('Salary');
         $data['Pre_Sales']=request('Pre_Sales');
         $data['Pre_Execu']=request('Pre_Execu');
         $data['Deduction']=request('Deduction');
         $data['Entitlement']=request('Entitlement');
         $data['Borrow']=request('Borrow');
         $data['Overtime']=request('Overtime');
         $data['Attendence_Hours']=request('Attendence_Hours');
         $data['Attendence']=request('Attendence');
         $data['Loan']=request('Loan');
         $data['Holidays']=request('Holidays');
         $data['Resduial_Salary']=request('Resduial_Salary');
         $data['Note']=request('Note');
         $data['Draw']=request('Draw');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['Cost_Center']=request('Cost_Center');
         $data['Emp']=request('Emp');
         $data['Settlements']=request('Settlements');
         $data['Later_Sales_Bill']=request('Later_Sales_Bill');
         $data['Attendence_Discount']=request('Attendence_Discount');
         $data['Holiday_Discount']=request('Holiday_Discount');
         $data['Allowances']=request('Allowances');
         $data['Discounts']=request('Discounts');
         $data['ProducationPoints']=request('ProducationPoints');
         $data['DiscountLate']=request('DiscountLate');
         $data['DiscountDeparture']=request('DiscountDeparture');
         $data['Shipping_Precent']=request('Shipping_Precent');
          $data['User']=auth()->guard('admin')->user()->id;

         PaySalary::create($data);
  
    $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    
         
         
         
                    $c= DB::select("SELECT last_value FROM pay_salaries_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
         

             $AccDefff=AccountsDefaultData::orderBy('id','desc')->first();
      if($AccDefff->Salary == 2){
          
            $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'صرف راتب',
            'TypeEn' => 'Pay Salary',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Resduial_Salary');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Merit;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'صرف راتب';
        $Gen['TypeEn']='Pay Salary';
        $Gen['Debitor']=request('Resduial_Salary');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Resduial_Salary');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Merit;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Resduial_Salary');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
             $Gen['Type']= 'صرف راتب';
        $Gen['TypeEn']='Pay Salary';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Resduial_Salary');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Resduial_Salary');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);    
          
          
          
          
          
          
          
          
      }else{
         
         
         
        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'صرف راتب',
            'TypeEn' => 'Pay Salary',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Resduial_Salary');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Account_Emp;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'صرف راتب';
        $Gen['TypeEn']='Pay Salary';
        $Gen['Debitor']=request('Resduial_Salary');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Resduial_Salary');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Account_Emp;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Resduial_Salary');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
             $Gen['Type']= 'صرف راتب';
        $Gen['TypeEn']='Pay Salary';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Resduial_Salary');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Resduial_Salary');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
      }
          
         if(request('Loan') != 0){
             
             
              $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'قسط قرض',
            'TypeEn' =>'Loan Installment',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Loan'),
            'Total_Creditor' => request('Loan'),
            'Note' => request('Note'),
  
        )
    );
         
             
             
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Loan');
        $PRODUCTSS['Account']=$Emp->Covenant;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='قسط قرض';
        $Gen['TypeEn']='Loan Installment';
        $Gen['Debitor']=0;
        $Gen['Creditor']= request('Loan');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') *  request('Loan');
        $Gen['Account']=$Emp->Covenant;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Loan');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
              $Gen['Type']='قسط قرض';
        $Gen['TypeEn']='Loan Installment';
        $Gen['Debitor']=request('Loan');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Loan');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
             
             
         }
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الرواتب';
           $dataUser['ScreenEn']='Salaries';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
      public function DeletePaySalary($id){
                      
        $del=PaySalary::find($id);
         
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','صرف راتب')->delete();
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','قسط قرض')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','صرف راتب')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','قسط قرض')->delete();
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='الرواتب';
           $dataUser['ScreenEn']='Salaries';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

    
    //Exchange Commissions
    public function ExchangeCommissionsPage(){
        
          $CostCenters=CostCenter::all();
          
            $Coins=Coins::all();  
          
         
          $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
          
                $res=ExchangeCommissions::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
          
         return view('admin.HR.ExchangeCommissions',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
             'Code'=>$Code,
         ]);
          
    }
  
    public function EmpCheckCommision($Emp) {         
         
    $EXX=ExchangeCommissions::where('Emp',$Emp)->get()->sum('Amount');
  $returns=ReturnMaintainceBill::where('Eng',$Emp)->get();
     $employeee=Employess::find($Emp);
    $co=0;    
    $s=0;    
    $e=0;    
    $totalcost=0;    
     
        
       $default=SalesDefaultData::orderBy('id','desc')->first();

        foreach($returns as $ret){

        $i= $ret->Cost_Precent / 100 ;
        $ii= $i *  $ret->Total_Cost ;   
        $co += $ii ;
                
          }
        
            
        $sales=Sales::
          where('Status',1)
          ->where('Delegate',$Emp)
          ->where('Later_Collection',1)
              ->get()->sum('Total_Price'); 
        
        
                       $Ratios=EmpRatio::where('Emp',$Emp)->get();
               
        foreach($Ratios as $r){
                          if($r->Typee == 1){
                  
            if($r->From  <=  $sales  and $r->To  >=  $sales ){
            
             $x=$r->Rate  / 100 ;
            $xx= $x *  $sales ;   
           $s+=$xx;    
                
            }else{
                
              $s+=0;      
                
            }   
                  
                  
              }
        
        }

       $products=ProductSales::where('Executor',$Emp)->where('Later_Collection',1)->get();
       $Executors=ProductSales::where('Executor',$Emp)->where('Later_Collection',1)->get()->sum('Total');
        
        
   
      
       $Maintaince=ReciptMaintaince::where('Status',8)->where('Eng',$Emp)->where('Returned',0)->get()->sum('Total');
       $MaintaincProducts=ProductMaintaincBill::all();
        
          foreach($products as $pro){
           if($default->Execute_Precent == 'Servicee'){
               
               
           if($pro->Product()->first()->P_Type == 'Service'){
               
               
                 $plow=ProductUnits::where('Product',$pro->Product)->where('Rate',1)->first(); 
             $rr=ProductUnits::where('Product',$pro->Product)->where('Unit',$pro->Unit)->first(); 
     $purchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total_Bf_Tax');

         $countPurchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
            
                   $storesTransfer=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('SmallTrans_Qty');  
              
              
  $purchsStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total');
            
            
         $countStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');

              
             $OUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;         
              
               
          $Cost=$this->AverageCostGet($pro->Product,$plow->Barcode,$pro->Store);         
         if(!empty($purchs) or !empty($purchsStart)or !empty($storesTransfer) or !empty($OUTCOME) ){
                 
        if($Cost != 0){
            
            
            
        $Average = $Cost * $rr->Rate ; 
$totalcost += ($Average * $pro->Qty) ;
       }else{
                               
        $totalcost += ($Cost * $pro->Qty) * $rr->Rate;                    
     } 

             }else{
              
            $totalcost +=0; 
             }
           
              
              

              
              $Emppp=Employess::find($Emp);
              
if($Emppp->Emp_Type == 'Engineer'){              
                     $diff= $sales  - $totalcost ;
}else{
    
             $diff= $Executors  ;
}
              
                   $Ratios=EmpRatio::where('Emp',$Emp)->get();
     
          foreach($Ratios as $r){


             if($r->Typee == 2){
                  
            if($r->From  <=  $diff  and $r->To  >=  $diff ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $diff ;   
           $e += $zz;    
                
            }else{
                
              $e+=0;      
                
            }   
                  
                  
              }

                
          }

              
           }               
           }else{
               
               
               
             $plow=ProductUnits::where('Product',$pro->Product)->where('Rate',1)->first(); 
             $rr=ProductUnits::where('Product',$pro->Product)->where('Unit',$pro->Unit)->first(); 
     $purchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total_Bf_Tax');

         $countPurchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
            
                   $storesTransfer=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('SmallTrans_Qty');  
              
              
  $purchsStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total');
            
            
         $countStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');

              
             $OUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;         
                     $Cost=$this->AverageCostGet($pro->Product,$plow->Barcode,$pro->Store);   
         if(!empty($purchs) or !empty($purchsStart)or !empty($storesTransfer) or !empty($OUTCOME) ){
                 
        if($Cost != 0){
        $Average = $Cost * $rr->Rate ; 
$totalcost += ($Average * $pro->Qty) ;
       }else{
                               
        $totalcost += ($Cost * $pro->Qty) * $rr->Rate;                    
     } 

             }else{
              
            $totalcost +=0; 
             }
           
              
              

              
              $Emppp=Employess::find($Emp);
              
if($Emppp->Emp_Type == 'Engineer'){              
                     $diff= $sales  - $totalcost ;
}else{
    
             $diff= $Executors  ;
}
              
                   $Ratios=EmpRatio::where('Emp',$Emp)->get();
               
             
     
          foreach($Ratios as $r){

 

             if($r->Typee == 2){
                  
            if($r->From  <=  $diff  and $r->To  >=  $diff ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $diff ;   
           $e += $zz;    
                
            }else{
                
              $e+=0;      
                
            }   
                  
                  
              }

                
          }

              
            
            
        }   
        }   
        
          foreach($MaintaincProducts as $pro){
            if($pro->Maintaince()->first()->Eng == $Emp){
            if($pro->Maintaince()->first()->Status == 8){
            if($pro->Maintaince()->first()->Returned == 0){
                
            $plow=ProductUnits::where('Product',$pro->Product)->where('Rate',1)->first(); 
             $rr=ProductUnits::where('Product',$pro->Product)->where('Unit',$pro->Unit)->first();         
                
     $purchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total_Bf_Tax');

         $countPurchs=ProductsPurchases::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
            
                
                  $storesTransfer=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$pro->Store)->get()->sum('SmallTrans_Qty');  
                         
                
  $purchsStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('Total');
            
            
         $countStart=ProductsStartPeriods::where('Product',$pro->Product)->where('SmallCode',$plow->Barcode)->where('Store',$pro->Store)->get()->sum('SmallQty');
             
                
                $OUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$pro->Product)->where('Store',$pro->Store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;        
              
                     $Cost=$this->AverageCostGet($pro->Product,$plow->Barcode,$pro->Store);  
                
         if(!empty($purchs) or !empty($purchsStart) or !empty($storesTransfer) or !empty($OUTCOME)){
                 
        if($CollectCount != 0){
        $Average = $Cost * $rr->Rate ; 
$totalcost += $Average * $pro->Qty ;
       }else{
                               
        $totalcost += ($Cost * $pro->Qty) *  $rr->Rate;                    
     } 

             }else{
              
            $totalcost +=0; 
             }
                 
            }
            }
            }
            
        }   



            if($employeee->Bill_Num == 1){
       $lastEx=ExchangeCommissions::where('Emp',$Emp)->orderBy('id','desc')->first();
                
            if(!empty($lastEx)){    
           $MaintainceCount=ReciptMaintaince::where('Status',8)
               ->where('Date','>=',$lastEx->Date)
               ->where('Eng',$Emp)->count();
            }else{
           
                $MaintainceCount=ReciptMaintaince::where('Status',8)
               ->where('Eng',$Emp)->count();        
                
            }
                if($employeee->NumbersOfBill == $MaintainceCount){
                    
                  $diff= ($Executors + $Maintaince)  - $totalcost ;   
                    
                }else{
                  $diff= 0 ;      
                }
                    
                    
            }else{
              $diff= ($Executors + $Maintaince)  - $totalcost ;    
            }

     $Ratios=EmpRatio::where('Emp',$Emp)->get();
     
          foreach($Ratios as $r){

             if($r->Typee == 2){
                  
            if($r->From  <=  $diff  and $r->To  >=  $diff ){
            
             $z=$r->Rate  / 100 ;
            $zz= $z *  $diff ;   
           $e += $zz;    
                
            }else{
                
              $e+=0;      
                
            }   
                  
                  
              }

                
          }

 
          $states=[];
 $states += ['Sales' => $s, 'Exec' =>$e , 'Retu' =>$co , 'Ex' =>$EXX];   



       return response()->json($states);
           
    }
    

     public function PostExchangeCommissions(){
        
        $data= $this->validate(request(),[
             'Date'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',
             'Emp'=>'required',
             'Coin'=>'required',
               ],[

     
         ]);

         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Month']=null;
         $data['Note']=request('Note');
         $data['Draw']=request('Draw');
         $data['Amount']=request('Amount');
         $data['Commision']=request('Commision');
         $data['Total_Exchange_Commision']=request('Total_Exchange_Commision');
         $data['Pre_Sales']=request('Pre_Sales');
         $data['Pre_Execu']=request('Pre_Execu');
         $data['Return_Maintaince']=request('Return_Maintaince');
         $data['Safe']=request('Safe');
         $data['Coin']=request('Coin');
         $data['Cost_Center']=request('Cost_Center');
         $data['Emp']=request('Emp');
         $data['User']=auth()->guard('admin')->user()->id;;
         ExchangeCommissions::create($data);
  
    $res=Journalizing::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    
         
         
                    $c= DB::select("SELECT last_value FROM exchange_commissions_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   
         
         
         
                   $AccDefff=AccountsDefaultData::orderBy('id','desc')->first();

          if($AccDefff->Commission == 2){
              
              
                  
              
                   $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'صرف عمولات',
            'TypeEn' => 'Exchange Commissions',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Merit;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='صرف عمولات';
        $Gen['TypeEn']='Exchange Commissions';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Merit;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
            $Gen['Type']='صرف عمولات';
        $Gen['TypeEn']='Exchange Commissions';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);        
              
              
              
              
          }else{

        $ID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'صرف عمولات',
            'TypeEn' => 'Exchange Commissions',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),
  
        )
    );
         
        
         $Emp=Employess::find(request('Emp'));  
          
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Emp->Commission;
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='صرف عمولات';
        $Gen['TypeEn']='Exchange Commissions';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Emp->Commission;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
            $Gen['Type']='صرف عمولات';
        $Gen['TypeEn']='Exchange Commissions';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
          }
          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='صرف عموله';
           $dataUser['ScreenEn']='Exchange Commissions';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
    public function ExchangeCommissionsSechdule(){
        $items=ExchangeCommissions::paginate(100);
         return view('admin.HR.ExchangeCommissionsSechules',[
             'items'=>$items,
         ]);
    }
    
        public function DeleteCommission($id){
                      
        $del=ExchangeCommissions::find($id);
         
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','صرف عمولات')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','صرف عمولات')->delete();
      
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
          $dataUser['Screen']='صرف عموله';
           $dataUser['ScreenEn']='Exchange Commissions';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    //ResignationRequest
        public function ResignationRequest(){
        $items=ResignationRequest::where('Emp',auth()->guard('admin')->user()->emp)->paginate(100);

        $res=ResignationRequest::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
          
         return view('admin.HR.ResignationRequest',[
             'items'=>$items,
             'Code'=>$Code,
         ]);
    }
    
       public function AddResignationRequest(){
        
        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             
             
               ],[
            
         ]);
    
            $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='Resignation_DateImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=null;
             }
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Resignation_Date']=request('Resignation_Date');
         $data['Emp']=auth()->guard('admin')->user()->emp;
         $data['Note']=request('Note');
         $data['Status']=0;
         $data['Reason']=null;
       

         ResignationRequest::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='طلب استقاله';
           $dataUser['ScreenEn']='Resignation Request';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditResignationRequest($id){
        
        $data= $this->validate(request(),[

             'Date'=>'required',
   
             
               ],[
            
         ]);
    
       $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='Resignation_DateImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=request('Files');
             }
           
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Resignation_Date']=request('Resignation_Date');
         $data['Emp']=auth()->guard('admin')->user()->emp;
         $data['Note']=request('Note');
         $data['Status']=request('Status');
         $data['Reason']=request('Reason');
       

         ResignationRequest::where('id',$id)->update($data);
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='طلب استقاله';
           $dataUser['ScreenEn']='Resignation Request';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }
    
      public function DeleteResignationRequest($id){
                      
        $del=ResignationRequest::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='طلب استقاله';
           $dataUser['ScreenEn']='Resignation Request';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 

       public function ResignationRequestSechdule(){
        $items=ResignationRequest::where('Status',0)->paginate(100);

         return view('admin.HR.ResignationRequestSechdule',[
             'items'=>$items,
         ]);
    }
    
    
          public function AcceptResignation($id){
                      
        ResignationRequest::where('id',$id)->update(['Status'=>1]);

        session()->flash('success',trans('admin.Accepted'));
        return back();

           } 
    
    
            public function RefuseResignation(){
                      $id=request('ID');
        ResignationRequest::where('id',$id)->update(['Status'=>2,'Reason'=>request('Reason')]);

        session()->flash('success',trans('admin.Refused'));
        return back();

           } 

    
//Disclaimer
         public function Disclaimer(){
      
             $EMps=Employess::where('EmpSort',1)->where('Active',1)->get();
             $items=Disclaimer::paginate(100);
          
         return view('admin.HR.Disclaimer',[
             'EMps'=>$EMps,
             'items'=>$items,

         ]);
    }
    
           public function AddDisclaimer(){
        
        $data= $this->validate(request(),[
  
             'Date'=>'required',
             'Note'=>'required',
             
             
               ],[
            
         ]);
    
            $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='DisclaimerImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=null;
             }
           

         $data['Date']=request('Date');
         $data['Emp']=request('Emp');
         $data['Note']=request('Note');


         Disclaimer::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
     
               $dataUser['Screen']='اخلاء طرف';
           $dataUser['ScreenEn']='Disclaimer';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Date');
           $dataUser['ExplainEn']=request('Date');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditDisclaimer($id){
        
        $data= $this->validate(request(),[
  
             'Date'=>'required',
    
         'Note'=>'required',
             
               ],[
            
         ]);
    
       $image=request()->file('File');
          if($image){            
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='DisclaimerImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);            
                   }
        
    
             if(!empty($image_url)){
       
                 $data['File']=$image_url; 
                 
             }else{
                 $data['File']=request('Files');
             }
           

         $data['Date']=request('Date');
    
             $data['Emp']=request('Emp');
         $data['Note']=request('Note');

       

         Disclaimer::where('id',$id)->update($data);
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='اخلاء طرف';
           $dataUser['ScreenEn']='Disclaimer';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Date');
           $dataUser['ExplainEn']=request('Date');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
             return back();
        
    }
    
      public function DeleteDisclaimer($id){
                      
        $del=Disclaimer::find($id);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='اخلاء طرف';
           $dataUser['ScreenEn']='Disclaimer';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Note;
            $dataUser['ExplainEn']=$del->Note;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           } 


    public function PrintDisclaimer($id){
             $disc=Disclaimer::find($id);         
       $item=Employess::find($disc->Emp);
  return view('admin.HR.PrintDisclaimer',['item'=>$item,'disc'=>$disc]);

           }
    


    


    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Virables extends Model
{
    use HasFactory;
             protected $table = 'virables';
      protected $fillable = [
        'Name',
        'NameEn',
    ];
    
            public function SubVirables()
    {
        return $this->hasOne(SubVirables::class);
    }
    
    
}

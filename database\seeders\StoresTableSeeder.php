<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StoresTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('stores')->delete();
        
        \DB::table('stores')->insert(array (
            0 => 
            array (
                'id' => 4,
                'Date' => '2021-06-01',
                'Time' => '05:51:42 am',
                'Name' => 'المخزون',
                'Phone' => NULL,
                'Address' => NULL,
                'Account' => 36,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => '2022-10-18 22:34:49',
                'Code' => '2',
                'Branch' => 3,
                'Letter' => NULL,
                'Account_Client' => '218',
            ),
            1 => 
            array (
                'id' => 18,
                'Date' => '2022-05-22',
                'Time' => '10:14:10 am',
                'Name' => 'Store',
                'Phone' => NULL,
                'Address' => NULL,
                'Account' => 831,
                'User' => 11,
                'created_at' => '2022-05-22 10:14:10',
                'updated_at' => '2022-10-18 22:34:54',
                'Code' => '3',
                'Branch' => 3,
                'Letter' => NULL,
                'Account_Client' => NULL,
            ),
        ));
        
        
    }
}
<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\StoresMoves;
use DB;
class ExportStoresMovesReport implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      

        $storex=$this->store;
  
         $from=$storex['from'];
         $to=$storex['to'];
         $branch=$storex['branch'];
         $storee=$storex['store'];
         $safe=$storex['safe'];
         $account=$storex['account'];
         $type=$storex['type'];
         $user=$storex['user'];
         $shipping_Company=$storex['shipping_Company'];
         $cost_Center=$storex['cost_Center'];
         $coin=$storex['coin'];
         $sadr=$storex['sadr'];
         $ward=$storex['ward'];
        
        
      
              if(app()->getLocale() == 'ar' ){    
        $prods = DB::table('stores_moves')->whereBetween('stores_moves.Date',[$from,$to])
            
               ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('stores_moves.Branch',$branch);
    })
       

                 ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('stores_moves.Store', $storee);
    })
       
                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('stores_moves.Safe', $safe);
    })   
       
       
                   ->when(!empty($type), function ($query) use ($type) {
        return $query->whereIn('stores_moves.Type', $type);
    })  
       
       
                   ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('stores_moves.Cost_Center', $cost_Center);
    })  
       
       
       
                   ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('stores_moves.User', $user);
    }) 
       
                        ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('stores_moves.Coin', $coin);
    })  
       
       
                        ->when(!empty($account), function ($query) use ($account) {
        return $query->whereIn('stores_moves.Account', $account);
    })  
       
       
                        ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('stores_moves.Ship', $shipping_Company);
    })   
            
            ->leftJoin('stores', function ($join) {
    
            $join->on('stores_moves.Store', '=', 'stores.id');
        })
                  
                   ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
        
         
                    ->leftJoin('safes_banks', function ($join) {
    
            $join->on('stores_moves.Safe', '=', 'safes_banks.Account');
        })
             
               ->leftJoin('cost_centers', function ($join) {
    
            $join->on('stores_moves.Cost_Center', '=', 'cost_centers.id');
        })
            
               ->leftJoin('admins', function ($join) {
    
            $join->on('stores_moves.User', '=', 'admins.id');
        })
            
               ->leftJoin('coins', function ($join) {
    
            $join->on('stores_moves.Coin', '=', 'coins.id');
        })
        
    ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('stores_moves.Account', '=', 'acccounting_manuals.id');
        })





->select('stores_moves.Date'
         ,'stores_moves.Code'
         ,'stores_moves.Time'
         ,'branches.Arabic_Name as BranchName'
         ,'stores.Name as StoreName'
         ,'safes_banks.Name as SafeName'
          ,'stores_moves.Type'
         ,'cost_centers.Arabic_Name as CostName'
         ,'admins.name as UserName'
         ,'coins.Arabic_Name as CoinName'
               ,'stores_moves.Note' 
               ,'stores_moves.Total_Qty' 
               ,'stores_moves.Total_Price' 
              ,'acccounting_manuals.Name as AccountName' 

        )
                  ->get(); 
              }else{
                  
                  
               $prods = DB::table('stores_moves')->whereBetween('stores_moves.Date',[$from,$to])
            
               ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('stores_moves.Branch',$branch);
    })
       

                 ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('stores_moves.Store', $storee);
    })
       
                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('stores_moves.Safe', $safe);
    })   
       
       
                   ->when(!empty($type), function ($query) use ($type) {
        return $query->whereIn('stores_moves.Type', $type);
    })  
       
       
                   ->when(!empty($cost_Center), function ($query) use ($cost_Center) {
        return $query->where('stores_moves.Cost_Center', $cost_Center);
    })  
       
       
       
                   ->when(!empty($user), function ($query) use ($user) {
        return $query->whereIn('stores_moves.User', $user);
    }) 
       
                        ->when(!empty($coin), function ($query) use ($coin) {
        return $query->where('stores_moves.Coin', $coin);
    })  
       
       
                        ->when(!empty($account), function ($query) use ($account) {
        return $query->whereIn('stores_moves.Account', $account);
    })  
       
       
                        ->when(!empty($shipping_Company), function ($query) use ($shipping_Company) {
        return $query->whereIn('stores_moves.Ship', $shipping_Company);
    })   
            
            ->leftJoin('stores', function ($join) {
    
            $join->on('stores_moves.Store', '=', 'stores.id');
        })
                  
                   ->leftJoin('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
        
         
                    ->leftJoin('safes_banks', function ($join) {
    
            $join->on('stores_moves.Safe', '=', 'safes_banks.Account');
        })
             
               ->leftJoin('cost_centers', function ($join) {
    
            $join->on('stores_moves.Cost_Center', '=', 'cost_centers.id');
        })
            
               ->leftJoin('admins', function ($join) {
    
            $join->on('stores_moves.User', '=', 'admins.id');
        })
            
               ->leftJoin('coins', function ($join) {
    
            $join->on('stores_moves.Coin', '=', 'coins.id');
        })
        
    ->leftJoin('acccounting_manuals', function ($join) {
    
            $join->on('stores_moves.Account', '=', 'acccounting_manuals.id');
        })





->select('stores_moves.Date'
         ,'stores_moves.Code'
         ,'stores_moves.Time'
         ,'branches.English_Name as BranchName'
         ,'stores.NameEn as StoreName'
         ,'safes_banks.NameEn as SafeName'
          ,'stores_moves.Type'
         ,'cost_centers.English_Name as CostName'
         ,'admins.nameEn as UserName'
         ,'coins.English_Name as CoinName'
               ,'stores_moves.Note' 
               ,'stores_moves.Total_Qty' 
               ,'stores_moves.Total_Price' 
              ,'acccounting_manuals.NameEn as AccountName' 

        )
                  ->get();       
                  
                  
              }

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Code',
          'Time',
          'Branch',
          'Store',
          'Safe',
          'Type',
          'Cost_Center',
          'User',
          'Coin',
          'Note',
          'Total_Qty',
          'Total_Price',
          'Account',
        ];
    }
    
    
    

}

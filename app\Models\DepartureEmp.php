<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DepartureEmp extends Model
{
    use HasFactory;
      protected $table = 'departure_emps';
      protected $fillable = [
        'In_Time',
        'Out_Time',
        'Hours_Number',
        'Date',
        'Month',
        'Note',
        'Departure',
        'Disc_Late',
        'Disc_Early',
        'Emp',

       
    ];
    
        public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
          public function Departure()
    {
        return $this->belongsTo(Departure::class,'Departure');
    }
}

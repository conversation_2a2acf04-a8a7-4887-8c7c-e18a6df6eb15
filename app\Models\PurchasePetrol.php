<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchasePetrol extends Model
{
    use HasFactory;
       protected $table = 'purchase_petrols';
      protected $fillable = [
        'Code',
        'Date',
        'Later_Due',
        'Vendor_Bill_Date',
        'Refernce_Number',
        'Draw',
        'Number',
        'Payment_Method',
        'Note',
        'File',
        'Car_Number',
        'Delivery_Method',
        'Product_Numbers',
        'Total_Qty',
        'Total_Price',
        'Pay',
        'Full',
        'Vendor',
        'Coin',
        'Safe',
        'CompanyCar',
        'Store',
        'Recipient',
        'User',
       
    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }

          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
            public function CompanyCar()
    {
        return $this->belongsTo(CompanyCars::class,'CompanyCar');
    }

}

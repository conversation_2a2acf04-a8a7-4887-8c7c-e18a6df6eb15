<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductSales;
use DB;
class ExportDelegateSalesDetails implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->store;
        
  
        
         $storee=$storex['store'];
         $from=$storex['from'];
         $to=$storex['to'];
         $delegate=$storex['delegate'];
         $branch=$storex['branch'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $payment_Method=$storex['payment_Method'];
   

    
         if(app()->getLocale() == 'ar' ){ 
          $prods = DB::table('product_sales')->whereBetween('product_sales.Date',[$from,$to])
              
                     ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('product_sales.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('product_sales.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('product_sales.Branch', $branch);
    })  
       
                            ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('product_sales.Store', $storee);
    }) 
       
                                ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('product_sales.Payment_Method', $payment_Method);
    }) 
       
        ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('product_sales.Delegate', $delegate);
    })   
                  
              
                     ->join('acccounting_manuals', function ($join) {
    
            $join->on('product_sales.Client', '=', 'acccounting_manuals.id');
        })
              
               ->join('employesses', function ($join) {
    
            $join->on('product_sales.Delegate', '=', 'employesses.id');
        })      
              
                ->join('stores', function ($join) {
    
            $join->on('product_sales.Store', '=', 'stores.id');
        })
              
                   ->join('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
              
              
                  ->join('products', function ($join) {
    
            $join->on('product_sales.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           
        
->select('product_sales.Date'
         ,'product_sales.P_Ar_Name'
         ,'product_sales.Product_Code'
         ,'product_sales.Qty'
         ,'product_sales.Price'
         ,'product_sales.Total'
         ,'stores.Name as StoreName'
         ,'acccounting_manuals.Name as Client'
         ,'employesses.Name as Emp'
         ,'branches.Arabic_Name as Branch'
         ,'items_groups.Name as Group'
         ,'brands.Name as Brand'
         ,'product_sales.Payment_Method'
        )
                  ->get();
         }else{
           
                      $prods = DB::table('product_sales')->whereBetween('product_sales.Date',[$from,$to])
              
                     ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('product_sales.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('product_sales.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('product_sales.Branch', $branch);
    })  
       
                            ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('product_sales.Store', $storee);
    }) 
       
                                ->when(!empty($payment_Method), function ($query) use ($payment_Method) {
        return $query->whereIn('product_sales.Payment_Method', $payment_Method);
    }) 
       
        ->when(!empty($delegate), function ($query) use ($delegate) {
        return $query->whereIn('product_sales.Delegate', $delegate);
    })   
                  
              
                     ->join('acccounting_manuals', function ($join) {
    
            $join->on('product_sales.Client', '=', 'acccounting_manuals.id');
        })
              
               ->join('employesses', function ($join) {
    
            $join->on('product_sales.Delegate', '=', 'employesses.id');
        })      
              
                ->join('stores', function ($join) {
    
            $join->on('product_sales.Store', '=', 'stores.id');
        })
              
                   ->join('branches', function ($join) {
    
            $join->on('stores.Branch', '=', 'branches.id');
        })
              
              
                  ->join('products', function ($join) {
    
            $join->on('product_sales.Product', '=', 'products.id');
        })
            
                ->join('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
        
         
               ->leftJoin('brands', 'products.Brand', '=', 'brands.id')      
           
        
->select('product_sales.Date'
         ,'product_sales.P_En_Name'
         ,'product_sales.Product_Code'
         ,'product_sales.Qty'
         ,'product_sales.Price'
         ,'product_sales.Total'
         ,'stores.NameEn as StoreName'
         ,'acccounting_manuals.NameEn as Client'
         ,'employesses.NameEn as Emp'
         ,'branches.English_Name as Branch'
         ,'items_groups.NameEn as Group'
         ,'brands.NameEn as Brand'
         ,'product_sales.Payment_Method'
        )
                  ->get();
             
             
         }
        
        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Date',
          'Name',
          'Code',
          'Qty',
          'Price',
          'Total',
          'Store',
          'Client',
          'Delegate',
          'Branch',
          'Group',
          'Brand',
          'Payment_Method'
        ];
    }
    
    
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantMenuStyle extends Model
{
    use HasFactory;
         protected $table = 'resturant_menu_styles';
      protected $fillable = [
        'Menu_1_BG_Type',
        'Menu_1_BG_Image',
        'Menu_1_BG_Color',
        'Menu_1_Image',
        'Menu_1_Icon_Image',
        'Menu_1_Title',
        'Menu_1_Bar_BG',
        'Menu_1_Bar_Color',
        'Menu_1_Btn_BG',
        'Menu_1_Btn_Color',
        'Menu_1_Icon_Color',
        'Menu_1_Icon_Hover_Color',
        'Menu_1_Icon_Active_Color',
        'Menu_1_Price_Color',
        'Menu_1_Offer_Price_Color',
          
        'Menu_2_Gallery_Border',
        'Menu_2_BG_Type',
        'Menu_2_BG_Image',
        'Menu_2_BG_Color',
        'Menu_2_Title',
        'Menu_2_Category_Color',
        'Menu_2_Category_Hover_Color',
        'Menu_2_Category_Active_Color',
        'Menu_2_Lines_Color', 
        'Menu_2_Btn_BG', 
        'Menu_2_Btn_Color', 
        'Menu_2_Name_BG', 
        'Menu_2_Name_Color', 
        'Menu_2_Icon_Color', 
        'Menu_2_Icon_Hover_Color', 
        'Menu_2_Icon_Active_Color', 
        'Menu_2_Price_Color', 
        'Menu_2_Offer_Price_Color', 
          
        'Menu_3_BG_Type', 
        'Menu_3_BG_Image', 
        'Menu_3_BG_Color', 
        'Menu_3_Image', 
        'Menu_3_Icon_Image', 
        'Menu_3_Title', 
        'Menu_3_Category_Color', 
        'Menu_3_Category_Hover_Color', 
        'Menu_3_Category_Active_Color', 
        'Menu_3_Box_BG', 
        'Menu_3_Box_Color', 
        'Menu_3_Btn_BG', 
        'Menu_3_Btn_Color', 
        'Menu_3_Icon_Color', 
        'Menu_3_Icon_Hover_Color', 
        'Menu_3_Icon_Active_Color', 
        'Menu_3_Price_Color', 
        'Menu_3_Offer_Price_Color', 
     
       
    ];
}

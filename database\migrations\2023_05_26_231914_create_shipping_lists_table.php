<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingListsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_lists', function (Blueprint $table) {
            $table->id();
            
            $table->longText('Ticket_Store')->nullable();
			$table->longText('Driver')->nullable();
			$table->longText('Car_Store')->nullable();
            $table->longText('Travel_Area')->nullable();
            $table->longText('Access_Area')->nullable();
            $table->longText('Date_Travel')->nullable();
            $table->longText('Date_Arrival')->nullable();
            $table->longText('Car_Number')->nullable();
			$table->longText('Code')->nullable();
			$table->longText('Date')->nullable();
			$table->longText('Total_Cash')->nullable();
			$table->longText('Total_Later')->nullable();
			$table->longText('Total_Price')->nullable();
			$table->longText('Tickets_Numbers')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_lists');
    }
}

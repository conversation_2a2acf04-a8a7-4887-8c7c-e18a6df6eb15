<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingRequest extends Model
{
    use HasFactory;
      protected $table = 'manufacturing_requests';
      protected $fillable = [
        'Code',
        'Date',
        'Recived_Date',
        'Client_Phone',
        'Client_Address',
        'Client',
        'Delegate',
        'Store',
        'Delegate_Phone',
        'Payment_Method',
        'Later_Due',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Note',
        'Status',
        'User',
    ];

    
       public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
    
    public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
 
}

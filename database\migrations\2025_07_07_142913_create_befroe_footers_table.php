<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBefroeFootersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('befroe_footers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Image');
            $table->string('Arabic_Title');
            $table->string('Arabic_Desc');
            $table->string('English_Title');
            $table->string('English_Desc');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('befroe_footers');
    }
}
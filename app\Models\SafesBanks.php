<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SafesBanks extends Model
{
    use HasFactory;
          protected $table = 'safes_banks';
      protected $fillable = [
        'Code',
        'Date',
        'Name',
        'NameEn',
        'Type',
        'Note',
        'Account',
        'Branch',
        'User',
        'Service_Fee',

       
    ];
    
        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
           public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
              public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    


}

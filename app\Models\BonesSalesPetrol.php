<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BonesSalesPetrol extends Model
{
    use HasFactory;
     protected $table = 'bones_sales_petrols';
      protected $fillable = [
        'Bone_Amount',
        'Bone',       
        'SalesPetrol',       
    ];



            public function Bone()
    {
        return $this->belongsTo(BonesType::class,'Bone');
    }
    
             public function SalesPetrol()
    {
        return $this->belongsTo(SalesPetrol::class,'SalesPetrol');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantIndexStyleI extends Model
{
    use HasFactory;
          protected $table = 'resturant_index_style_i_s';
      protected $fillable = [
        'Home_1_Nxt_Prev_Color',
        'Home_1_Nxt_Prev_BG',
        'Home_1_Nxt_Prev_Border',
          
        'Home_2_Image_1',
        'Home_2_Image_2',
        'Home_2_Image_Arrow',
        'Home_2_Number_BG',
        'Home_2_Image_Border',
        'Home_2_Number_Color',
          
        'Category_Bar_BG',
        'Category_Bar_Color',
          
          
        'Video_Section_Body_BG_Type',
        'Video_Section_Body_BG_Image',
        'Video_Section_Body_BG_Color',
        'Video_Section_Image_1',
        'Video_Section_Image_2',
          
        'Menu_Index_Body_BG_Type',
        'Menu_Index_Body_BG_Image',
        'Menu_Index_Body_BG_Color',
        'Menu_Index_Txt_Color',
        'Menu_Index_Txt_Hover_Color',
        'Menu_Index_Btn_BG',
        'Menu_Index_Btn_Txt_Color',
        'Icon_Image',
     
          
  
       
    ];
}

<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductUnits;
use App\Models\ProductsQty;
use DB ;
class ExportInventorySerial implements FromCollection ,WithHeadings 
{
 
    
     private $Group=[] ;

    public function __construct($Group=0) 
    {
        $this->Group = $Group;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->Group;

         $group=$storex['Group'];
         $brand=$storex['Brand'];
         $product_Name=$storex['Product_Name'];
         $product_Code=$storex['Product_Code'];


        
          if(app()->getLocale() == 'ar' ){
             $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
   
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
                    ->join('products', function ($join) {
    
            $join->on('products_qties.Product', '=', 'products.id')->where('products.P_Type','=','Serial');
        })
              
                 
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
                 
                  ->leftJoin('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
 
                             ->leftJoin('brands', function ($join) {
    
            $join->on('products.Brand', '=', 'brands.id');
        })
         ->orderBy('products_qties.Product','asc')           
->select('products_qties.P_Ar_Name'
         ,'products_qties.P_Code'
         ,'items_groups.Name as GroupName'
         ,'brands.Name as BrandName'
         ,'products_qties.Qty'
         ,'stores.Name as StoreName'
        )
              
                  ->get();    
        
          }else{
              
              
                 $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
              
                ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
   
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })    
              
                    ->join('products', function ($join) {
    
            $join->on('products_qties.Product', '=', 'products.id')->where('products.P_Type','=','Serial');
        })
              
                 
            ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })
                 
                  ->leftJoin('items_groups', function ($join) {
    
            $join->on('products.Group', '=', 'items_groups.id');
        })
 
                             ->leftJoin('brands', function ($join) {
    
            $join->on('products.Brand', '=', 'brands.id');
        })
         ->orderBy('products_qties.Product','asc')           
->select('products_qties.P_En_Name'
         ,'products_qties.P_Code'
         ,'items_groups.NameEn as GroupName'
         ,'brands.NameEn as BrandName'
         ,'products_qties.Qty'
         ,'stores.NameEn as StoreName'
        )
              
                  ->get();    
          
              
              
              
          }

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Product_Name',
          'Product_Code',
          'Group',
          'Brand',
          'Qty',
          'Store',

        
        
        ];
    }
    
    
    

}

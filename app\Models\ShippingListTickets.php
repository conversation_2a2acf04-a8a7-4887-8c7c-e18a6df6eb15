<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingListTickets extends Model
{
    use HasFactory;
       protected $table = 'shipping_list_tickets';
      protected $fillable = [

                'Shipping_List',
                'Code',
                'Sender_Name',
                'Addressees_Name',
                'Total_Qty',
                'Total_Price',
                'Payment_Method',
                'Notes',
                'Ticket',
          
            
    ];
    
    
        public function Shipping_List()
    {
        return $this->belongsTo(ShippingList::class,'Shipping_List');
    }
    
            public function Ticket()
    {
        return $this->belongsTo(Ticket::class,'Ticket');
    }
  
    

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Projects extends Model
{
    use HasFactory;
          protected $table = 'projects';
      protected $fillable = [

                'Name',
                'NameEn',
                'Start_Date',
                'End_Date',
                'Duration',
                'Value',
                'File',
                'Client',
                'Manager',
                'Status',
                'User',
            

    ];
    
        public function Client()
    {
        return $this->belongsTo(Customers::class,'Client');
    }
    
           public function Manager()
    {
        return $this->belongsTo(Employess::class,'Manager');
    }
    
           public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}

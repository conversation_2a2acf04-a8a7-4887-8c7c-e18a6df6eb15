<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProductTypeDefaultsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('product_type_defaults')->delete();
        
        \DB::table('product_type_defaults')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Type' => 'Completed',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            1 => 
            array (
                'id' => 2,
                'Type' => 'Raw',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            2 => 
            array (
                'id' => 3,
                'Type' => 'Service',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            3 => 
            array (
                'id' => 4,
                'Type' => 'Subscribe',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            4 => 
            array (
                'id' => 5,
                'Type' => 'Assembly',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            5 => 
            array (
                'id' => 6,
                'Type' => 'Industrial',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            6 => 
            array (
                'id' => 7,
                'Type' => 'Single_Variable',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            7 => 
            array (
                'id' => 8,
                'Type' => 'Duble_Variable',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
            8 => 
            array (
                'id' => 9,
                'Type' => 'Serial',
                'created_at' => '2022-04-13 02:47:56',
                'updated_at' => '2022-04-13 02:47:56',
            ),
        ));
        
        
    }
}
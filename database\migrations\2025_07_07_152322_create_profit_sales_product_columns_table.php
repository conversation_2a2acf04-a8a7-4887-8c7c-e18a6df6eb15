<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProfitSalesProductColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('profit_sales_product_columns', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Product_Code')->nullable();
            $table->string('Product_Name')->nullable();
            $table->string('Unit')->nullable();
            $table->string('Qty')->nullable();
            $table->string('Price')->nullable();
            $table->string('Cost')->nullable();
            $table->string('Profit')->nullable();
            $table->string('Store');
            $table->string('Branch');
            $table->string('Group')->nullable();
            $table->string('Brand')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('profit_sales_product_columns');
    }
}
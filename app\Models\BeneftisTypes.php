<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeneftisTypes extends Model
{
    use HasFactory;
      protected $table = 'beneftis_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
                            ];
    
    
                public function Entitlement()
    {
        return $this->hasOne(Entitlement::class);
    }
    
    
}

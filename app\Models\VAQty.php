<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VAQty extends Model
{
    use HasFactory;
          protected $table = 'v_a_qties';
      protected $fillable = [
        'Qty',
        'MainV',
        'SubV',
        'Product',
        'ProductID',
        'VAProductID',
        
    ];
    
       
                public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }

           
                public function ProductID()
    {
        return $this->belongsTo(Products::class,'ProductID');
    }
           
                public function MainV()
    {
        return $this->belongsTo(Virables::class,'MainV');
    }
    
                   public function SubV()
    {
        return $this->belongsTo(SubVirables::class,'SubV');
    }
    
    
                   public function VAProductID()
    {
        return $this->belongsTo(VAProducts::class,'VAProductID');
    }
    
}

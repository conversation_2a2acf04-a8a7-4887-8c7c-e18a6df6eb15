<?php

namespace App\Http\Middleware;
use Closure;
use Auth;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate 
{
    public function handle($request, Closure  $next=null,$guard=null)
    {
         if(Auth::guard($guard)->check()){  
             
               return $next($request);
               return redirect('OstAdmin');
        
                    }else{

             return redirect('AdminLogin');
                      }

      
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventory extends Model
{
    use HasFactory;
             protected $table = 'inventories';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Note',
        'Total_Dificit',
        'Total_Excess',
        'Total_Dificit_Price',
        'Total_Excess_Price',
        'Account_Excess',
        'Account_Dificit',
        'Store',
        'Coin',
        'User',
        'Settle',
   
    ];
    
           public function Account_Excess()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Excess');
    }
    
              public function Account_Dificit()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Dificit');
    }
    
              public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
    
        public function ProductInventory()
    {
        return $this->hasOne(ProductInventory::class);
    }
    
           public function Settlement()
    {
        return $this->hasOne(Settlement::class);
    }
    
    
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Competitors extends Model
{
    use HasFactory;
          protected $table = 'competitors';
      protected $fillable = [
        'Name',
        'NameEn',
        'Website',
        'Facebook',
        'Instagram',
        'Twitter',
        'Pinterest',
        'Addtional_Link',
        'Phone',
        'Whatsapp',
        'Country',
        
      
    ];
    
             public function Country()
    {
        return $this->belongsTo(Countris::class,'Country');
    }
}

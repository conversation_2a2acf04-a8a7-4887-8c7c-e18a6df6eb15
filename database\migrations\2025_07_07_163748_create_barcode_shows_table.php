<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBarcodeShowsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('barcode_shows', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Company_Name')->nullable();
            $table->string('Product_Name')->nullable();
            $table->string('Product_Price')->nullable();
            $table->string('Unit')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Group')->nullable();
            $table->timestamps();
            $table->string('Code')->nullable();
            $table->string('Logo')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('barcode_shows');
    }
}
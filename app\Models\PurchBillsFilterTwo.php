<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchBillsFilterTwo extends Model
{
    use HasFactory;
       protected $table = 'purch_bills_filter_twos';
      protected $fillable = [
        'Code',
        'Date',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'Vendor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'Ship',
        'Later_Due',
        'Time',
        'Branch',
        'CustomerGroup',
        'Type',
        'ID',
    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
   
    
                  public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
    
  
    
         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
    
           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\SecretariatStores;
use App\Models\SecretariatQty;
use App\Models\SecretariatImportGoods;
use App\Models\ProductsSecretariatImportGoods;
use App\Models\SecretariatExportGoods;
use App\Models\ProductsSecretariatExportGoods;
use App\Models\AcccountingManual;
use App\Models\Brands;
use App\Models\Stores;
use App\Models\ProductsQty;
use App\Models\Coins;
use App\Models\ItemsGroups;
use App\Models\Measuerments;
use App\Models\Products;
use App\Models\ProductUnits;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\ProductsPurchases;
use App\Models\ProductsStoresTransfers;
use App\Models\ProductsStartPeriods;
use App\Models\OutcomManufacturingModel;
use App\Models\ProductMoves;
use App\Models\ManufacturingHalls;
use App\Models\ManufacturingSecretariatModel;
use App\Models\OutcomeManufacturingSecretariatModel;
use App\Models\IncomManufacturingSecretariatModel;
use App\Models\CostCenter;
use App\Models\DefaultDataShowHide;
use App\Models\ExecutingReceivingSecretariat;
use App\Models\ProductExecutingReceivingSecretariat;
use App\Models\Branches;
use App\Models\ManuStoreCount;
use App\Models\StoresMoves;
use DB;
use Str;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

  
   
class SecretariatController extends Controller
{
    
    function __construct()
{

$this->middleware('permission:مخازن الامانات', ['only' => ['Secretariat_StoresPage','AddSecretariat_Stores','EditSecretariat_Stores','DeleteSecretariat_Stores']]);
$this->middleware('permission:وارد بضاعه الامانات', ['only' => ['Secretariat_Import_goodsPage']]);
$this->middleware('permission:جدول وارد بضاعه الامانات', ['only' => ['Secretariat_Import_goods_Sechdule']]);
$this->middleware('permission:صرف بضاعه امانات', ['only' => ['Secretariat_Export_goodsPage']]);
$this->middleware('permission:جدول صرف بضاعه الامانات', ['only' => ['Secretariat_Export_goods_Sechdule']]);
$this->middleware('permission:كميات مخازن الامانات', ['only' => ['Secretariat_Stores_Qty']]);
$this->middleware('permission:نموذج تصنيع للغير', ['only' => ['ManufacturingModelSecretariat']]);
$this->middleware('permission:جدول نماذج تصنيع للغير', ['only' => ['ManufacturingModelSecretariatSechdule']]);
$this->middleware('permission:نموذج تصنيع للغير', ['only' => ['ManufacturingModelSecretariatPrecent']]);
$this->middleware('permission:تنفيذ و استلام للغير', ['only' => ['ExecutingReceivingSecretariat']]);
        

}  
    
    //Secretariat Stores

    public function Secretariat_StoresPage(){
        $items=SecretariatStores::all();
         return view('admin.Secretariat.SecretariatStores',['items'=>$items]);
    }
    
     public function AddSecretariat_Stores(){
        
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
     
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
        
         SecretariatStores::create($data);
         
         
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='مخازن الامانات';
           $dataUser['ScreenEn']='Secretariat Stores';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
        
    }
    
     public function EditSecretariat_Stores($id){ 
         
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
            
             
               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),    
            'English_Name.required' => trans('admin.English_NameRequired'),    
     
         ]);
   
         
           
         $data['Arabic_Name']=request('Arabic_Name');
         if(!empty(request('English_Name'))){
         $data['English_Name']=request('English_Name');
         }else{
             
             
           $data['English_Name']=request('Arabic_Name');   
         }
         
           SecretariatStores::where('id',$id)->update($data);
         
                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='مخازن الامانات';
           $dataUser['ScreenEn']='Secretariat Stores';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);
         
         
            session()->flash('success',trans('admin.Updated'));
            return back();
     
     
     }
    
     public function DeleteSecretariat_Stores($id){
                      
        $del=SecretariatStores::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
        $dataUser['Screen']='مخازن الامانات';
           $dataUser['ScreenEn']='Secretariat Stores';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }
    
    
    //Secretariat_Import_goodsPage
    public function Secretariat_Import_goodsPage(){

         $Stores=SecretariatStores::all();
      
                     $Accounts = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

            $res=SecretariatImportGoods::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
                          $Brands=Brands::all();
$ItemsGroups=ItemsGroups::all();
      $Units=Measuerments::all();
         return view('admin.Secretariat.SecretariatImportGoods',[
             'Stores'=>$Stores,
             'Accounts'=>$Accounts,
             'Code'=>$Code,
             'Brands'=>$Brands,
             'ItemsGroups'=>$ItemsGroups,
             'Units'=>$Units,
      
          
         ]);
    }
 
    function ImportGoodsProductsFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             
   
                               
    if($search != '' and $store != '')
    {
            
      $Prods =Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")    
        ->take(100)        
          ->get(); 
        
           $data =ProductUnits::      
            where('Barcode', 'ILIKE', "%{$search}%")
        ->take(100)         
          ->get(); 
                       
     }

         $total_row = $Prods->count();
         $total_row1 = $data->count();
         $total_row2 = $total_row + $total_row1;
      if($total_row2 > 0) 
      { 
  
          
         foreach($Prods as $rows){  
              $Stores=SecretariatStores::all();  
           if($rows->P_Type == 'Completed' or $rows->P_Type == 'Raw' or $rows->P_Type == 'Industrial'){
        
    if($rows->Status == 0){
     $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();        
        
        $st=SecretariatStores::find($store);
        
       
     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                      $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                       $StoreNemo=$st->English_Name;    
                       
                   }  


        
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->id.'">
        </td>
   
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value="">   '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                
                
  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" value="1" > 
       
        </td>
 
        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                    if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
  
   
        
        <td>   
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 

          </td>
        </tr>
        
       
            ';
        }
        
        }elseif($rows->P_Type == 'Single_Variable'){
        
       if($rows->Status == 0){
     $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
          
        $st=SecretariatStores::find($store);
           
           
     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                         $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                               $StoreNemo=$st->English_Name; 
                       
                   }  


           
           
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value="">    '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" value="1"> 
       
        </td>
 
        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                    if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 

        <td>
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="FunV('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
          </td>
        </tr>
        
       
            ';
        }
          
         }elseif($rows->P_Type == 'Duble_Variable'){
        
       if($rows->Status == 0){
     $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
        
             $st=SecretariatStores::find($store);
         
     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                      $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                       $StoreNemo=$st->English_Name;    
                       
                   }  
           


        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value="">  '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                             
  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$UniiName.'"> 

        </td>
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" value="1" > 
       
        </td>
        
      

        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
                    if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
       

        
        <td>
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="FunVV('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
            
          
          </td>
        </tr>
        
       
            ';
        }
          
         }
          
               
             
             
        }  
          
        foreach($data as $row){  
             $Stores=SecretariatStores::all();  
       
     if($row->P_Type == 'Completed' or $row->P_Type == 'Raw' or $row->P_Type == 'Service' or $row->P_Type == 'Industrial'){ 
                     
             if($row->Product()->first()->Status == 0){
     $units=ProductUnits::where('Product',$row->Product)->get();
         $rr=ProductUnits::where('Product',$row->Product)->where('Def',1)->first();


                 
        $st=SecretariatStores::find($store);
                      
               if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                      $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                       $StoreNemo=$st->English_Name;    
                       
                   }  
           
   
  
        $output .= '
        
       <tr id="Row'.$row->Product.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$row->Product.'" value="'.$row->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$row->Product.'" value="'.$row->P_En_Name.'">
        <input type="hidden"  id="Product'.$row->Product.'" value="'.$row->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$row->Product.'" onchange="UnitCodePurchh('.$row->Product.')">
                <option value=""> '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                           
  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$row->Product.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$row->Product.'" value="'.$UniiName.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$row->Product.'"   class="form-control" value="1" > 
       
        </td>
        

        
            <td>
   <select class="select2 form-control w-100"    id="StorePurch'.$row->Product.'" onchange="StoreNamePurch('.$row->Product.')">
              ';
             
            foreach($Stores as $stor){
                  if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 

        
        <td>
           <input type="hidden" id="StorePurchName'.$row->Product.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$row->Product.'" onclick="Fun('.$row->Product.')">
          <i class="fal fa-plus"></i>
          </button>
          
          
             
          </td>
        </tr>
        
       
            ';
        }

        }elseif($row->P_Type == 'Single_Variable'){
            
          if($row->Product()->first()->Status == 0){
     $units=ProductUnits::where('Product',$row->Product)->get();
         $rr=ProductUnits::where('Product',$row->Product)->where('Def',1)->first();
           $st=SecretariatStores::find($store);
              
              
                 if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                      $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                       $StoreNemo=$st->English_Name;    
                       
                   }  
           
  

        $output .= '
        
       <tr id="Row'.$row->Product.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$row->Product.'" value="'.$row->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$row->Product.'" value="'.$row->P_En_Name.'">
        <input type="hidden"  id="Product'.$row->Product.'" value="'.$row->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$row->Product.'" onchange="UnitCodePurch('.$row->Product.')">
                <option value=""> '.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                            
  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$row->Product.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$row->Product.'" value="'.$UniiName.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$row->Product.'"   class="form-control" value="1" > 
       
        </td>
        
        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$row->Product.'" onchange="StoreNamePurch('.$row->Product.')">
              ';
             
            foreach($Stores as $stor){
                    if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
 

        
        <td>
         <input type="hidden" id="StorePurchName'.$row->Product.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$row->Product.'" onclick="FunV('.$row->Product.')">
          <i class="fal fa-plus"></i>
          </button> 
          
           
          
          </td>
        </tr>
        
       
            ';
        }
            
        }elseif($row->P_Type == 'Duble_Variable'){
          
             if($row->Product()->first()->Status == 0){
     $units=ProductUnits::where('Product',$row->Product)->get();
         $rr=ProductUnits::where('Product',$row->Product)->where('Def',1)->first();
        $st=SecretariatStores::find($store);
                 
                     if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      $UniiName=$rr->Unit()->first()->Name; 
                      $StoreNemo=$st->Arabic_Name; 
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                       $UniiName=$rr->Unit()->first()->NameEn;    
                       $StoreNemo=$st->English_Name;    
                       
                   }  


                        
        $output .= '
        
       <tr id="Row'.$row->Product.'">
        <td>
        '.$PrrroName.' 
 <input type="hidden"  id="P_Ar_Name'.$row->Product.'" value="'.$row->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$row->Product.'" value="'.$row->P_En_Name.'">
        <input type="hidden"  id="Product'.$row->Product.'" value="'.$row->Product.'">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$row->Product.'" onchange="UnitCodePurch('.$row->Product.')">
                <option value="">'.trans('admin.Choice_Unit').'</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                           
               
  if(app()->getLocale() == 'ar' ){ 
                       $UnitNamme=$nam->Name;
                   }else{
                       
                       $UnitNamme=$nam->NameEn; 
                   }
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$UnitNamme.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$row->Product.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$row->Product.'" value="'.$UniiName.'"> 
        </td>
        
        <td>
        <input type="number" id="Qty'.$row->Product.'"   class="form-control" value="1" > 
       
        </td>

        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$row->Product.'" onchange="StoreNamePurch('.$row->Product.')">
              ';
             
            foreach($Stores as $stor){
                    if(app()->getLocale() == 'ar' ){ 
                       $StorNamme=$stor->Arabic_Name;
                   }else{
                       
                       $StorNamme=$stor->English_Name; 
                   } 
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$StorNamme.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
  
        
        <td>
              <input type="hidden" id="StorePurchName'.$row->Product.'"   class="form-control"  value="'.$StoreNemo.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$row->Product.'" onclick="FunVV('.$row->Product.')">
          <i class="fal fa-plus"></i>
          </button> 
          
    
          </td>
        </tr>
        
       
            ';
        }
            
        }
         
            
      }
          
          
      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }

      public function AddSecretariat_Import_goods(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      

         ]);

 
           $ID = DB::table('secretariat_import_goods')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Qty' => request('Total_Qty'),
            'Account' => request('Account'),
            'Store' => request('Store'),
            'User' => auth()->guard('admin')->user()->id,
            
        )
    );  
  
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');

            for($i=0 ; $i < count($Unit) ; $i++){
 $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Date']=request('Date');
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Import']=$ID;
                
                if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
              
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
            $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
               
                         if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
                    
               $uu['V1']=$vId->id;  
                $uu['V2']=$vvId->id;          
             
                }else{
                    
                $uu['V1']=$VOne[$i];  
                $uu['V2']=$VTwo[$i];  
                }
                    
               ProductsSecretariatImportGoods::create($uu); 
                
             
                       $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('Product_Code',$P_Code[$i])    
                ->first(); 
                
                
                if(empty($Quantity)){
                    
                              $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first();   
                    
                       if(empty($Quantity)){
                           
                                         $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first();      
                           
                            if(empty($Quantity)){
                                
            $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first();       
                                
                            }       
                           
                           
                           
                           
                       } 
                    
                    
                    
                }
                
                $prooooo=Products::find($Product[$i]);
                
            if(!empty($Quantity)){    
                
                
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty +  $qq ; 
                
           SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
  
                
            }else{
                
       

                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                    $pqty['Qty']=$Qty[$i] * $pp->Rate;
                    $pqty['Store']=$StorePurch[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Product']=$Product[$i];

                  if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
              
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
            $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
             
                      
                           if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
                    $pqty['V1']=$vId->id;
                    $pqty['V2']=$vvId->id;
                    $pqty['V_Name']=$first_word;
                    $pqty['VV_Name']=$second_word;       
             
                }else{
                    
        
                       if($prooooo->P_Type == 'Serial'){
                          
                            $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                          
                      }else{

                          
         if($prooooo->P_Type == 'Single_Variable'){
                                       $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                              
                          }elseif($prooooo->P_Type == 'Duble_Variable'){
                              
                              $pqty['Product_Code']=$P_Code[$i];  
                              
                          }else{
                          
                          
                          
                        $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                   $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }
                              
                          }
                              
                              
                      }  
                      
                      
                      
                }   
     
              SecretariatQty::create($pqty);  
       
   
                
            }
   
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                $dataUser['Screen']='وارد بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Import Goods';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
         
         
        if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('Secretariat_Import_goodsPrint/'.$ID); } 
            
        
    }
    
    public function Secretariat_Import_goodsPrint($id){
        $item=SecretariatImportGoods::find($id);

         return view('admin.Secretariat.SecretariatImportGoodsPrint',['item'=>$item]);
    }
    
     public function Secretariat_Import_goods_Sechdule(){

            $items=SecretariatImportGoods::paginate(100);
           
         return view('admin.Secretariat.SecretariatImportGoodsSechdule',[
             'items'=>$items,
          
         ]);
    }
    
        public function DeleteSecretariat_Import_goods($id){
                      
        $del=SecretariatImportGoods::find($id);

    
    $Products=ProductsSecretariatImportGoods::where('Import',$del->id)->get();        
      foreach($Products as $prod){
          
        $PR=SecretariatQty::where('Product',$prod->Product)
             ->where('Product_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
             
        $newqty=$PR->Qty - $prod->Qty ; 
        
          SecretariatQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='وارد بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Import Goods';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   
        
public function EditSecretariat_Import_goods(){

    $id=request('ID');
         $Stores=SecretariatStores::all();
      
                     $Accounts = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

            $item=SecretariatImportGoods::find($id);
             $Products=ProductsSecretariatImportGoods::where('Import',$item->id)->get();
     
                          $Brands=Brands::all();
$ItemsGroups=ItemsGroups::all();
      $Units=Measuerments::all();
         return view('admin.Secretariat.EditSecretariatImportGoods',[
             'Stores'=>$Stores,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'Products'=>$Products,
             'Brands'=>$Brands,
             'ItemsGroups'=>$ItemsGroups,
             'Units'=>$Units,
      
          
         ]);
    }
    
      public function PostEditSecretariat_Import_goods(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      

         ]);

 
           $ID = request('ID');
    
            
            $Bill['Code'] = request('Code');
            $Bill['Date'] = request('Date');
            $Bill['Note'] = request('Note');
            $Bill['Product_Numbers'] = request('Product_Numbers');
            $Bill['Total_Qty'] = request('Total_Qty');
            $Bill['Account'] = request('Account');
            $Bill['Store'] = request('Store');
            $Bill['User'] = request('User');
            
            SecretariatImportGoods::where('id',$ID)->update($bill);
         $del=SecretariatImportGoods::find($ID);

    
    $Products=ProductsSecretariatImportGoods::where('Import',$del->id)->get();        
      foreach($Products as $prod){
          
        $PR=SecretariatQty::where('Product',$prod->Product)
             ->where('Product_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
             
        $newqty=$PR->Qty - $prod->Qty ; 
        
          SecretariatQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

          if(!empty(request('Unit'))){
            
              ProductsSecretariatImportGoods::where('Import',$del->id)->delete();    
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');

            for($i=0 ; $i < count($Unit) ; $i++){
 $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
                
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Date']=request('Date');
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Import']=$ID;
                
                if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
              
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
            $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
                    if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
               $uu['V1']=$vId->id;  
                $uu['V2']=$vvId->id;          
             
                }else{
                    
                $uu['V1']=$VOne[$i];  
                $uu['V2']=$VTwo[$i];  
                }
                    
               ProductsSecretariatImportGoods::create($uu); 
                
             
                       $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('Product_Code',$P_Code[$i])    
                ->first(); 
                
                
                    if(empty($Quantity)){
                    
                              $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first();   
                    
                       if(empty($Quantity)){
                           
                                         $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first();      
                           
                            if(empty($Quantity)){
                                
            $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first();       
                                
                            }       
                           
                           
                           
                           
                       } 
                    
                    
                    
                }
                
                
            if(!empty($Quantity)){    
                
                
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty +  $qq ; 
                
           SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
  
                
            }else{
                
       

                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                    $pqty['Qty']=$Qty[$i] * $pp->Rate;
                    $pqty['Store']=$StorePurch[$i];
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Product']=$Product[$i];

                   if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
              
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
            $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
                         if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
                    $pqty['V1']=$vId->id;
                    $pqty['V2']=$vvId->id;
                    $pqty['V_Name']=$first_word;
                    $pqty['VV_Name']=$second_word;       
             
                }else{
                    
        
                       if($prooooo->P_Type == 'Serial'){
                          
                            $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                          
                      }else{

                          
         if($prooooo->P_Type == 'Single_Variable'){
                                       $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$P_Code[$i];
                              
                          }elseif($prooooo->P_Type == 'Duble_Variable'){
                              
                              $pqty['Product_Code']=$P_Code[$i];  
                              
                          }else{
                          
                          
                          
                        $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                   $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['Product_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }
                              
                          }
                              
                              
                      }  
                      
                      
                      
                }      
     
              SecretariatQty::create($pqty);  
       
   
                
            }
           
                
                
                
                
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                 $dataUser['Screen']='وارد بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Import Goods';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
          return redirect('Secretariat_Import_goods_Sechdule');
            
        
    }
    
//Secretariat_Export_goodsPage
     public function Secretariat_Export_goodsPage(){

         $Stores=SecretariatStores::all();
      
                     $Accounts = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

            $res=SecretariatExportGoods::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
                          $Brands=Brands::all();
$ItemsGroups=ItemsGroups::all();
      $Units=Measuerments::all();
         return view('admin.Secretariat.SecretariatExportGoods',[
             'Stores'=>$Stores,
             'Accounts'=>$Accounts,
             'Code'=>$Code,
             'Brands'=>$Brands,
             'ItemsGroups'=>$ItemsGroups,
             'Units'=>$Units,
      
          
         ]);
    }
    
     function ExportProductsFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             

         
    if($search != '' and $store != '')
    {

            $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
           ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
           ->orWhere('Product_Code','ILIKE', "%{$search}%")        
           ->orWhere('PP_Code','ILIKE', "%{$search}%")        
           ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
           ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
           ->take(500)        
           ->get(); 

                       
     }

         $total_row = $Prods->count();
      if($total_row > 0) 
      { 

         foreach($Prods as $rows){  
             if($rows->Qty != 0){ 
        if($rows->Store == $store){      
            $Stores=SecretariatStores::all();  
            
        if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
   
        $st=SecretariatStores::find($store);
    
                
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.' 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rr->Unit()->first()->Name.'"> 
        </td>

                 <td>
        <input type="number" id="AvQty'.$rows->id.'" disabled  class="form-control" value="'.$rows->Qty.'" > 
       
        </td>
       
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" value="1" > 
       
        </td>
        
    
        
            <td>
   <select class="select2 form-control w-100" disabled    id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 

    <td>
        <input type="text" id="PNote'.$rows->id.'"   class="form-control"> 
       
        </td>

        <td>
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
          
   
          </td>
        </tr>
        
       
            ';
        }
        
        }elseif($rows->Product()->first()->P_Type == 'Single_Variable'){
        
       if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();

           $st=SecretariatStores::find($store);

        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.' ('.$rows->V1()->first()->Name.') 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="'.$rows->V1()->first()->Name.'">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Product_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
        </td>

           <td>
        <input type="number" id="AvQty'.$rows->id.'" disabled  class="form-control" value="'.$rows->Qty.'" > 
       
        </td> 
    
        
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" value="1"  > 
       
        </td>
        
       
        
            <td>
   <select class="select2 form-control w-100" disabled   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
     <td>
        <input type="text" id="PNote'.$rows->id.'"   class="form-control"> 
       
        </td>
        <td>
         <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
        
          
          </td>
        </tr>
        
       
            ';
        }
          
         }elseif($rows->Product()->first()->P_Type == 'Duble_Variable'){
        
       if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
          $st=SecretariatStores::find($store);
        
      
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'  ('.$rows->V1()->first()->Name.') ('.$rows->V2()->first()->Name.') 
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="'.$rows->V1()->first()->Name.'">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="'.$rows->V2()->first()->Name.'">

        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurch('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Product_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
        </td>
        

                        <td>
        <input type="number" id="AvQty'.$rows->id.'" disabled  class="form-control" value="'.$rows->Qty.'" > 
       
        </td>
        <td>
        <input type="number" id="Qty'.$rows->id.'"   class="form-control" > 
       
        </td>
        
    

        
            <td>
   <select class="select2 form-control w-100" disabled   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 

            <td>
        <input type="text" id="PNote'.$rows->id.'"   class="form-control"> 
       
        </td>
        <td>
          <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          
   
          </td>
        </tr>
        
       
            ';
        }
          
         }
            
            
            

        }  
        }
             
        }  

      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
       public function AddSecretariatExportGoods(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      

         ]);

 
           $ID = DB::table('secretariat_export_goods')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Qty' => request('Total_Qty'),
            'Account' => request('Account'),
            'Store' => request('Store'),
            'Status' => 0,
            'User' => auth()->guard('admin')->user()->id,
            
        )
    );  
  
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $PNote=request('PNote');
 

            for($i=0 ; $i < count($Unit) ; $i++){
 $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Date']=request('Date');
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Export']=$ID;
                $uu['V1']=$VOne[$i];  
                $uu['V2']=$VTwo[$i];  
                $uu['Note']=$PNote[$i];  

                    
               ProductsSecretariatExportGoods::create($uu); 
                
             
                       $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('Product_Code',$P_Code[$i])    
                ->first(); 
                
                
                 if(empty($Quantity)){
                    
                              $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first();   
                    
                       if(empty($Quantity)){
                           
                                         $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first();      
                           
                            if(empty($Quantity)){
                                
            $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first();       
                                
                            }       
                           
                           
                           
                           
                       } 
                    
                    
                    
                }
                
            if(!empty($Quantity)){    
                
                
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty -  $qq ; 
                
           SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
  
                
            }
           
                
                
                
                
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
   
                     $dataUser['Screen']='صرف بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Export Goods';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
         
         
        if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('Secretariat_Export_goodsPrint/'.$ID); } 
            
        
    }
    
    public function Secretariat_Export_goodsPrint($id){
        $item=SecretariatExportGoods::find($id);

         return view('admin.Secretariat.SecretariatExportGoodsPrint',['item'=>$item]);
    }
    
      public function Secretariat_Export_goods_Sechdule(){

            $items=SecretariatExportGoods::paginate(100);
           
         return view('admin.Secretariat.SecretariatExportGoodsSechdule',[
             'items'=>$items,
          
         ]);
    }

        public function DeleteSecretariat_Export_goods($id){
                      
        $del=SecretariatExportGoods::find($id);

    
    $Products=ProductsSecretariatExportGoods::where('Export',$del->id)->get();        
      foreach($Products as $prod){
          
        $PR=SecretariatQty::where('Product',$prod->Product)
             ->where('Product_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
             
        $newqty=$PR->Qty + $prod->Qty ; 
        
          SecretariatQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='صرف بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Export Goods';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Code;
           $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }   
        
public function EditSecretariat_Export_goods(){

    $id=request('ID');
         $Stores=SecretariatStores::all();
      
                     $Accounts = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

            $item=SecretariatExportGoods::find($id);
             $Products=ProductsSecretariatExportGoods::where('Export',$item->id)->get();
     
                          $Brands=Brands::all();
$ItemsGroups=ItemsGroups::all();
      $Units=Measuerments::all();
    
    
             if($item->Status != 0){  
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Secretariat_Export_goods_Sechdule');
        
                    }
    
         return view('admin.Secretariat.EditSecretariatExportGoods',[
             'Stores'=>$Stores,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'Products'=>$Products,
             'Brands'=>$Brands,
             'ItemsGroups'=>$ItemsGroups,
             'Units'=>$Units,
      
          
         ]);
    }
    
    public function PostEditSecretariatExportGoods(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      

         ]);

           $ID = request('ID');
 
            $bill['Code'] = request('Code');
            $bill['Date'] = request('Date');
            $bill['Note'] = request('Note');
            $bill['Product_Numbers'] = request('Product_Numbers');
            $bill['Total_Qty'] = request('Total_Qty');
            $bill['Account'] = request('Account');
            $bill['Store'] = request('Store');
            $bill['User'] = request('User');
            $bill['Status'] = 0;
  
        SecretariatExportGoods::where('id',$ID)->update($bill);
        
        $del=SecretariatExportGoods::find($ID);

    
    $Products=ProductsSecretariatExportGoods::where('Export',$del->id)->get();        
      foreach($Products as $prod){
          
        $PR=SecretariatQty::where('Product',$prod->Product)
             ->where('Product_Code',$prod->Product_Code) 
             ->where('Store',$prod->Store) 
             ->first(); 
             
        $newqty=$PR->Qty + $prod->Qty ; 
        
          SecretariatQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
          
      }

          if(!empty(request('Unit'))){
            ProductsSecretariatExportGoods::where('Export',$del->id)->delete();   
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
     $PNote=request('PNote');

            for($i=0 ; $i < count($Unit) ; $i++){
 $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();    
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Date']=request('Date');
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Export']=$ID;
                $uu['V1']=$VOne[$i];  
                $uu['V2']=$VTwo[$i];  
     $uu['Note']=$PNote[$i];  
                    
               ProductsSecretariatExportGoods::create($uu); 
                
             
                       $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('Product_Code',$P_Code[$i])    
                ->first(); 
            
                 if(empty($Quantity)){
                    
                              $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first();   
                    
                       if(empty($Quantity)){
                           
                                         $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first();      
                           
                            if(empty($Quantity)){
                                
            $Quantity =SecretariatQty::
                where('Store',$StorePurch[$i])    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first();       
                                
                            }       
                           
                           
                           
                           
                       } 
                    
                    
                    
                }
                
            if(!empty($Quantity)){    
                
                
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty -  $qq ; 
                
           SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
  
                
            }
           
                
                
                
                
            }  

              
          }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                            $dataUser['Screen']='صرف بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Export Goods';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
   return redirect('Secretariat_Export_goods_Sechdule');  
            
        
    }
    
    
    //RecivedSecretariat_Export_goods
   public function RecivedSecretariat_Export_goods(){

    $id=request('ID');
         $Stores=SecretariatStores::all();
         $RealStores=Stores::all();
      
        
            $Coins=Coins::all();  
          
        $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
                     $Accounts = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

            $item=SecretariatExportGoods::find($id);
             $Products=ProductsSecretariatExportGoods::where('Export',$item->id)->get();
     
      $Units=Measuerments::all();
         return view('admin.Secretariat.RecivedSecretariatExportGoods',[
             'Stores'=>$Stores,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'Products'=>$Products,
             'Units'=>$Units,
             'RealStores'=>$RealStores,
             'Coins'=>$Coins,
             'Safes'=>$Safes,
      
          
         ]);
    }
    
     public function PostRecivedSecretariatExportGoods(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Store'=>'required',
        
               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Store.required' => trans('admin.StoreRequired'),      

         ]);

           $ID = request('ID');
 $CodeT=request('Code');
            $bill['Code'] = request('Code');
            $bill['Date'] = request('Date');
            $bill['Note'] = request('Note');
            $bill['Product_Numbers'] = request('Product_Numbers');
            $bill['Total_Qty'] = request('Total_Qty');
            $bill['Total_Recived_Qty'] = request('Total_Recived_Qty');
            $bill['Total_Price'] = request('Total_Price');
            $bill['Account'] = request('Account');
            $bill['Store'] = request('Store');
            $bill['StoreGoods'] = request('StoreGoods');
            $bill['User'] = request('User');
            $bill['Draw'] = request('Draw');
            $bill['Coin'] = request('Coin');
            $bill['Safe'] = request('Safe');
            $bill['Status'] = 1;
  
           SecretariatExportGoods::where('id',$ID)->update($bill);
        $del=SecretariatExportGoods::find($ID);

 
          if(!empty(request('Unit'))){
            ProductsSecretariatExportGoods::where('Export',$del->id)->delete();   
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $StorePurch=request('StorePurch');
              $Product=request('Product');
              $VOne=request('VOne');
              $VTwo=request('VTwo');
              $V_Name=request('V_Name');
              $VV_Name=request('VV_Name');
              $PNote=request('PNote');
              $RecivedQty=request('RecivedQty');
              $Impotence=request('Impotence');
              $Price=request('Price');
              $Total=request('Total');

            for($i=0 ; $i < count($Unit) ; $i++){
 $pp=ProductUnits::where('Product',$Product[$i])->where('Unit',$Unit[$i])->first();   
            $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();                   
                
                $uu['Product_Code']=$P_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['V_Name']=$V_Name[$i];
                $uu['VV_Name']=$VV_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Date']=request('Date');
                $uu['Store']=$StorePurch[$i];
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['Export']=$ID;
                $uu['V1']=$VOne[$i];  
                $uu['V2']=$VTwo[$i];  
                $uu['Note']=$PNote[$i];  
                $uu['RecivedQty']=$RecivedQty[$i];  
                $uu['Impotence']=$Impotence[$i];  
                $uu['Price']=$Price[$i];  
                $uu['Total']=$Total[$i];  
                    
               ProductsSecretariatExportGoods::create($uu); 
                
                     $Quantity =ProductsQty::
                where('Store',request('StoreGoods'))    
                ->where('Product',$Product[$i])    
                ->where('P_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',request('StoreGoods'))    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$P_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',request('StoreGoods'))    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$P_Code[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',request('StoreGoods'))    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$P_Code[$i])    
                ->first(); 

}

}

}

            if(!empty($Quantity)){    
           $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();  
                
           $qq= $unit->Rate * $Qty[$i] ;
                
           $newqty=$Quantity->Qty +  $qq ; 
                
           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]); 
                
               $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();    
                
                
        $purchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('Total_Bf_Tax');     
  $countPurchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('SmallQty');
        
                                 $storesTransfer=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',request('StoreGoods'))->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',request('StoreGoods'))->get()->sum('SmallTrans_Qty');          
              
                
    $purchsStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('SmallQty');
                
            $OUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',request('StoreGoods'))->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',request('StoreGoods'))->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }

                
                if($ty != 0){
                   $in=$qq * $ty;
         $out=0;     
         $current=$newqty * $ty;  
                }else{
                  
             $in=$qq * 1;
         $out=0;     
         $current=$newqty * 1;        
                    
                }
     
                
                
            $prooooo=Products::find($Product[$i]);     
          $move['Date']=request('Date');
          $move['Type']='استلام امانات';
          $move['TypeEn']='Recived Secretariat';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');         
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=request('StoreGoods');
          $move['Product']=$Product[$i]; 
          $move['V1']=$VOne[$i];  
          $move['V2']=$VTwo[$i];   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);        
                
                
            }else{
                
                
           if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
              
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
                     $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
  
                    if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
                    $sNam=$first_word;
                    $sId=$vId->id;
                    $ssNam=$second_word;       
                    $ssId=$vvId->id;       
             
                }else{
                    
                    $sNam=$V_Name[$i];
                    $ssNam=$VV_Name[$i];
                      $sId=$VOne[$i];
                     $ssId=$VTwo[$i]; 
                }             
                
                
                   $id_store = DB::table('products_stores')->insertGetId(
            
        array(
            
            'P_Ar_Name' => $P_Ar_Name[$i],
            'P_En_Name' => $P_En_Name[$i],
            'P_Code' =>   $P_Code[$i],
            'Exp_Date' => null,
            'Product' => $Product[$i],
            'Store' =>request('StoreGoods'),
            'V1' => $sId,
            'V2' => $ssId,        
            'V_Name' => $sNam,        
            'VV_Name' => $ssNam,        
     
        )
    );          
                

                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Qty']=$Qty[$i] * $pp->Rate;
                    $pqty['Price']=$Price[$i];
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=request('StoreGoods');
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$Product[$i];
                
           $prooooo=Products::find($Product[$i]); 
            if( !empty($V_Name[$i]) and  !empty($VV_Name[$i])){
        
                                    $my_value = $V_Name[$i];
$first_word = explode(' - ',trim($my_value))[0];    
$second_word = explode(' - ',trim($my_value))[1];    
          
            $vId=SubVirables::where('Name','like', $first_word.'%')->first();            
            $vvId=SubVirables::where('Name','like', $second_word.'%')->first(); 
                         if(empty($vId)){
                         $vId=SubVirables::where('NameEn','like', $first_word.'%')->first();        
                    }
                   
                           if(empty($vvId)){
                         $vvId=SubVirables::where('NameEn','like', $second_word.'%')->first();        
                    }
                    $pqty['V1']=$vId->id;
                    $pqty['V2']=$vvId->id;
                    $pqty['V_Name']=$first_word;
                    $pqty['VV_Name']=$second_word;    
                    $pqty['P_Code']=$P_Code[$i];  
             
                }else{
                      
                       
                
                      if($prooooo->P_Type == 'Serial'){
                          
                            $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['P_Code']=$P_Code[$i];
                          
                      }else{

                          
         if($prooooo->P_Type == 'Single_Variable'){
                                       $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['P_Code']=$P_Code[$i];
                              
                          }elseif($prooooo->P_Type == 'Duble_Variable'){
                              
                              $pqty['P_Code']=$P_Code[$i];  
                              
                          }else{
                          
                          
                          
                        $coco=array();
                $CodesPrds=ProductUnits::where('Product',$Product[$i])->select('Barcode')->get();   
                foreach($CodesPrds as $cco){
                    
                  
                    array_push($coco,$cco->Barcode);
                    
                }

                   $pqty['V1']=$VOne[$i];
                    $pqty['V2']=$VTwo[$i];
                    $pqty['V_Name']=$V_Name[$i];
                    $pqty['VV_Name']=$VV_Name[$i];
                    $pqty['P_Code']=$coco[0];
                
                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null; 
                }
                   
                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null; 
                }
                
                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null; 
                }
                              
                          }
                              
                              
                      }
                      
                      
                }   
                

              ProductsQty::create($pqty);  
                $prooooo=Products::find($Product[$i]); 
                
                 $plow=ProductUnits::where('Product',$Product[$i])->where('Rate',1)->first();  
                
                
            $purchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('Total_Bf_Tax');     
        $countPurchs=ProductsPurchases::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('SmallQty');
         $storesTransfer=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',request('StoreGoods'))->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('To_Store',request('StoreGoods'))->get()->sum('SmallTrans_Qty');          
              
                        
    $purchsStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('Total');     
                
    $countStart=ProductsStartPeriods::where('Product',$Product[$i])->where('SmallCode',$plow->Barcode)->where('Store',request('StoreGoods'))->get()->sum('SmallQty');
                
          $OUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',request('StoreGoods'))->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$Product[$i])->where('Store',request('StoreGoods'))->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ; 
                }else{
                    
                   $ty= $Collect;   
                }              
  
                
                
                
                if($ty != 0){
                   $in=($Qty[$i] * $pp->Rate) * $ty ;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * $ty ;  
                }else{
                  
             $in=($Qty[$i] * $pp->Rate) * 1;
         $out=0;     
         $current=($Qty[$i] * $pp->Rate) * 1;        
                    
                }
 
            $move['Date']=request('Date');
          $move['Type']='استلام امانات';
                      $move['TypeEn']='Recived Secretariat';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$Qty[$i] * $pp->Rate;
          $move['Outcom']=0;
          $move['Current']=$Qty[$i] * $pp->Rate;
        $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');   
          $move['P_Ar_Name']=$P_Ar_Name[$i];
          $move['P_En_Name']=$P_En_Name[$i];
          $move['P_Code']=$P_Code[$i];
          $move['Unit']=$Unit[$i];
          $move['QTY']=$Qty[$i];    
          $move['Group']=$prooooo->Group;
          $move['Store']=request('StoreGoods');
          $move['Product']=$Product[$i]; 
          $move['V1']=$VOne[$i];  
          $move['V2']=$VTwo[$i];   
          $move['User']=auth()->guard('admin')->user()->id;
              ProductMoves::create($move);            
                
   
                
            }
    
            }  

              
          }

         
               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
        
            'Type' => 'استلام امانات',
            'TypeEn' => 'Recived Secrtrait',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total_Price'),
            'Total_Creditor' => request('Total_Price'),
            'Note' => request('Note'),
  
        )
    );
           
              
        
         $store=Stores::find(request('StoreGoods'));         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$store->Account;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
       
        $Gen['Type']='استلام امانات';
        $Gen['TypeEn']='Recived Secrtrait';
        $Gen['Debitor']=request('Total_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total_Price');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$store->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Price');
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
               $Gen['Type']='استلام امانات';         $Gen['TypeEn']='Recived Secrtrait';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total_Price');
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
         
              
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
               $Gen['Type']='استلام امانات';         $Gen['TypeEn']='Recived Secrtrait';
        $Gen['Debitor']=request('Total_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Total_Price');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Price');
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
               $Gen['Type']='استلام امانات';         $Gen['TypeEn']='Recived Secrtrait';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Total_Price');
        $Gen['Account']=request('Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);           
              
    
         
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='استلام بضاعه امانات';
           $dataUser['ScreenEn']='Secretariat Recived Goods';
           $dataUser['Type']='استلام';
           $dataUser['TypeEn']='Recived';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Recived'));
         
            if(request('SP') == 0){     return redirect('Secretariat_Export_goods_Sechdule');   }elseif(request('SP') == 1){  return redirect('Secretariat_Recived_Export_goodsPrint/'.$ID); } 
            
         

            
        
    }
    
      public function Secretariat_Recived_Export_goodsPrint($id){
        $item=SecretariatExportGoods::find($id);

         return view('admin.Secretariat.SecretariatRecivedExportGoodsPrint',['item'=>$item]);
    }

    //Secretariat_Stores_Qty
    public function Secretariat_Stores_Qty(){

         $Stores=SecretariatStores::all();
      
         return view('admin.Secretariat.Secretariat_Stores_Qty',[
             'Stores'=>$Stores,
  
         ]);
    }

        function Secretariat_Stores_QtyFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('Store');             
                 
    if($search != '' and $store != '')
    {

        
         $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code','ILIKE', "%{$search}%")        
              ->orWhere('PP_Code','ILIKE', "%{$search}%")                  
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")                  
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")         
            ->where('Store',$store)             
          ->get();  
                       
     }elseif($search != '' and $store == ''){
        
        
             $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code','ILIKE', "%{$search}%")                  
            ->orWhere('PP_Code','ILIKE', "%{$search}%")                  
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")                  
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")                  
          ->get();  
        
    }elseif($search == '' and $store != ''){
        
        
             $Prods=SecretariatQty::          
            where('Store',$store)             
          ->get();  
        
        
    }
         
         
         $total_row = $Prods->count();
      if($total_row > 0) 
      { 
         foreach($Prods as $rows){  
  
             
                  if(app()->getLocale() == 'ar' ){ 
                      $PrrroName=$rows->P_Ar_Name; 
                      
                      if(!empty($rows->V1)){
                      $PrrroVName=$rows->V1()->first()->Name; 
                      }else{
                          $PrrroVName='';
                          
                      }
                       
                      
                           if(!empty($rows->V2)){
                      $PrrroVVName=$rows->V2()->first()->Name; 
                           }else{
                              $PrrroVVName=''; 
                           }
                   
                   }else{
                         $PrrroName=$rows->P_En_Name;  
                            if(!empty($rows->V1)){
                      $PrrroVName=$rows->V1()->first()->NameEn; 
                      }else{
                          $PrrroVName='';
                          
                      }
                       
                      
                           if(!empty($rows->V2)){
                      $PrrroVVName=$rows->V2()->first()->NameEn; 
                           }else{
                              $PrrroVVName=''; 
                           }

                       
                   }   
             
        $output .= '
        
       <tr>
       
       <td>
'.$rows->Product_Code.'

       </td>
       
        <td>
        '.$PrrroName.' ('.$PrrroVName .') ('.$PrrroVVName .')
        </td>
    
       
         <td>
      <input type="text" id="Qty'.$rows->id.'" class="form-control Qun" value="'.$rows->Qty.'"  disabled> 
       </td>
    
        </tr>
            ';
       
        
        }
  
      
      }else
      {
       $output = '
        <div class="col-md-3">'.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }

    //ManufacturingModelSecretariat
        public function ManufacturingModelSecretariat(){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $res=ManufacturingSecretariatModel::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
   
         $Stores=SecretariatStores::all();
     
            $StoresOut=Stores::all();
           
         return view('admin.Secretariat.ManufacturingModelSecretariat',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Stores'=>$Stores,
             'StoresOut'=>$StoresOut,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
    function IncomManufacturingModelSecretariatFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

    $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
                  $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code',$search)        
            ->orWhere('PP_Code',$search)        
            ->orWhere('PPP_Code',$search)        
            ->orWhere('PPPP_Code',$search)        
          ->get(); 
            
              $data =ProductUnits:: 
           where('P_Ar_Name',$search)                  
            ->orWhere('P_En_Name',$search)      
            ->orWhere('Barcode', $search)
          ->get();      
 }else{
            
              $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code','ILIKE', "%{$search}%")        
            ->orWhere('PP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
          ->get();   
            
           $data =ProductUnits::   
          where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")         
            ->orWhere('Barcode','ILIKE', "%{$search}%")                    
          ->get();     
  } 
      
       
        
     }

             $total_row = $Prods->count();
         $total_row1 = $data->count();
         $total_row2= $total_row + $total_row1 ;
      if($total_row2  > 0) 
      { 
   
          
         foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=SecretariatStores::all();  
    if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Service'){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first();    
             $plow=ProductUnits::where('Product',$rows->Product)->where('Rate',1)->first();      
                
         $purchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total_Bf_Tax');
     $countPurchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

    $purchsStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total');
      $countStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

               $storesTransfer=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('SmallTrans_Qty');        
                
          
                $OUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;        
                
 
        if(!empty($x)){
        
         if(!empty($purchs) or !empty($purchsStart) or !empty($storesTransfer) or !empty($OUTCOME)){
           
               
   if($CollectCount != 0){
     $ty= ($Collect /  $CollectCount) * $rr->Rate ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '');
                       
                   }else{
                       
                       $pr=$Collect * $rr->Rate;       
                   }         
             
         }else{
          $pr=$x->Price;   
             
         }        
            
            
        }else{
         $pr=$rr->Price;    
        }
      
        $st=SecretariatStores::find($store);
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Product_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" >
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
 
        </td>
        
              



        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  

          
          
           foreach($data as $rows){  

           $Stores=SecretariatStores::all();  
       if($rows->P_Type == 'Service' or $rows->P_Type == 'Completed' or $rows->P_Type == 'Raw' or $rows->P_Type == 'Industrial' ){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
           
         $pr=$rr->Price;    
      
        $st=SecretariatStores::find($store);
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>
        

        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        
        }  

        

      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     function OutcomManufacturingModelSecretariatFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             
                   
    if($search != '')
    {

        
        
        $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
           $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode',$search)
        ->take(100)         
          ->get();  
            
            
 }else{
            
             $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get();  
            
    $data =ProductUnits::      
            where('Barcode','ILIKE', "%{$search}%")
        ->take(100)         
          ->get();         
            
            
  } 
        
 
             
     }

   
         $total_row2 = $data->count();
         $total_row3 = $datas->count();
         $total_row4 = $total_row2 + $total_row3;
      if($total_row4 > 0) 
      { 
   
            foreach($datas as $rows){  
         
             
           $Stores=Stores::all();  
        
          if($rows->P_Type == 'Industrial' or $rows->P_Type == 'Completed'){       
            if($rows->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->id)->first(); 

        $st=Stores::find($store);
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        
                <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$rr->Unit()->first()->Name.'"> 
        </td>
        
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>
        
             <td>
        <input type="number" id="Workmanship_Price'.$rows->id.'" step="any" class="form-control" value="1"> 
        </td>
        

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
          }
        
          
        }  
          
          
           foreach($data as $rows){  
    
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        
               <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
        </td>
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
 
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>
        
                <td>
        <input type="number" id="Workmanship_Price'.$rows->id.'" step="any" class="form-control" value="1"> 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
   
        }  
          
   
      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
      
      public function AddManufacturingModelSecretariat(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Name'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

         
                 if(!empty(request('NameEn'))){
         $NameEn=request('NameEn');
          }else{
             $NameEn=request('Name'); 
              
          }

          
          
           $ID = DB::table('manufacturing_secretariat_models')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Name' => request('Name'),
            'NameEn' => $NameEn,
            'Time' => request('Time'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Discount' => request('Total_Discount'),
            'Total_BF_Taxes' => request('Total_BF_Taxes'),
            'Total_Taxes' => request('Total_Taxes'),
            'Total_Price' => request('Total_Price'),
            'Hall' => request('Hall'),
            'Coin' => request('Coin'),
            'Type' => 1,
            'Cost_Center' => request('Cost_Center'),
            'User' => auth()->guard('admin')->user()->id,

        )
    );  
        
    
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uuIn['Product_Code']=$P_Code[$i];
                $uuIn['P_Ar_Name']=$P_Ar_Name[$i];
                $uuIn['P_En_Name']=$P_En_Name[$i];
                $uuIn['Qty']=$Qty[$i];
                $uuIn['Cost']=$Price[$i];
                $uuIn['Discount']=$Discount[$i];
                $uuIn['Tax']=$PurchTax[$i];
                $uuIn['Total_Bf_Tax']=$TotalBFTax[$i];
                $uuIn['Total']=$Totaal[$i];
                $uuIn['Total_Tax']=$TotalTax[$i];
                $uuIn['Depreciation']=$DeprecPrecent[$i];
                $uuIn['Depreciation_Qty']=$Deprec[$i];
                $uuIn['Store']=$StorePurch[$i];
                $uuIn['Product']=$Product[$i];
                $uuIn['Unit']=$Unit[$i];
                $uuIn['Model']=$ID;
             

               IncomManufacturingSecretariatModel::create($uuIn); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
              $Workmanship_Price=request('Workmanship_Price');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                     $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first();    
                 $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Workmanship_Price']=$Workmanship_Price[$i];
                $uu['Cost']=request('Total_Price');
                $uu['Model']=$ID;

               OutcomeManufacturingSecretariatModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

                         $dataUser['Screen']='نموذج تصنيع للغير';
           $dataUser['ScreenEn']='Manufacturing Model To Others';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
                 
          if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('ManuSecrtaritExecution/'.$ID); }  
            
            
        
    }

      public function ManufacturingModelSecretariatSechdule(){
        $items=ManufacturingSecretariatModel::paginate(100);
         return view('admin.Secretariat.ManufacturingModelSecretariatSechdule',['items'=>$items]);
    }
  
     public function DeleteManufacturingModelSecretariat($id){
                      
        $del=ManufacturingSecretariatModel::find($id);
         
         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                           $dataUser['Screen']='نموذج تصنيع للغير';
           $dataUser['ScreenEn']='Manufacturing Model To Others';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);
         
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

      public function EditManufacturingSecretariatModel($id){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $item=ManufacturingSecretariatModel::find($id);
         $Stores=SecretariatStores::all();
            $StoresOut=Stores::all();
          
          
 $Outs=OutcomeManufacturingSecretariatModel::where('Model',$item->id)->get(); 
        $Ins=IncomManufacturingSecretariatModel::where('Model',$item->id)->get();      
         return view('admin.Secretariat.EditManufacturingModelSecretariat',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'item'=>$item,
             'Outs'=>$Outs,
             'Ins'=>$Ins,
             'Stores'=>$Stores,
             'StoresOut'=>$StoresOut,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
     public function PostEditManufacturingModelSecretariat(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Name'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

         $id=request('ID');
  
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Name'] = request('Name');
            $data['NameEn'] = request('NameEn');
            $data['Time'] = request('Time');
            $data['Draw' ]=request('Draw');
            $data['Note'] = request('Note');
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Discount'] = request('Total_Discount');
            $data['Total_BF_Taxes'] = request('Total_BF_Taxes');
            $data['Total_Taxes'] = request('Total_Taxes');
            $data['Total_Price'] = request('Total_Price');
            $data['Hall'] = request('Hall');
            $data['Coin'] = request('Coin');
            $data['Cost_Center'] = request('Cost_Center');

         ManufacturingSecretariatModel::where('id',$id)->update($data);
         

          if(!empty(request('Unit'))){
            IncomManufacturingSecretariatModel::where('Model',$id)->delete();
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');

            for($i=0 ; $i < count($Unit) ; $i++){

                $uuIn['Product_Code']=$P_Code[$i];
                $uuIn['P_Ar_Name']=$P_Ar_Name[$i];
                $uuIn['P_En_Name']=$P_En_Name[$i];
                $uuIn['Qty']=$Qty[$i];
                $uuIn['Cost']=$Price[$i];
                $uuIn['Discount']=$Discount[$i];
                $uuIn['Tax']=$PurchTax[$i];
                $uuIn['Total_Bf_Tax']=$TotalBFTax[$i];
                $uuIn['Total']=$Totaal[$i];
                $uuIn['Total_Tax']=$TotalTax[$i];
                $uuIn['Depreciation']=$DeprecPrecent[$i];
                $uuIn['Depreciation_Qty']=$Deprec[$i];
                $uuIn['Store']=$StorePurch[$i];
                $uuIn['Product']=$Product[$i];
                $uuIn['Unit']=$Unit[$i];
                $uuIn['Model']=$id;
             

               IncomManufacturingSecretariatModel::create($uuIn); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
              OutcomeManufacturingSecretariatModel::where('Model',$id)->delete();
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
              $Workmanship_Price=request('Workmanship_Price');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                     $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first();    
                 $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Workmanship_Price']=$Workmanship_Price[$i];
                $uu['Cost']=request('Total_Price');
                $uu['Model']=$id;

               OutcomeManufacturingSecretariatModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='نموذج تصنيع للغير';
           $dataUser['ScreenEn']='Manufacturing Model To Others';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
          return redirect('ManufacturingModelSecretariatSechdule');
            
        
    }
    
        public function ManuSecrtaritExecution($id){
       
         
                   $res=ExecutingReceivingSecretariat::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
         $Model=ManufacturingSecretariatModel::find($id);
              $Branches=Branches::all();
              $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
         
         return view('admin.Secretariat.ManuExecutionSecrtrait',['Code'=>$Code,'Model'=>$Model,'Branches'=>$Branches,'Clients'=>$Clients]);
    }
 
    
    //ManufacturingModelSecretariatPrecent
     public function ManufacturingModelSecretariatPrecent(){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $res=ManufacturingSecretariatModel::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
   
         $Stores=SecretariatStores::all();
     
            $StoresOut=Stores::all();
           
         return view('admin.Secretariat.ManufacturingModelSecretariatPrecent',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Stores'=>$Stores,
             'StoresOut'=>$StoresOut,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
        function IncomManufacturingModelSecretariatFilterPrecent(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');                       
                               
    if($search != '' and $store != '')
    {

    $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
                  $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code',$search)        
            ->orWhere('PP_Code',$search)        
            ->orWhere('PPP_Code',$search)        
            ->orWhere('PPPP_Code',$search)        
          ->get(); 
            
              $data =ProductUnits:: 
           where('P_Ar_Name',$search)                  
            ->orWhere('P_En_Name',$search)      
            ->orWhere('Barcode', $search)
          ->get();      
 }else{
            
              $Prods=SecretariatQty::      
            where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('V_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('VV_Name','ILIKE', "%{$search}%")                                                                  
            ->orWhere('Product_Code','ILIKE', "%{$search}%")        
            ->orWhere('PP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPP_Code','ILIKE', "%{$search}%")        
            ->orWhere('PPPP_Code','ILIKE', "%{$search}%")        
          ->get();   
            
         $data =ProductUnits::   
          where('P_Ar_Name','ILIKE',"%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")         
            ->orWhere('Barcode','ILIKE', "%{$search}%")                    
          ->get();       
  } 
      
       
        
     }

            $total_row = $Prods->count();
         $total_row1 = $data->count();
         $total_row2= $total_row + $total_row1 ;
      if($total_row2  > 0) 
      { 
   
          
         foreach($Prods as $rows){  
           
        if($rows->Store == $store){     
             
           $Stores=SecretariatStores::all();  
    if($rows->Product()->first()->P_Type == 'Completed' or $rows->Product()->first()->P_Type == 'Raw' or $rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Service'){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first();    
             $plow=ProductUnits::where('Product',$rows->Product)->where('Rate',1)->first();      
                
         $purchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total_Bf_Tax');
     $countPurchs=ProductsPurchases::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

    $purchsStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('Total');
      $countStart=ProductsStartPeriods::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('Store',$store)->get()->sum('SmallQty');

               $storesTransfer=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('Total');
                
         $storesTransferCount=ProductsStoresTransfers::where('Product',$rows->Product)->where('SmallCode',$plow->Barcode)->where('To_Store',$store)->get()->sum('SmallTrans_Qty');        
                
          
                $OUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');     
           
$countOUTCOME=OutcomManufacturingModel::where('Product',$rows->Product)->where('Store',$store)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');      


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME; 
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;        
                
 
        if(!empty($x)){
        
         if(!empty($purchs) or !empty($purchsStart) or !empty($storesTransfer) or !empty($OUTCOME)){
           
               
   if($CollectCount != 0){
     $ty= ($Collect /  $CollectCount) * $rr->Rate ; 
             
             $pr=number_format((float)abs($ty), 2, '.', '');
                       
                   }else{
                       
                       $pr=$Collect * $rr->Rate;       
                   }         
             
         }else{
          $pr=$x->Price;   
             
         }        
            
            
        }else{
         $pr=$rr->Price;    
        }
      
        $st=SecretariatStores::find($store);
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Product_Code.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

      <td>
        <input type="number" id="Precent'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
        </td>
        
        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
       
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" >
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
 
        </td>
        
              



        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        }  
        }  

          
              foreach($data as $rows){  

           $Stores=SecretariatStores::all();  
     if($rows->P_Type == 'Service' or $rows->P_Type == 'Completed' or $rows->P_Type == 'Raw' or $rows->P_Type == 'Industrial' ){
        
            if($rows->Product()->first()->Status == 0){
                
     $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
           
         $pr=$rr->Price;    
      
        $st=SecretariatStores::find($store);
        $output .= '
        
       <tr id="Row'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_Name'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_Name'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="Product'.$rows->id.'" value="'.$rows->Product.'">
        <input type="hidden"  id="VOne'.$rows->id.'" value="'.$rows->V1.'">
        <input type="hidden"  id="VTwo'.$rows->id.'" value="'.$rows->V2.'">
        <input type="hidden"  id="V_Name'.$rows->id.'" value="">
        <input type="hidden"  id="VV_Name'.$rows->id.'" value="">
        </td>
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurch'.$rows->id.'" onchange="UnitCodePurchh('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
        <td>
         <input type="text" id="CodePurch'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchName'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
         <input type="hidden" id="TaxRate'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Rate.'"> 
         <input type="hidden" id="TaxType'.$rows->id.'" value="'.$rows->Product()->first()->Tax()->first()->Type.'"> 
         <input type="hidden" id="PurchTax'.$rows->id.'" value="'.$rows->Product()->first()->Tax.'"> 
        </td>
        

      <td>
        <input type="number" id="Precent'.$rows->id.'" step="any"   class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" > 
        </td>

        <td>
        <input type="number" id="Qty'.$rows->id.'" step="any"   class="form-control" disabled > 
        </td>
        
              <td>

 <input type="number" id="Price'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="'.$pr.'" disabled>
 <input type="hidden" id="Discount'.$rows->id.'" step="any"  class="form-control" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')" value="0" >
<input type="hidden" id="TotalBFTax'.$rows->id.'"   class="form-control" > 
<input type="hidden" id="Tax'.$rows->id.'"   class="form-control" >               
  <input type="hidden" id="Total'.$rows->id.'"   class="form-control" > 
        </td>
        
   
        
            <td>
   <select class="select2 form-control w-100"   id="StorePurch'.$rows->id.'" onchange="StoreNamePurch('.$rows->id.')">
              ';
             
            foreach($Stores as $stor){
           
                  $output .= '
      <option value="'.$stor->id.'"       ';  if($stor->id == $store){    $output .= '  selected  ';  }   $output .= ' > '.$stor->Arabic_Name.'</option>
                 ';
                        }
            
                $output .= '      
                  
                        </select>
        </td> 
        
        
             <td>
      <input type="number" step="any" id="DeprecPrecent'.$rows->id.'" onkeyup="PurchTotal('.$rows->id.')" onclick="PurchTotal('.$rows->id.')"   class="form-control"> 

          </td>    
       
          <td>
      <input type="number" step="any" id="Deprec'.$rows->id.'"   class="form-control" disabled > 
      <input type="hidden" id="StorePurchName'.$rows->id.'"   class="form-control"  value="'.$st->Arabic_Name.'" > 
          </td>
        
        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed" style="display:none" id="AddBtnPur'.$rows->id.'" onclick="Fun('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
        </tr>
        
       
            ';
        }
        
        }
        
        }  


      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
    
     function OutcomManufacturingModelSecretariatFilterPrecent(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
      $store = $request->get('store');             
                   
    if($search != '')
    {

        
        
        $DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
        if($DefSHOW->Search_Typical ==  1){
            
           $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get(); 
            
    $data =ProductUnits::      
            where('Barcode',$search)
        ->take(100)         
          ->get();  
            
            
 }else{
            
             $datas=Products::      
            where('P_Ar_Name','ILIKE', "%{$search}%")                  
            ->orWhere('P_En_Name','ILIKE', "%{$search}%")                                                                  
          ->take(100) 
          ->get();  
            
    $data =ProductUnits::      
            where('Barcode','ILIKE', "%{$search}%")
        ->take(100)         
          ->get();         
            
            
  } 
        
 
             
     }

   
         $total_row2 = $data->count();
         $total_row3 = $datas->count();
         $total_row4 = $total_row2 + $total_row3;
      if($total_row4 > 0) 
      { 
   
            foreach($datas as $rows){  
         
             
           $Stores=Stores::all();  
        
          if($rows->P_Type == 'Industrial' or $rows->P_Type == 'Completed'){       
            if($rows->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->id)->get();
         $rr=ProductUnits::where('Product',$rows->id)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->id)->first(); 

        $st=Stores::find($store);
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->id.'">
        </td>
        
        
                <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rr->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$rr->Unit()->first()->Name.'"> 
        </td>
        
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>
        
             <td>
        <input type="number" id="Workmanship_Price'.$rows->id.'" step="any" class="form-control" value="1"> 
        </td>
        

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
          }
        
          
        }  
          
          
           foreach($data as $rows){  
    
           $Stores=Stores::all();  
    if($rows->Product()->first()->P_Type == 'Industrial'  or $rows->Product()->first()->P_Type == 'Completed'){
        
            if($rows->Product()->first()->Status == 0){
                
        $units=ProductUnits::where('Product',$rows->Product)->get();
         $rr=ProductUnits::where('Product',$rows->Product)->where('Def',1)->first();
            $x = ProductsQty::where("Unit",$rr->Unit)->where('Product',$rows->Product)->first(); 

        $st=Stores::find($store);
        $output .= '
        
       <tr id="RowO'.$rows->id.'">
        <td>
        '.$rows->P_Ar_Name.'
 <input type="hidden"  id="P_Ar_NameO'.$rows->id.'" value="'.$rows->P_Ar_Name.'">
        <input type="hidden"  id="P_En_NameO'.$rows->id.'" value="'.$rows->P_En_Name.'">
        <input type="hidden"  id="ProductO'.$rows->id.'" value="'.$rows->Product.'">
        </td>
        
        
               <td>
         <input type="text" id="CodePurchO'.$rows->id.'" class="form-control" value="'.$rows->Barcode.'"  disabled> 
         <input type="hidden" id="UnitPurchNameO'.$rows->id.'" value="'.$rows->Unit()->first()->Name.'"> 
        </td>
        
        
        <td>
             <select class="select2 form-control w-100"   id="UnitPurchO'.$rows->id.'" onchange="UnitCodePurchhO('.$rows->id.')">
                <option value=""> اختر وحده</option>
              ';
             
            foreach($units as $uni){
                $nam=Measuerments::find($uni->Unit);
                  $output .= '
            <option value="'.$uni->Unit.'"       ';  if($uni->Def == 1){    $output .= '  selected  ';  }   $output .= ' > '.$nam->Name.'</option>
                 ';
                
          
                        }
            
                $output .= '      
                  
                        </select>
        </td>
 
        

        <td>
        <input type="number" id="QtyO'.$rows->id.'" step="any" class="form-control" value="1" disabled > 
        </td>
        
                <td>
        <input type="number" id="Workmanship_Price'.$rows->id.'" step="any" class="form-control" value="1"> 
        </td>

        <td>
 <button type="button" class="btn btn-default waves-effect waves-themed"  id="AddBtnPurO'.$rows->id.'" onclick="FunO('.$rows->id.')">
          <i class="fal fa-plus"></i>
          </button> 
          </td>
          
        </tr>
        
       
            ';
        }
        
        }
   
        }  
          
   
      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }
      
          public function AddManufacturingModelSecretariatPrecent(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Name'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

         
              
                            if(!empty(request('NameEn'))){
         $NameEn=request('NameEn');
          }else{
             $NameEn=request('Name'); 
              
          }

           $ID = DB::table('manufacturing_secretariat_models')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Name' => request('Name'),
            'NameEn' => $NameEn,
            'Time' => request('Time'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Product_Numbers' => request('Product_Numbers'),
            'Total_Discount' => request('Total_Discount'),
            'Total_BF_Taxes' => request('Total_BF_Taxes'),
            'Total_Taxes' => request('Total_Taxes'),
            'Total_Price' => request('Total_Price'),
            'Hall' => request('Hall'),
            'Coin' => request('Coin'),
            'Type' => 2,
            'Cost_Center' => request('Cost_Center'),
            'User' => auth()->guard('admin')->user()->id,

        )
    );  
        
    
          if(!empty(request('Unit'))){
            
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');
              $Precent=request('Precent');
            for($i=0 ; $i < count($Unit) ; $i++){

                $uuIn['Product_Code']=$P_Code[$i];
                $uuIn['P_Ar_Name']=$P_Ar_Name[$i];
                $uuIn['P_En_Name']=$P_En_Name[$i];
                $uuIn['Qty']=$Qty[$i];
                $uuIn['Cost']=$Price[$i];
                $uuIn['Discount']=$Discount[$i];
                $uuIn['Tax']=$PurchTax[$i];
                $uuIn['Total_Bf_Tax']=$TotalBFTax[$i];
                $uuIn['Total']=$Totaal[$i];
                $uuIn['Total_Tax']=$TotalTax[$i];
                $uuIn['Depreciation']=$DeprecPrecent[$i];
                $uuIn['Depreciation_Qty']=$Deprec[$i];
                $uuIn['Store']=$StorePurch[$i];
                $uuIn['Product']=$Product[$i];
                $uuIn['Unit']=$Unit[$i];
                $uuIn['Model']=$ID;
                $uu['Precent']=$Precent[$i];

               IncomManufacturingSecretariatModel::create($uuIn); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
            
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
              $Workmanship_Price=request('Workmanship_Price');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                     $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first();    
                 $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Workmanship_Price']=$Workmanship_Price[$i];
                $uu['Cost']=request('Total_Price');
                $uu['Model']=$ID;

               OutcomeManufacturingSecretariatModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='نموذج تصنيع بنسبه للغير';
           $dataUser['ScreenEn']='Manufacturing Model Precent To Others';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
                 
          if(request('SP') == 0){  return back(); }elseif(request('SP') == 1){  return redirect('ManuSecrtaritExecution/'.$ID); }  
            
            
        
    }

          public function EditManufacturingModelSecretariatPrecent($id){
        
               $CostCenters=CostCenter::all();
            $Coins=Coins::all();  
            $ManufacturingHalls=ManufacturingHalls::all();  
                $item=ManufacturingSecretariatModel::find($id);
         $Stores=SecretariatStores::all();
            $StoresOut=Stores::all();
          
          
 $Outs=OutcomeManufacturingSecretariatModel::where('Model',$item->id)->get(); 
        $Ins=IncomManufacturingSecretariatModel::where('Model',$item->id)->get();      
         return view('admin.Secretariat.EditManufacturingModelSecretariatPrecent',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'item'=>$item,
             'Outs'=>$Outs,
             'Ins'=>$Ins,
             'Stores'=>$Stores,
             'StoresOut'=>$StoresOut,
             'ManufacturingHalls'=>$ManufacturingHalls,
         ]);
    }
    
     public function PostEditManufacturingModelSecretariatPrecent(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Name'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      
            'Coin.required' => trans('admin.CoinRequired'),      
            'Draw.required' => trans('admin.TDrawRequired'),      


         ]);

         $id=request('ID');
  
            $data['Code'] = request('Code');
            $data['Date'] = request('Date');
            $data['Name'] = request('Name');
            $data['NameEn'] = request('NameEn');
            $data['Time'] = request('Time');
            $data['Draw' ]=request('Draw');
            $data['Note'] = request('Note');
            $data['Product_Numbers'] = request('Product_Numbers');
            $data['Total_Discount'] = request('Total_Discount');
            $data['Total_BF_Taxes'] = request('Total_BF_Taxes');
            $data['Total_Taxes'] = request('Total_Taxes');
            $data['Total_Price'] = request('Total_Price');
            $data['Hall'] = request('Hall');
            $data['Coin'] = request('Coin');
            $data['Cost_Center'] = request('Cost_Center');

         ManufacturingSecretariatModel::where('id',$id)->update($data);
         

          if(!empty(request('Unit'))){
            IncomManufacturingSecretariatModel::where('Model',$id)->delete();
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Unit=request('Unit');
              $P_Code=request('P_Code');
              $Qty=request('Qty');
              $Price=request('Price');
              $Discount=request('Discount');
              $TotalBFTax=request('TotalBFTax');
              $TotalTax=request('TotalTax');
              $Totaal=request('Total');
              $StorePurch=request('StorePurch');
              $DeprecPrecent=request('DeprecPrecent');
              $Deprec=request('Deprec');
              $Product=request('Product');
              $PurchTax=request('PurchTax');
 $Precent=request('Precent');
            for($i=0 ; $i < count($Unit) ; $i++){

                $uuIn['Product_Code']=$P_Code[$i];
                $uuIn['P_Ar_Name']=$P_Ar_Name[$i];
                $uuIn['P_En_Name']=$P_En_Name[$i];
                $uuIn['Qty']=$Qty[$i];
                $uuIn['Cost']=$Price[$i];
                $uuIn['Discount']=$Discount[$i];
                $uuIn['Tax']=$PurchTax[$i];
                $uuIn['Total_Bf_Tax']=$TotalBFTax[$i];
                $uuIn['Total']=$Totaal[$i];
                $uuIn['Total_Tax']=$TotalTax[$i];
                $uuIn['Depreciation']=$DeprecPrecent[$i];
                $uuIn['Depreciation_Qty']=$Deprec[$i];
                $uuIn['Store']=$StorePurch[$i];
                $uuIn['Product']=$Product[$i];
                $uuIn['Unit']=$Unit[$i];
                $uuIn['Model']=$id;
                $uu['Precent']=$Precent[$i];

               IncomManufacturingSecretariatModel::create($uuIn); 
                
                 
            }  

              
          }

          if(!empty(request('UnitO'))){
              OutcomeManufacturingSecretariatModel::where('Model',$id)->delete();
              $P_Ar_NameO=request('P_Ar_NameO');
              $P_En_NameO=request('P_En_NameO');
              $UnitO=request('UnitO');
              $P_CodeO=request('P_CodeO');
              $QtyO=request('QtyO');
              $ProductO=request('ProductO');
              $Workmanship_Price=request('Workmanship_Price');
             

            for($i=0 ; $i < count($UnitO) ; $i++){

                     $pp=ProductUnits::where('Product',$ProductO[$i])->where('Unit',$UnitO[$i])->first();    
                 $plow=ProductUnits::where('Product',$ProductO[$i])->where('Rate',1)->first();  
                $uu['SmallCode']=$plow->Barcode;
                $uu['Product_Code']=$P_CodeO[$i];
                $uu['P_Ar_Name']=$P_Ar_NameO[$i];
                $uu['P_En_Name']=$P_En_NameO[$i];
                $uu['Qty']=$QtyO[$i];
                $uu['SmallQty']=$QtyO[$i] * $pp->Rate;
                $uu['Store']=request('StoreOut');
                $uu['Product']=$ProductO[$i];
                $uu['Unit']=$UnitO[$i];
                $uu['Workmanship_Price']=$Workmanship_Price[$i];
                $uu['Cost']=request('Total_Price');
                $uu['Model']=$id;

               OutcomeManufacturingSecretariatModel::create($uu); 
                
                 
            }  

              
          }

          
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                            $dataUser['Screen']='نموذج تصنيع بنسبه للغير';
           $dataUser['ScreenEn']='Manufacturing Model Precent To Others';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Updated'));
          return redirect('ManufacturingModelSecretariatSechdule');
            
        
    }
    
    


    //ExecutingReceivingSecretariat
         public function ExecutingReceivingSecretariat(){
        $items=ExecutingReceivingSecretariat::paginate(100);
         
                   $res=ExecutingReceivingSecretariat::orderBy('id','desc')->first();
           
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
               
           }else{
               
              $Code=1; 
               
           }
            
         $Models=ManufacturingSecretariatModel::all();
         $Branches=Branches::all();
              $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
         
         return view('admin.Secretariat.ExecutingReceivingSecretariat',['items'=>$items,'Code'=>$Code,'Models'=>$Models,'Branches'=>$Branches,'Clients'=>$Clients]);
    }
   
    function ModelExecutingReceivingSecretariatFilter(Request $request)
             {
           
     if($request->ajax())
     { 
      $output = '';
      $search = $request->get('search');             
                                                  
    if($search != '')
    {

            $modal=ManufacturingSecretariatModel::find($search);      
           
        $Out=OutcomeManufacturingSecretariatModel::where('Model',$search)->first();
        $Ins=IncomManufacturingSecretariatModel::where('Model',$search)->get();
        $InsCount=IncomManufacturingSecretariatModel::where('Model',$search)->count();
       
     }

      if(!empty($modal)) 
      { 
  $i=1;
          
           $output .= '

             <input type="hidden"  name="Product_Code[]" value="'.$Out->Product_Code.'">
             <input type="hidden"  name="P_Ar_Name[]" value="'.$Out->P_Ar_Name.'">
             <input type="hidden"  name="P_En_Name[]" value="'.$Out->P_En_Name.'">
             <input type="hidden" step="any" class="form-control"  name="Qty[]" value="'.$Out->Qty.'">
             <input type="hidden"  name="Price[]" value="'.$modal->Total_Price.'">
             <input type="hidden"  name="Product[]" value="'.$Out->Product.'">
             <input type="hidden"  name="Store[]" value="'.$Out->Store.'">
             <input type="hidden"  name="Unit[]" value="'.$Out->Unit.'">
             <input type="hidden"  name="Workmanship_Price[]" id="Workmanship_Price" value="'.$Out->Workmanship_Price.'">
     
       
            ';
          
          
        foreach($Ins as $in){
           $Qunta=SecretariatQty::where('Product',$in->Product)->where('Product_Code',$in->Product_Code)->where('Store',$in->Store)->get()->sum('Qty');
               $output .= '
             <tr>
        <td>
        '.$in->Product_Code.'
             <input type="hidden"  name="Product_CodeI[]" value="'.$in->Product_Code.'">
        </td>
            <td>
        '.$in->P_Ar_Name.'
             <input type="hidden"  name="P_Ar_NameI[]" value="'.$in->P_Ar_Name.'">
             <input type="hidden"  name="P_En_NameI[]" value="'.$in->P_En_Name.'">
        </td>
        <td>'.$Qunta.'
                        <input type="hidden"  id="StoreQty'.$i.'" value="'.$Qunta.'"> 
        </td>
        <td>
    <input type="number" step="any" id="DepreciationQty'.$i.'" class="form-control" name="QtyI[]"  value="'.$in->Depreciation_Qty.'" >
                <input type="hidden" id="OriginalDepreciationQty'.$i.'"   value="'.$in->Depreciation_Qty.'"> 
        </td>
        
             <td>
             <input type="number" step="any" id="Qtyy'.$i.'" class="form-control"   value="'.$in->Qty.'" disabled>
                <input type="hidden" id="OriginalQtyy'.$i.'" name="QtyHide[]" value="'.$in->Qty.'">  
                <input type="hidden" class="MO" id="More'.$i.'"  value="0">  
        </td>
        
                  <td>
        '.$in->Store()->first()->Arabic_Name.'
             <input type="hidden"  name="PriceI[]" value="'.$in->Total.'">
             <input type="hidden"  name="ProductI[]" value="'.$in->Product.'">
             <input type="hidden"  name="StoreI[]" value="'.$in->Store.'">
        </td>
        
                  <td>
        '.$in->Unit()->first()->Name.'
             <input type="hidden"  name="UnitI[]" value="'.$in->Unit.'">
        </td>
        
        <td>
        
        <label>هل تريد  ادخال الكميه للمخزن</label>
   
<select class="select2 form-control" name="inStore[]">
<option value="0">لا</option>
<option value="1">نعم</option>
</select>
</td>
          
        </tr>
        
       
            ';
            
      $i++;      
        }  
          


      }else
      {
       $output = '
        <div class="col-md-3">لا يوجد صنف كهذا</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
       'qty'  => $Out->Qty,
       'tot'  => $modal->Total_Price,
       'Outcome'  => $Out->P_Ar_Name,
       'count'  => $InsCount,
       '_Price'  => $Out->Qty * $Out->Workmanship_Price,
      );
      echo json_encode($data);
     }
    }
    
      public function AddExecutingReceivingSecretariat(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Model'=>'required',

               ],[
          
            'Date.required' => trans('admin.DateRequired'),      


         ]);

      $in=IncomManufacturingSecretariatModel::where('Model',request('Model'))->orderBy('id','asc')->first();
$out=OutcomeManufacturingSecretariatModel::where('Model',request('Model'))->first();
    
 
           $ID = DB::table('executing_receiving_secretariats')->insertGetId(
        array(
            
            'Code' => request('Code'),
            'Date' => request('Date'),
            'Qty' => request('Qtyy'),
            'Total' => request('Totall')*request('Qtyy'),
            'Model' => request('Model'),
            'Client' => request('Client'),
            'User' => auth()->guard('admin')->user()->id,
            'Time' =>date("h:i:s a", time()),
            'Branch' =>request('Branch'),
            'Total_Workmanship_Price' =>request('Total_Workmanship_Price'),
            'StoreIn' =>$in->Store,
            'StoreOut' =>$out->Store,

        )
    );  
          
          
          
          
          $c= DB::select("SELECT last_value FROM executing_receiving_secretariats_arr_seq");
      $f=array_shift($c);
      $z=end($f);    
  $CodeT=$z;   

$ins=IncomManufacturingSecretariatModel::where('Model',request('Model'))->get();
$out=OutcomeManufacturingSecretariatModel::where('Model',request('Model'))->first();
    
          //Out

          if(!empty(request('Unit'))){
            
              $Product_Code=request('Product_Code');
              $P_Ar_Name=request('P_Ar_Name');
              $P_En_Name=request('P_En_Name');
              $Qty=request('Qty');
              $Price=request('Price');
              $Product=request('Product');
              $Store=request('Store');
              $Unit=request('Unit');   
              $Workmanship_Price=request('Workmanship_Price');
            for($i=0 ; $i < count($Unit) ; $i++){

                $uu['Product_Code']=$Product_Code[$i];
                $uu['P_Ar_Name']=$P_Ar_Name[$i];
                $uu['P_En_Name']=$P_En_Name[$i];
                $uu['Qty']=$Qty[$i];
                $uu['Price']=$Price[$i];
                $uu['Total']=$Price[$i];
                $uu['StoreIn']=$in->Store;
                $uu['StoreOut']=$out->Store;
                $uu['Product']=$Product[$i];
                $uu['Unit']=$Unit[$i];
                $uu['type']=1;
                $uu['Executing']=$ID;
                $uu['Workmanship_Price']=$Workmanship_Price[$i];
               ProductExecutingReceivingSecretariat::create($uu); 
       
                
                                 $Quantity =SecretariatQty::
                where('Store',$out->Store)    
                ->where('Product',$Product[$i])    
                ->where('Product_Code',$Product_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$out->Store)    
                ->where('Product',$Product[$i])    
                ->where('PP_Code',$Product_Code[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$out->Store)    
                ->where('Product',$Product[$i])    
                ->where('PPP_Code',$Product_Code[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$out->Store)    
                ->where('Product',$Product[$i])    
                ->where('PPPP_Code',$Product_Code[$i])    
                ->first(); 

}
}
}


                   $unit=ProductUnits::where('Unit',$Unit[$i])->where('Product',$Product[$i])->first();
                    if(!empty($Quantity)){
 
           $qq= $unit->Rate * ($Qty[$i] * request('Qtyy'));
                
           $newqty=$Quantity->Qty +  $qq ; 
            

             SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]);              
               
   
                    }else{
                        
                        
                    $pqty['P_Ar_Name']=$P_Ar_Name[$i];
                    $pqty['P_En_Name']=$P_En_Name[$i];
                    $pqty['Product_Code']=$Product_Code[$i];
                    $pqty['Qty']=$Qty[$i];
                    $pqty['Store']=$out->Store;
                    $pqty['Unit']=$Unit[$i];
                    $pqty['Product']=$Product[$i];
                   $pqty['V1']=null;
                    $pqty['V2']=null;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;   
                       $pqty['PP_Code']=null; 
                         $pqty['PPP_Code']=null; 
                              $pqty['PPPP_Code']=null;   
     
              SecretariatQty::create($pqty);              
                        
                        
                        
                    }
               
                           
   
                
                
            }  

              
          }

          //In
          
          if(!empty(request('UnitI'))){
            
              $Product_CodeI=request('Product_CodeI');
              $P_Ar_NameI=request('P_Ar_NameI');
              $P_En_NameI=request('P_En_NameI');
              $QtyI=request('QtyHide');
              $Dep=request('QtyI');
              $PriceI=request('PriceI');
              $ProductI=request('ProductI');
              $StoreI=request('StoreI');
              $UnitI=request('UnitI');
          
              $INSTORE=request('inStore');

            for($i=0 ; $i < count($UnitI) ; $i++){

                $uuz['Product_Code']=$Product_CodeI[$i];
                $uuz['P_Ar_Name']=$P_Ar_NameI[$i];
                $uuz['P_En_Name']=$P_En_NameI[$i];
                $uuz['Qty']=$QtyI[$i];
                $uuz['Dep']=$Dep[$i];
                $uuz['Price']=$PriceI[$i];
                $uuz['Total']=$PriceI[$i];
                $uuz['StoreIn']=$in->Store;
                $uuz['StoreOut']=$out->Store;
                $uuz['Product']=$ProductI[$i];
                $uuz['Unit']=$UnitI[$i];
                   $uuz['type']=0;
                $uuz['Executing']=$ID;

               ProductExecutingReceivingSecretariat::create($uuz); 

                 $Quantity =SecretariatQty::
                where('Store',$in->Store)    
                ->where('Product',$ProductI[$i])    
                ->where('Product_Code',$Product_CodeI[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$in->Store)    
                ->where('Product',$ProductI[$i])    
                ->where('PP_Code',$Product_CodeI[$i])    
                ->first(); 

if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$in->Store)    
                ->where('Product',$ProductI[$i])    
                ->where('PPP_Code',$Product_CodeI[$i])    
                ->first(); 


if(empty($Quantity)){

  $Quantity =SecretariatQty::
                where('Store',$in->Store)    
                ->where('Product',$ProductI[$i])    
                ->where('PPPP_Code',$Product_CodeI[$i])    
                ->first(); 

}
}
}


                   $unit=ProductUnits::where('Unit',$UnitI[$i])->where('Product',$ProductI[$i])->first();
                    if(!empty($Quantity)){
 
           $qq= $unit->Rate * ($QtyI[$i] * request('Qtyy'));
                
           $newqty=$Quantity->Qty -  $qq ; 
            

             SecretariatQty::where('id',$Quantity->id)->update(['Qty'=>$newqty]);              
               
   
                    }
               
                
                
     
            }  

              
          }


          $model=ManufacturingSecretariatModel::find(request('Model'));

                $NewMkhazns=ManuStoreCount::all();

               $res=Journalizing::orderBy('id','desc')->first();
         
           if(!empty($res->Code)){
               
              $Code=$res->Code + 1 ; 
           }else{
              $Code=1; 
               
           }    

        $JunID = DB::table('journalizings')->insertGetId(
            
        array(
            
            'Code' => $Code,
            'Type' => 'التصنيع',
            'TypeEn' => 'Manufacturing',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => $model->Draw,
            'Coin' =>$model->Coin,
            'Cost_Center' => $model->Cost_Center,
            'Total_Debaitor' => $model->Total_Price * request('Qtyy'),
            'Total_Creditor' => $model->Total_Price * request('Qtyy'),
            'Note' => null,
  
        )
    );
         
          $account=AcccountingManual::where('Name','ايراد مصنعيه')->first();
    
            $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Workmanship_Price');
        $PRODUCTSS['Account']=$account->id;
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='التصنيع';
        $Gen['TypeEn']= 'Manufacturing';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Workmanship_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * 0;
        $Gen['Creditor_Coin']=$model->Draw * request('Total_Workmanship_Price');
        $Gen['Account']=$account->id;
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']=$model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      
    
     
        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Workmanship_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Client');
        $PRODUCTSS['Statement']=null;
      
    
         JournalizingDetails::create($PRODUCTSS);      
   
        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
          $Gen['Type']='التصنيع';
        $Gen['TypeEn']= 'Manufacturing';
        $Gen['Debitor']=request('Total_Workmanship_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$model->Draw;
        $Gen['Debitor_Coin']= $model->Draw * request('Total_Workmanship_Price');
        $Gen['Creditor_Coin']=$model->Draw * 0;
        $Gen['Account']=request('Client');
        $Gen['Coin']= $model->Coin;
        $Gen['Cost_Center']= $model->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);      



           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='تنفيذ و استلام للغير';
           $dataUser['ScreenEn']='Executing and Receiving to Others';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);
         
             session()->flash('success',trans('admin.Added_Successfully'));
             return back();
            
        
    }
    
    

    
}

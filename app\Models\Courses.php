<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Courses extends Model
{
    use HasFactory;
     protected $table = 'courses';
      protected $fillable = [
        'Code',
        'Arabic_Name',
        'English_Name',
        'Arabic_Desc',
        'English_Desc',
        'Category',
        'Lec_Num',
        'Hours',
        'Subject',
        'Note',
    
    ];

         public function Category()
    {
        return $this->belongsTo(CoursesCategory::class,'Category');
    }
         public function Subject()
    {
        return $this->belongsTo(ScientificMaterial::class,'Subject');
    }
    
}

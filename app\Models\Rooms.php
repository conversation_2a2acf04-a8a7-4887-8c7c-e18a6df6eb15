<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rooms extends Model
{
    use HasFactory;
       protected $table = 'rooms';
      protected $fillable = [
        'Code',
        'Floor',
        'Bulding_Name',
        'Adults_Num',
        'Childs_Num',
        'Beds_Num',
        'Desc',
        'DescEn',
        'Price',
        'Reserved',
        'RoomsType',
   
    ];
    
               public function RoomsType()
    {
        return $this->belongsTo(RoomsType::class,'RoomsType');
    }
}

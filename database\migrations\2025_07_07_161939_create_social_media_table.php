<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSocialMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('social_media', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Facebook')->nullable();
            $table->string('Twitter')->nullable();
            $table->string('Instagram')->nullable();
            $table->string('Youtube')->nullable();
            $table->string('Snapchat')->nullable();
            $table->string('Whatsapp')->nullable();
            $table->string('Google_Plus')->nullable();
            $table->string('LinkedIn')->nullable();
            $table->string('Pinterest')->nullable();
            $table->string('Telegram')->nullable();
            $table->string('iOS')->nullable();
            $table->string('Android')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('social_media');
    }
}
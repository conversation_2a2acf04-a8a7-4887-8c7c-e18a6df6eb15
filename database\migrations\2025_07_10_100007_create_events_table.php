<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('events', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Start_Date')->nullable();
            $table->string('End_Date')->nullable();
            $table->string('Event_Ar_Name');
            $table->string('Event_En_Name');
            $table->string('Type')->nullable();
            $table->string('Type_ID')->nullable();
            $table->string('Type_Code')->nullable();
            $table->string('Emp')->nullable();
            $table->string('Client')->nullable();
            $table->string('Product')->nullable();
            $table->string('Customer')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('events');
    }
}

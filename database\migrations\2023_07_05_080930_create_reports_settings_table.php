<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReportsSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('reports_settings', function (Blueprint $table) {
            $table->id();
            $table->longText('Product_Info')->nullable();
            $table->longText('Product_Order_Limit')->nullable();
            $table->longText('ReportStartPeriodProducts')->nullable();
            $table->longText('SettlementsReports')->nullable();
            $table->longText('StoresCost')->nullable();
            $table->longText('StoresInventory')->nullable();
            $table->longText('Collection_Delegates')->nullable();
            $table->longText('Sales_Delegates')->nullable();
            $table->longText('StagnantItems')->nullable();
            $table->longText('ItemsMoves')->nullable();     
            $table->longText('StoresBalancesTwo')->nullable();
            $table->longText('NetPurchases')->nullable();
            $table->longText('NetSales')->nullable();
            $table->longText('ClientSales')->nullable();
            $table->longText('ExecutorSales')->nullable();
            $table->longText('InstallmentReport')->nullable();
            $table->longText('ExpiredProucts')->nullable();
            $table->longText('StoresSalesDetails')->nullable();
            $table->longText('TotalNetPurchases')->nullable();
            $table->longText('TotalNetSales')->nullable(); 
            $table->longText('Profits')->nullable();
            $table->longText('Shifts')->nullable();
            $table->longText('Shifts_Details')->nullable();
            $table->longText('DailyClosing')->nullable();
            $table->longText('Products')->nullable();
            $table->longText('DailyShifts')->nullable();
            $table->longText('ExpensesReport')->nullable();
            $table->longText('DailyProducts')->nullable();
            $table->longText('EmployeeCommissionDiscounts')->nullable();
            $table->longText('VendorPricesReport')->nullable();     
            $table->longText('DailyMoves')->nullable();
            $table->longText('GroupsSales')->nullable();
            $table->longText('VendorPurchases')->nullable();
            $table->longText('ExceptProfits')->nullable();
            $table->longText('DelegateSalesDetails')->nullable();
            $table->longText('CreditStores')->nullable();
            $table->longText('ProductProfits')->nullable();
            $table->longText('ExceptProductProfits')->nullable();
            $table->longText('SalesBills')->nullable();
            $table->longText('PurchasesBills')->nullable(); 
            $table->longText('StoresMovesReport')->nullable();
            $table->longText('StoresTransferReport')->nullable();
            $table->longText('SafesTransferReport')->nullable();
            $table->longText('CompareSalesPrice')->nullable();
            $table->longText('ProductMoveDetails')->nullable();
            $table->longText('MostSalesProducts')->nullable();
            $table->longText('ProfitSalesProduct')->nullable();
            $table->longText('ClientAccountStatement')->nullable();
            $table->longText('ClientsStatement')->nullable();
            $table->longText('VendorAccountStatement')->nullable();     
            $table->longText('VendorsStatement')->nullable();
            $table->longText('EmpGoals')->nullable();
            $table->longText('InventorySerial')->nullable();
            $table->longText('TotalExpensesSafes')->nullable();
            $table->longText('SubIncomList')->nullable();
            $table->longText('ExpensesList')->nullable();
            $table->longText('StoresBalances')->nullable();
            $table->longText('StoresBalancesCat')->nullable();
            $table->longText('ItemCost')->nullable();
            $table->longText('StoresInventoryy')->nullable();   
            $table->longText('DelegateSalesDetailss')->nullable();
            $table->longText('ProfitDelegateSalesDetails')->nullable();
            $table->longText('InstallmentCompaniesSales')->nullable();
            $table->longText('StoresCosts')->nullable();
            $table->longText('DailyClosingDetails')->nullable();
            $table->longText('SalesProsMoreDetails')->nullable();
            $table->longText('StagnantItemss')->nullable();
            $table->longText('SalesCustomersGroups')->nullable();
            $table->longText('BrandsSales')->nullable();
            $table->longText('Customer_Debts')->nullable();
            $table->longText('Vendor_Debts')->nullable();
            $table->longText('MaintanceSalesReport')->nullable();
            $table->longText('Maintenance_Tune')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('reports_settings');
    }
}

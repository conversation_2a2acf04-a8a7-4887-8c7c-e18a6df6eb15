<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateManufacturingDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('manufacturing_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Coin')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Hall')->nullable();
            $table->string('Manu_Type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('manufacturing_default_data');
    }
}
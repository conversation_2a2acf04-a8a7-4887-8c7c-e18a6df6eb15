<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmpsProducationPoint extends Model
{
    use HasFactory;
      protected $table = 'emps_producation_points';
      protected $fillable = [
        'Month',
        'Point',
        'Emp',
        'Date',
      
    ];
    
    
       public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
}

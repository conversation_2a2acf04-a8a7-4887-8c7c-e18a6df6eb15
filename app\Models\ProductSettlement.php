<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSettlement extends Model
{
    use HasFactory;
      protected $table = 'product_settlements';
      protected $fillable = [
        'Av_Qty',
        'Inventory',
        'Deficit',
        'Excess',
        'Set_ID',
        'Product',
        'P_Code',
        'Price',
        'TotalDificitP',
        'TotalExcessP',
        'V1',
        'V2',
        'Unit',
        'Store',
        'Coin',
        'User',
        'Date',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
          
   
    ];
    
           public function Set_ID()
    {
        return $this->belongsTo(Settlement::class,'Set_ID');
    }
    
               public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
               public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
               public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
               public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
               public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
              public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentImportant extends Model
{
    use HasFactory;
      protected $table = 'student_importants';
      protected $fillable = [

                'Student',
                'Course',
      

    ];
    
    
        public function Student()
    {
        return $this->belongsTo(Students::class,'Student');
    }    
    
    
       
        public function Course()
    {
        return $this->belongsTo(Courses::class,'Course');
    }
    
    
}

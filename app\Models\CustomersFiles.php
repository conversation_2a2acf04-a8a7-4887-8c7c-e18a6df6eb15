<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomersFiles extends Model
{
    use HasFactory;
            protected $table = 'customers_files';
    protected $fillable = [
        'File',
        'Customer',

    ];
   
           public function Customer()
    {
        return $this->belongsTo(Customers::class,'Customer');
    }
}

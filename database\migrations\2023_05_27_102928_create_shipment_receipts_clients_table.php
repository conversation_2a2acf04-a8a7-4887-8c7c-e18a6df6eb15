<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentReceiptsClientsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipment_receipts_clients', function (Blueprint $table) {
            $table->id();
               $table->longText('Code')->nullable();
               $table->longText('Date')->nullable();
               $table->longText('ShipmentReceipts')->nullable();
               $table->longText('ShipmentReceiptsList')->nullable();
               $table->longText('Total_Price')->nullable();
               $table->longText('Total_Qty')->nullable();
               $table->longText('Pay')->nullable();
               $table->longText('Payment_Method')->nullable();
               $table->longText('Safe')->nullable();
               $table->longText('Coin')->nullable();
               $table->longText('Draw')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipment_receipts_clients');
    }
}

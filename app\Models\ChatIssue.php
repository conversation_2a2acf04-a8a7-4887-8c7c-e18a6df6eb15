<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatIssue extends Model
{
    use HasFactory;
          protected $table = 'chat_issues';
      protected $fillable = [
        'Name',
        'Date',
        'Time',
        'Desc',
        'Image',
        'Issue',
        'Type',
        'Appear',
      
    ];
    
    
       public function Issue()
    {
        return $this->belongsTo(Issues::class,'Issue');
    }
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class Vendorss implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         //DB::table('products')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('vendors')->insert([

     
            'Code'	   =>$value[1]
            ,'Name'  =>$value[2]
            ,'Phone'  =>$value[3]
            ,'Phone2'  =>$value[4]
            ,'Commercial_Register'  =>$value[5]
            ,'Tax_Card'  =>$value[6]
            ,'Price_Level'  =>$value[7]
            ,'Account'  =>$value[8]
            ,'User'  =>$value[9]
            ,'created_at'  =>$value[10]
            ,'updated_at'  =>$value[11]
            ,'Governrate'  =>$value[12]
            ,'City'  =>$value[13]
            ,'Place'  =>$value[14]
            ,'Tax_Registration_Number'  =>$value[15]
            ,'Tax_activity_code'  =>$value[16]
            ,'work_nature'  =>$value[17]
            ,'Nationality'  =>$value[18]
            ,'Buliding_Num'  =>$value[19]
            ,'Street'  =>$value[20]
            ,'Postal_Code'  =>$value[21]
            ,'tax_magistrate'  =>$value[22]
           

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}
	

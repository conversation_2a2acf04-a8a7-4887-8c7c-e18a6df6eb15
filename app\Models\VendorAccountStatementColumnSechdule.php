<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorAccountStatementColumnSechdule extends Model
{
    use HasFactory;
                protected $table = 'vendor_account_statement_column_sechdules';
      protected $fillable = [

        'Date',               
        'Code',               
        'Time',               
        'Refrence_Number',               
        'Branch',               
        'Store',               
        'Payment_Method',               
        'Safe',               
        'Type',               
        'Shipping',               
        'Cost_Center',                                     
        'User',               
        'Coin',               
        'Due_Date',               
        'Delegate',               
        'Note',               
        'Total_Return',               
        'Total_Price',               
        'Total_Discount',               
        'Total_Tax',               
        'Total_Net',               
        'Paid',               
        'Residual',    
        'Vendor',    
          
           


    ];

}

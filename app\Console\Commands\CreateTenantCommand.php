<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant;

class CreateTenantCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create-localhost';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates a tenant for localhost';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Tenant::create(['id' => 'localhost'])->domains()->create(['domain' => 'localhost']);
        $this->info('Tenant for localhost created successfully.');
        return 0;
    }
}
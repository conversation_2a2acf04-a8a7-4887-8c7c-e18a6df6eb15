<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission;
class PackPrem extends Model
{
    use HasFactory;
        protected $table = 'pack_prems';
      protected $fillable = [
        'package',
        'premission',
       
          
    ];
    
    
                   public function premission()
    {
        return $this->belongsTo(Permission::class,'premission');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpireDateQty extends Model
{
    use HasFactory;
     protected $table = 'expire_date_qties';
      protected $fillable = [
        'Qty',
        'P_Ar_Name',
        'P_En_Name',
        'P_Code',  
        'PP_Code',  
        'PPP_Code',  
        'PPPP_Code',  
        'V_Name',  
        'VV_Name',  
        'Store',
        'Unit',
        'Product',
        'V1',
        'V2',
        'Low_Unit',
        'Exp_Date',
        'Group',
        'Brand',
        'Branch',
       
    ];
    
           public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function Pro_Stores()
    {
        return $this->belongsTo(ProductsStores::class,'Pro_Stores');
    }
    
         public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
         public function Low_Unit()
    {
        return $this->belongsTo(Measuerments::class,'Low_Unit');
    }
    

             public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
         public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
           public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }
    
              public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }
}

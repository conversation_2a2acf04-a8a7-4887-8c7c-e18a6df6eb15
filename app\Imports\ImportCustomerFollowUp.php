<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ImportCustomerFollowUp implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('comments_clients')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('comments_clients')->insert([

         
            'Comment'	   =>$value[1]
            ,'Responsible'  =>$value[2]
            ,'Customer'  =>$value[3]
            ,'created_at'  =>$value[4]
            ,'updated_at'  =>$value[5]
            ,'CommentEn'  =>$value[6]
            ,'Code'  =>$value[7]
            ,'Date'  =>$value[8]
            ,'Rate'  =>$value[9]
            ,'Visit_Cost'  =>$value[10]
            ,'Note'  =>$value[11]
                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

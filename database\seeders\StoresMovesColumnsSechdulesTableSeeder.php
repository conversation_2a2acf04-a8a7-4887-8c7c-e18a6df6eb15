<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class StoresMovesColumnsSechdulesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('stores_moves_columns_sechdules')->delete();
        
        \DB::table('stores_moves_columns_sechdules')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Branch' => '1',
                'Store' => '1',
                'Safe' => '1',
                'Type' => '1',
                'Cost_Center' => '1',
                'User' => '1',
                'Coin' => '1',
                'Note' => '1',
                'Total_Qty' => '1',
                'Total_Price' => '1',
                'Account' => '1',
                'Ship' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-08 11:10:10',
            ),
        ));
        
        
    }
}
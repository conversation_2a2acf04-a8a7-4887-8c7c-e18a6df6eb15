<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ImportExporting_Checks implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('export_checks')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('export_checks')->insert([

         
            'Code'	   =>$value[1]
            ,'Date'  =>$value[2]
            ,'Draw'  =>$value[3]
            ,'Note'  =>$value[4]
            ,'Check_Num'  =>$value[5]
            ,'Due_Date'  =>$value[6]
            ,'Amount'  =>$value[7]
            ,'Status'  =>$value[8]
            ,'Reason'  =>$value[9]
            ,'Check_Type'  =>$value[10]
            ,'Coin'  =>$value[11]
            ,'Cost_Center'  =>$value[12]
            ,'Account'  =>$value[13]
            ,'Bank'  =>$value[14]
            ,'Pay_Account'  =>$value[15]
            ,'Bene_Account'  =>$value[16]
            ,'User'  =>$value[17]
            ,'created_at'  =>$value[18]
            ,'updated_at'  =>$value[19]            
          

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

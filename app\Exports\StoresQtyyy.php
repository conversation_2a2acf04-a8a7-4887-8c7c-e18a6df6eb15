<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use DB;
class StoresQtyyy implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result
     //   $records = ProductsQty::select('P_Ar_Name','P_Code','Qty','Store')->where('Store',$this->store)->get();
        
      

        $storex=$this->store;
        $storee =  $storex['store'];
         $group = $storex['group'] ;
        $brand =  $storex['brand'] ;
         $branch = $storex['branch'] ;
        $product_Name =   $storex['product_Name'] ;
         $product_Code = $storex['product_Code'] ;
         $zero = $storex['zero'] ;
        
             if(app()->getLocale() == 'ar' ){  
      if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  ->select('products_qties.P_Ar_Name','products_qties.P_Code','products_qties.Qty','product_units.Price','products_qties.Price','stores.Name as StoreName')
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->select('products_qties.P_Ar_Name','products_qties.P_Code','products_qties.Qty','product_units.Price','products_qties.Price','stores.Name as StoreName')
                  ->get();
        
      }
      
             }else{
                 
                 
          if($zero == 0){
          
           $prods = DB::table('products_qties')
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  ->select('products_qties.P_En_Name','products_qties.P_Code','products_qties.Qty','product_units.Price','products_qties.Price','stores.NameEn as StoreName')
                  ->get();
          
      }else{        
          $prods = DB::table('products_qties')->where('products_qties.Qty','!=',0)
   
                ->when(!empty($storee), function ($query) use ($storee) {
        return $query->where('products_qties.Store',$storee);
    })   
              
      ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('products_qties.Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('products_qties.Brand', $brand);
    })  
       
     
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('products_qties.Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('products_qties.P_Ar_Name','ILIKE', "%{$product_Name}%" );
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('products_qties.P_Code', $product_Code);
    })                
            
               ->join('products', function ($join) {
    
            $join->on('products.id', '=', 'products_qties.Product')
    
                ;
        })     
               
              
                   ->join('product_units', function ($join) {
    
            $join->on('products.id', '=', 'product_units.Product')
               ->join('measuerments','measuerments.id', '=', 'product_units.Unit')
               ->where('product_units.Def', '=', 1)
                ;
        })       
  
          ->join('stores', function ($join) {
    
            $join->on('products_qties.Store', '=', 'stores.id');
        })    
  
->select('products_qties.P_En_Name','products_qties.P_Code','products_qties.Qty','product_units.Price','products_qties.Price','stores.NameEn as StoreName')
                  ->get();
        
      }         
                 
                 
             }

        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'P_Ar_Name',
          'P_Code',
          'Qty',
          'Price',
          'Cost',
          'Store'
        ];
    }
    
    
    

}

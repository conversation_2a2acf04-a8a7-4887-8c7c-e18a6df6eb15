<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintaincBill extends Model
{
    use HasFactory;
     protected $table = 'maintainc_bills';
      protected $fillable = [
        'Code',
        'Date',
        'Product_Numbers',
        'Total_Qty',
        'Total_Price',
        'Total_Price_Errors',
        'Totaal',
        'Note',
        'Serial_Num',
        'Draw',
        'Company',
        'Device_Type',
        'Device_Case',
        'Coin',
        'Cost_Center',
        'Account',
        'User',
        'Store',
        'Safe',
        'Recipt',          
    ];

    
            public function Company()
    {
        return $this->belongsTo(ManufactureCompany::class,'Company');
    }
    
               public function Device_Type()
    {
        return $this->belongsTo(DevicesTypesy::class,'Device_Type');
    }
    
               public function Device_Case()
    {
        return $this->belongsTo(DesviceCases::class,'Device_Case');
    }
    
               public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    
               public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
    
               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
    
               public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
               public function Recipt()
    {
        return $this->belongsTo(ReciptMaintainceErrors::class,'Recipt');
    }

        public function ProductMaintaincBill()
    {
        return $this->hasOne(ProductMaintaincBill::class);
    }
    
             public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
      
          public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
    
}

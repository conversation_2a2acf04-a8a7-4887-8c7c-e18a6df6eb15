<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkDepartments extends Model
{
    use HasFactory;
              protected $table = 'work_departments';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Parent',
        'Budget',
   
    ];
    
                              public function Employess()
    {
        return $this->hasOne(Employess::class);
    }
    
                public function Parent()
    {
        return $this->belongsTo(WorkDepartments::class,'Parent');
    }
    
}

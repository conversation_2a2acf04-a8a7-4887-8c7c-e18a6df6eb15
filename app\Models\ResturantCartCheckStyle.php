<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantCartCheckStyle extends Model
{
    use HasFactory;
         protected $table = 'resturant_cart_check_styles';
      protected $fillable = [
        'Cart_BG_Type',
        'Cart_BG_Image',
        'Cart_BG_Color',
        'Cart_Title_Color',
        'Cart_Txt_Color',
        'Cart_Top_Image',
        'Cart_Box_BG',
        'Cart_Box_Border',
        'Cart_Table_Color',
        'Cart_Table_BG',
        'Cart_Btn_BG',
        'Cart_Btn_Color',
          
        'Collapse_Border',  
        'Collapse_BG',  
        'Collapse_Color',  
          
        'Checkout_Image',
        'Checkout_Title_Color',
        'Checkout_BG_Type',
        'Checkout_BG_Image',
        'Checkout_BG_Color',
        'Checkout_Box_BG',
        'Checkout_Box_Border',
        'Checkout_Input_BG',
        'Checkout_Input_Color',
        'Checkout_Btn_Color',
        'Checkout_Btn_BG',
        'Checkout_Txt_Color', 
        'Checkout_Txt_Hover_Color', 
        'Checkout_Price_Color', 
          
        'MyAccount_BG_Type', 
        'MyAccount_BG_Image', 
        'MyAccount_BG_Color', 
        'MyAccount_Box_BG', 
        'MyAccount_Box_Border', 
        'MyAccount_Table_BG', 
        'MyAccount_Table_Color', 
        'MyAccount_Table_Head_BG', 
        'MyAccount_Table_Head_Color', 
        'MyAccount_Txt_Color', 
        'MyAccount_Btn_BG', 
        'MyAccount_Btn_Color', 
        'MyAccount_Input_BG', 
        'MyAccount_Input_Color', 
     
       
    ];
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Products extends Model
{
    use HasFactory;

        protected $table = 'products';
      protected $fillable = [

                    'P_Type',
                    'P_Ar_Name',
                    'P_En_Name',
                    'Brand',
                    'Group',
                    'Image',
                    'Image2',
                    'Minimum',
                    'Maximum',
                    'Length',
                    'Width',
                    'Height',
                    'Weight',
                    'Saller_Point',
                    'Customer_Point',
                    'Tax',
                    'Validity',
                    'Days_Notify',
                    'Ar_Desc',
                    'En_Desc',
                    'Ar_Spec',
                    'En_Spec',
                    'Store_Show',
                    'Store_Type',
                    'Sub_Cost',
                    'subscribe_type',
                    'Status',
                    'Cas_No',
                    'HSN',
                    'Uni_Code',
                    'Offer',
                    'OfferPrice',
                    'rate',
                    'Code_Type',
                    'World_Code',
                    'Origin_Number',
                    'Origin_Country',
                    'SearchCode1',
                    'SearchCode2',
                    'Space',
                    'Storage',
                    'Processor',
                    'Camera',
                    'Screen',
                    'OS',
                    'Battery',
                    'Warranty',
                    'Color',
                    'Category',
                    'Model',
                    'Guess_Price',
                    'Offer_Start_Date',
                    'Offer_End_Date',
                    'Arrange',
                    'Added',
                    'Arabic_Brief_Desc',
                    'English_Brief_Desc',
                    'Show_Other_Store',
                    'Calories',
                    'Thickness',
                    'Maximum_Sales_Qty',
          


    ];

        public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }

           public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }


        public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }

    public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }

    public function SubImages()
    {
        return $this->hasOne(SubImages::class);
    }

      public function ProductUnits()
    {
        return $this->hasOne(ProductUnits::class);
    }


          public function AssemblyProducts()
    {
        return $this->hasOne(AssemblyProducts::class);
    }


         public function ProductsStartPeriods()
    {
        return $this->hasOne(ProductsStartPeriods::class);
    }

         public function ProductsStores()
    {
        return $this->hasOne(ProductsStores::class);
    }

            public function ProductsQty()
    {
        return $this->hasOne(ProductsQty::class);
    }

           public function ProductsVira()
    {
        return $this->hasOne(ProductsVira::class);
    }

         public function subscribe_type()
    {
        return $this->belongsTo(SubscribeTypes::class,'subscribe_type');
    }

           public function ProductInventory()
    {
        return $this->hasOne(ProductInventory::class);
    }

                public function ProductSettlement()
    {
        return $this->hasOne(ProductSettlement::class);
    }

                   public function ProductsStoresTransfers()
    {
        return $this->hasOne(ProductsStoresTransfers::class);
    }

         public function BarcodeProducts()
    {
        return $this->hasOne(BarcodeProducts::class);
    }

              public function ProductsPurchasesOrder()
    {
        return $this->hasOne(ProductsPurchasesOrder::class);
    }

                  public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }

                          public function RecivedPurchProducts()
    {
        return $this->hasOne(RecivedPurchProducts::class);
    }

        public function ReturnPurchProducts()
    {
        return $this->hasOne(ReturnPurchProducts::class);
    }
              public function ProductsQuote()
    {
        return $this->hasOne(ProductsQuote::class);
    }

           public function ProductSales()
    {
        return $this->hasOne(ProductSales::class);
    }

         public function ProductSalesOrder()
    {
        return $this->hasOne(ProductSalesOrder::class);
    }

           public function RecivedSalesProducts()
    {
        return $this->hasOne(RecivedSalesProducts::class);
    }

          public function ReturnSalesProducts()
    {
        return $this->hasOne(ReturnSalesProducts::class);
    }


}

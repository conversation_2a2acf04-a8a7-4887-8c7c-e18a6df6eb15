<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMostSalesProductsColumnSechdulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('most_sales_products_column_sechdules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Product_Code');
            $table->string('Product_Name');
            $table->string('Qty');
            $table->string('Price');
            $table->string('Discount')->nullable();
            $table->string('Tax')->nullable();
            $table->string('Total')->nullable();
            $table->string('Store');
            $table->string('Date')->nullable();
            $table->string('Unit');
            $table->string('Safe')->nullable();
            $table->string('Branch');
            $table->string('Group');
            $table->string('Brand')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('most_sales_products_column_sechdules');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResturantStyle extends Model
{
    use HasFactory;
      protected $table = 'resturant_styles';
      protected $fillable = [
        'Font',
        'Home',
        'Menu',
          
        'PDF',
        'Cart',
        'Blogs',
        'Gallery',
        'Terms',
        'Privacy',
        'Reviews',
        'Reservations',
          
          
        'Body_BG_Type',
        'Body_BG_Image',
        'Body_BG_Color',
        'Body_Title_Color',
          
        'Pagination_BG_Color',
        'Pagination_Txt_Color',
        'Pagination_Active_BG_Color',
        'Pagination_Active_Txt_Color',
          
        'Modal_BG_Color',
        'Modal_Txt_Color',
        'Modal_Button_BG_Color',
        'Modal_Button_Txt_Color',
          
        'Preloader_Circle_1_Color',
        'Preloader_Circle_2_Color',
        'Preloader_Circle_3_Color', 
          
        'Form_Box_BG', 
        'Form_Box_Border_Color', 
        'Form_Txt_Color', 
        'Form_Txt_Hover_Color', 
        'Form_Input_BG', 
        'Form_Input_Txt_Color', 
        'Form_Btn_BG', 
        'Form_Btn_Txt_Color', 
          
  
       
    ];
}

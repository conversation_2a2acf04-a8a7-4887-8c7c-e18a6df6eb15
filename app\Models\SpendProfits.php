<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpendProfits extends Model
{
    use HasFactory;
       protected $table = 'spend_profits';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Amount',
        'Remaining_Profit',
        'Partner',
        'Safe',
        'Coin',
        'Cost_Center',
        'User',
    ];

    public function Partner()
    {
        return $this->belongsTo(Partners::class,'Partner');
    }
    

    public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }

    
    public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }


    public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }


     public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
  
    
}

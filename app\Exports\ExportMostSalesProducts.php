<?php

namespace App\Exports;

use App\Models\ProductsQty;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\ProductSales;
class ExportMostSalesProducts implements FromCollection ,WithHeadings 
{
 
    
     private $store=[] ;

    public function __construct($store=0) 
    {
        $this->store = $store;

       
    }

    
  
    public function collection()
    {
        ## 3. Conditional export and customize result

        $storex=$this->store;

         $storee=$storex['store'];
         $from=$storex['from'];
         $to=$storex['to'];
         $safe=$storex['safe'];
         $group=$storex['group'];
         $brand=$storex['brand'];
         $branch=$storex['branch'];
         $product_Name=$storex['product_Name'];
         $product_Code=$storex['product_Code'];
        
    
   

     $items=ProductSales::whereBetween('Date',[$from,$to])
          ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store',$storee);
    })
       

                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('Safe', $safe);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })  
            ->distinct(['Product'])->get();   
              
        

        $result = array();
        foreach($items as $record){
            
            $Sales_Qty=ProductSales::where('Product',$record->Product)
                                                ->whereBetween('Date',[$from,$to])
        ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store',$storee);
    })
       

                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('Safe', $safe);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })  
                                                ->get()->sum('Qty');  
                                                
                                                
              $Sales_Count=ProductSales::where('Product',$record->Product)
                                                ->whereBetween('Date',[$from,$to])
        ->when(!empty($storee), function ($query) use ($storee) {
        return $query->whereIn('Store',$storee);
    })
       

                  ->when(!empty($safe), function ($query) use ($safe) {
        return $query->whereIn('Safe', $safe);
    })   
       
       
                   ->when(!empty($group), function ($query) use ($group) {
        return $query->whereIn('Group', $group);
    })  
       
       
                   ->when(!empty($brand), function ($query) use ($brand) {
        return $query->whereIn('Brand', $brand);
    })  
       
 
       
                        ->when(!empty($branch), function ($query) use ($branch) {
        return $query->where('Branch', $branch);
    })  
       
       
                        ->when(!empty($product_Name), function ($query) use ($product_Name) {
        return $query->where('P_Ar_Name', $product_Name);
    })  
       
       
                        ->when(!empty($product_Code), function ($query) use ($product_Code) {
        return $query->where('P_Code', $product_Code);
    })  
         ->count();      

               if(app()->getLocale() == 'ar' ){ 
           $result[] = array(
              'Product_Code'=>$record->Product_Code,
              'Product_Name' => $record->P_Ar_Name .''. ( $record->V_Name) .''. ($record->VV_Name),
              'Sales_Count' => $Sales_Count,
              'Sales_Qty' =>$Sales_Qty 
           );
                   
               }else{
                 $result[] = array(
              'Product_Code'=>$record->Product_Code,
              'Product_Name' => $record->P_En_Name .''. ( $record->V_Name) .''. ($record->VV_Name),
              'Sales_Count' => $Sales_Count,
              'Sales_Qty' =>$Sales_Qty 
           );       
                   
               }
        }

        return collect($result);
    }
    

    public function headings(): array
    {
        return [
          'Product_Code',
          'Product_Name',
          'Sales_Count',
          'Sales_Qty'
        
        ];
    }
    
 

}

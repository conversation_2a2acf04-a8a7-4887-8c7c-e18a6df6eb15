<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerFollowUp extends Model
{
    use HasFactory;
     protected $table = 'customer_follow_ups';
      protected $fillable = [
        'Code',
        'Date',
        'Client',
        'Subject',
        'Rate',
        'Emp',
        'Visit_Cost',
        'Note',
   
    ];
    
    
               public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
             public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }
    
      
 
}

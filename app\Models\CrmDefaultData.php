<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CrmDefaultData extends Model
{
    use HasFactory;
              protected $table = 'crm_default_data';
      protected $fillable = [
        'Price_Level',
        'Governrate',
        'City',
        'Responsible',
        'Activity',
        'Campagin',
        'ClientStatus',
        'Platforms',
        'Client_Delegate',
        'Nationality',
        'ClientGroup',
   
    ];
    
          public function ClientGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'ClientGroup');
    }
              public function Governrate()
    {
        return $this->belongsTo(Governrate::class,'Governrate');
    }
    
    
              public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }
    
               public function City()
    {
        return $this->belongsTo(City::class,'City');
    }
    
               public function Responsible()
    {
        return $this->belongsTo(Employess::class,'Responsible');
    }
    
               public function Activity()
    {
        return $this->belongsTo(Activites::class,'Activity');
    }
    
               public function Campagin()
    {
        return $this->belongsTo(Campaigns::class,'Campagin');
    }
    
               public function ClientStatus()
    {
        return $this->belongsTo(ClientStatus::class,'ClientStatus');
    }
    
                public function Platform()
    {
        return $this->belongsTo(Platforms::class,'Platform');
    }      
                public function Platforms()
    {
        return $this->belongsTo(Platforms::class,'Platforms');
    }  

}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\Purchases;
use App\Models\Sales;
use App\Models\PurchasesDefaultData;
use App\Models\AcccountingManual;
use App\Models\UsersMoves;
use App\Models\CompanyData;
use App\Models\Customers;
use App\Models\ProductSales;
use App\Models\ReturnSales;
use App\Models\ReturnSalesProducts;
use DB ;
use Str ;
use App\Mail\AdminResetPassword;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use SpamProtector;
use Storage;
use DateTime;
use DateTimeZone;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Hash; 
use File; 


class ElectronicBillController extends Controller
{
    function __construct()
{

$this->middleware('permission:ارسال فواتير مشتريات', ['only' => ['Send_Bill_Purchases','PostSendPurchBill']]);
$this->middleware('permission:ارسال فواتير مبيعات', ['only' => ['Send_Bill_Sales','PostSendSalesBill']]);
$this->middleware('permission:فواتير مشتريات مرسله', ['only' => ['Bill_Purchases_Sent']]);
$this->middleware('permission:فواتير مبيعات مرسله', ['only' => ['Bill_Sales_Sent']]);
$this->middleware('permission:ارسال فواتير مرتجعات مبيعات', ['only' => ['ReturnSendSalesBill']]);
$this->middleware('permission:فواتير مرتجعات مرسله', ['only' => ['Bill_ReturnSales_Sent']]);
$this->middleware('permission:ارسال ايصال الكتروني', ['only' => ['Send_Recipt_Sales']]);
$this->middleware('permission:ارسال مرتجع ايصال الكتروني', ['only' => ['ReturnSend_Recipt_Sales']]);
$this->middleware('permission:ايصالات مرسله', ['only' => ['Recipt_Sales_Sent']]);
        
}   
    
    

    //Send_Bill_Sales
       public function Send_Bill_Sales(){

                $items=Sales::orderBy('id','desc')
            ->where('Sent',null)->where('TaxBill',1)->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Send_Bill_Sales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
        public function Send_Bill_ReturnSales(){

                $items=ReturnSales::orderBy('id','desc')
            ->where('Sent',null)->where('TaxBill',1)->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Send_Bill_ReturnSales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }

     public function Send_Bill_SalesFilter(){
         $Client=request('Client');
         $Payment_Method=request('Payment_Method');
         $Code=request('Code');
   
                $items=Sales::whereBetween('Date',[request('From'),request('To')])
                           ->where('Sent',null)
                    ->where('TaxBill',1)
                             ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client', $Client);
    })     
                    
                                  ->when(!empty($Payment_Method), function ($query) use ($Payment_Method) {
        return $query->where('Payment_Method', $Payment_Method);
    })    
                    
                                  ->when(!empty($Code), function ($query) use ($Code) {
        return $query->where('TaxCode', $Code);
    })                
                    
              
                    
                    ->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
    
         return view('admin.ElectonicBill.Send_Bill_Sales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
     public function ReturnSend_Bill_SalesFilter(){
         $Client=request('Client');
         $Payment_Method=request('Payment_Method');
         $Code=request('Code');
   
                $items=ReturnSales::whereBetween('Date',[request('From'),request('To')])
                           ->where('Sent',null)
                    ->where('TaxBill',1)
                             ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client', $Client);
    })     
                    
                                  ->when(!empty($Payment_Method), function ($query) use ($Payment_Method) {
        return $query->where('Payment_Method', $Payment_Method);
    })    
                    
                                  ->when(!empty($Code), function ($query) use ($Code) {
        return $query->where('TaxCode', $Code);
    })                
                    
              
                    
                    ->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
    
         return view('admin.ElectonicBill.Send_Bill_ReturnSales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    

    //Login Api with Submit Send Sales 
     public function SendSalesBill($id){
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

         if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
         
         $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'&scope=InvoicingAPI',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));

                  
$response = curl_exec($curl);
curl_close($curl);
$i=json_decode($response,true); 
 
          $this->PAY($i['access_token'],$id);
         return redirect('Send_Bill_Sales');

    }
        private function PAY($x,$id){
            ini_set('precision', 6);
          ini_set('serialize_precision', 6);
            $Company=CompanyData::orderBy('id','desc')->first(); 
            $sale=Sales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
        
            $out=[];
            $Prods=ProductSales::where('Sales',$sale->id)->get();
             $PRODUCTS=array();

    if($sale->DiscountTax == 0){    
            
          foreach($Prods as $pro){
              
            $Q=$pro->Qty;  
            $SalesTotal=($pro->Price * $pro->Qty);  
            $discountAmount=$pro->Discount * $pro->Qty;    
            $netTotal=$SalesTotal -  $discountAmount;  
            $amountEGP=$pro->Price;  
            $taxableItems=($pro->Tax()->first()->Rate / 100) *  $netTotal;
            $taxableItemsAmount=number_format($taxableItems, 2, '.', '');
            $total=$netTotal + $taxableItemsAmount;
              
              if($sale->DiscountTax == 0){
                 $discTax=0;  
              }else{
                $discTax=$pro->NetTotal *  (1/100) ;   
              }
             
       array_push($PRODUCTS,[
                
                    "description"=>$pro->P_Ar_Name,
                    "itemType"=>$pro->Product()->first()->Code_Type,
                    "itemCode"=>$pro->Product()->first()->World_Code,
                    "unitType"=>$pro->Unit()->first()->Code,
                    "quantity"=>(float)$Q,
                    "internalCode"=>$pro->Product_Code,
                    "salesTotal"=>(float)$pro->SalesTotal,  // price * qty  el egmaly kabl el dreba lw masery lw agnbya htdrn amountEgp f el qty 
                    "netTotal"=>(float)$pro->NetTotal, // sales total - amount discount
                    "valueDifference"=>0.00,  // da bytktb lw el dreba btt7sb 3ala haga tanya gher egmaly s3r
           "unitValue"=>[
                        "currencySold"=>$pro->CoinCode,
                        "amountSold"=>(float)$pro->CoinPrice,
                        "currencyExchangeRate"=>(float)$pro->CoinRate,
                            "amountEGP"=>(float)$pro->AmountEGP// lw 3omla agnbya tdrb sold f rate  lw masry 7ot el s3r bs
                    ],
                    "discount"=> [
                        "rate"=> 0.00,
                        "amount"=>(float)$pro->DiscountAmount 
                    ],
                    "taxableItems"=> [
                        [
                            "taxType"=>$pro->TaxType,
                            "amount"=>(float)$pro->TaxAmount,  // rate tax * net total
                            "subType"=>$pro->TaxSubType,  // V001 da m3nah m3fy mn dreba lsabb w brdu V002  bs v009 da sal3a el 3dya 
                            "rate"=> (float)$pro->TaxRate
                        ]
                       
                    ],
                    "totalTaxableFees"=>0.00,  // magmo3 kool amount daryeb 
     "itemsDiscount"=> 0.00,   // da lw ana b3ml el dreba el awel w b3den khasm lakn lw bnst3ml khasm el awel w b3den dareba yb2a discount elly fook kafya y3ni y3tbr lw 3yaz a3ml khsm mysrsh 3 dreba
        "total"=>(float)$pro->TotalBill - $discTax
           //net total + Tax amount - itmesDiscount
               ]);    
          }
    }else{
        
           foreach($Prods as $pro){
              
            $Q=$pro->Qty;  
            $SalesTotal=($pro->Price * $pro->Qty);  
            $discountAmount=$pro->Discount * $pro->Qty;    
            $netTotal=$SalesTotal -  $discountAmount;  
            $amountEGP=$pro->Price;  
            $taxableItems=($pro->Tax()->first()->Rate / 100) *  $netTotal;
            $taxableItemsAmount=number_format($taxableItems, 2, '.', '');
            $total=$netTotal + $taxableItemsAmount;
              
              if($sale->DiscountTax == 0){
                 $discTax=0;  
              }else{
                $discTax=$pro->NetTotal *  (1/100) ;   
              }
             
       array_push($PRODUCTS,[
                
                    "description"=>$pro->P_Ar_Name,
                    "itemType"=>$pro->Product()->first()->Code_Type,
                    "itemCode"=>$pro->Product()->first()->World_Code,
                    "unitType"=>$pro->Unit()->first()->Code,
                    "quantity"=>(float)$Q,
                    "internalCode"=>$pro->Product_Code,
                    "salesTotal"=>(float)$pro->SalesTotal,  // price * qty  el egmaly kabl el dreba lw masery lw agnbya htdrn amountEgp f el qty 
                    "netTotal"=>(float)$pro->NetTotal, // sales total - amount discount
                    "valueDifference"=>0.00,  // da bytktb lw el dreba btt7sb 3ala haga tanya gher egmaly s3r
           "unitValue"=>[
                        "currencySold"=>$pro->CoinCode,
                        "amountSold"=>(float)$pro->CoinPrice,
                        "currencyExchangeRate"=>(float)$pro->CoinRate,
                            "amountEGP"=>(float)$pro->AmountEGP// lw 3omla agnbya tdrb sold f rate  lw masry 7ot el s3r bs
                    ],
                    "discount"=> [
                        "rate"=> 0.00,
                        "amount"=>(float)$pro->DiscountAmount 
                    ],
                    "taxableItems"=> [
                        [
                            "taxType"=>$pro->TaxType,
                            "amount"=>(float)$pro->TaxAmount,  // rate tax * net total
                            "subType"=>$pro->TaxSubType,  // V001 da m3nah m3fy mn dreba lsabb w brdu V002  bs v009 da sal3a el 3dya 
                            "rate"=> (float)$pro->TaxRate
                        ],
                        [
                            "taxType"=>"T4",
                            "amount"=>(float)$discTax,  // rate tax * net total
                            "subType"=>"W002",  // V001 da m3nah m3fy mn dreba lsabb w brdu V002  bs v009 da sal3a el 3dya 
                            "rate"=> 1.00
                        ]
                    ],
                    "totalTaxableFees"=>0.00,  // magmo3 kool amount daryeb 
     "itemsDiscount"=> 0.00,   // da lw ana b3ml el dreba el awel w b3den khasm lakn lw bnst3ml khasm el awel w b3den dareba yb2a discount elly fook kafya y3ni y3tbr lw 3yaz a3ml khsm mysrsh 3 dreba
        "total"=>(float)$pro->TotalBill - $discTax
           //net total + Tax amount - itmesDiscount
               ]);    
          }
    }

            
            $homepage = file_get_contents("".$Company->Path."/Invoice".$id.".json");
    $values = json_decode($homepage);
    $valuesXy =  (array) $values;
    $valuesXyX =  (array) $valuesXy;
    $valuesXyXY =  (array) $valuesXyX['documents'][0];
    $valuesXyXYX =  (array) $valuesXyXY['signatures'][0];
          // dd($valuesXyXYX['value']);

            
//totalSalesAmount => egmaly el ftora mngher dareba wla khsm 
//totalDiscountAmount => egmaly el khasm elly hwa qty f sa3r
//netAmount  => elly hwa zy netTotal  bs 3ala kol asnaf
//taxTotals => mgmo3 el dreba 
//totalAmount => hngm3 total bta3 kol sanf - extraDiscount
//extraDiscountAmount  => lw f khsm zyada 3 ftora
//totalItemsDiscountAmount => da elly hwa itemsDiscount mgmo3hm f kol lines elly f ghalb hykwn bsfr 

            $xx=json_encode($PRODUCTS);

$curl = curl_init();
if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/documentsubmissions';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/documentsubmissions';    
}
            
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
       
          
                if($Company->Version_Type == 1.0){
                    $SIGN=$valuesXyXYX['value'];
                }else{
                  $SIGN='MIIGywYJKoZIhvcNAQcCoIIGvDCCBrgCAQMxDTALBglghkgBZQMEAgEwCwYJKoZIhvcNAQcFoIID/zCCA/swggLjoAMCAQICEEFkOqRVlVar0F0n3FZOLiIwDQYJKoZIhvcNAQELBQAwSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzIwHhcNMjAwMzMxMDAwMDAwWhcNMjEwMzMwMjM1OTU5WjBgMRUwEwYDVQQKFAxFZ3lwdCBUcnVzdCAxGDAWBgNVBGEUD1ZBVEVHLTExMzMxNzcxMzELMAkGA1UEBhMCRUcxIDAeBgNVBAMMF1Rlc3QgU2VhbGluZyBEZW1vIHVzZXIyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApmVGVJtpImeq\\u002BtIJiVWSkIEEOTIcnG1XNYQOYtf5\\u002BDg9eF5H5x1wkgR2G7dvWVXrTsdNv2Q\\u002Bgvml9SdfWxlYxaljg2AuBrsHFjYVEAQFI37EW2K7tbMT7bfxwT1M5tbjxnkTTK12cgwxPr2LBNhHpfXp8SNyWCxpk6eyJb87DveVwCLbAGGXO9mhDj62glVTrCFit7mHC6bZ6MOMAp013B8No9c8xnrKQiOb4Tm2GxBYHFwEcfYUGZNltGZNdVUtu6ty\\u002BNTrSRRC/dILeGHgz6/2pgQPk5OFYRTRHRNVNo\\u002BjG\\u002BnurUYkSWxA4I9CmsVt2FdeBeuvRFs/U1I\\u002BieKg1wIDAQABo4HHMIHEMAkGA1UdEwQCMAAwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL21wa2ljcmwuZWd5cHR0cnVzdC5jb20vRWd5cHRUcnVzdENvcnBvcmF0ZUNBRzIvTGF0ZXN0Q1JMLmNybDAdBgNVHQ4EFgQUqzFDImtytsUbghbmtnl2/k4d5jEwEQYJYIZIAYb4QgEBBAQDAgeAMB8GA1UdIwQYMBaAFCInP8ziUIPmu86XJUWXspKN3LsFMA4GA1UdDwEB/wQEAwIGwDANBgkqhkiG9w0BAQsFAAOCAQEAxE3KpyYlPy/e3\\u002B6jfz5RqlLhRLppWpRlKYUvH1uIhCNRuWaYYRchw1xe3jn7bLKbNrUmey\\u002BMRwp1hZptkxFMYKTIEnNjOKCrLmVIuPFcfLXAQFq5vgLDSbnUhG/r5D\\u002B50ndPucyUPhX3gw8gFlA1R\\u002BtdNEoeKqYSo9v3p5qNANq12OuZbkhPg6sAD4dojWoNdlkc8J2ML0eq4a5AQvb4yZVb\\u002BezqJyqKj83RekRZi0kMxoIm8l82CN8I/Bmp6VVNJRhQKhSeb7ShpdkZcMwcfKdDw6LW02/XcmzVl8NBBbLjKSJ/jxdL1RxPPza7RbGqSx9pfyav5\\u002BAxO9sXnXXc5jGCApIwggKOAgEBMF0wSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzICEEFkOqRVlVar0F0n3FZOLiIwCwYJYIZIAWUDBAIBoIIBCjAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcFMBwGCSqGSIb3DQEJBTEPFw0yMTAyMDEyMzUwMjFaMC8GCSqGSIb3DQEJBDEiBCD5bGXJu9uJZIPMGXK98UrHzJM/V2U/WAO6BErxpX5wdTCBngYLKoZIhvcNAQkQAi8xgY4wgYswgYgwgYUEIAJA8uO/ek3l9i3ZOgRtPhGWwwFYljbeJ7yAgEnyYNCWMGEwTaBLMEkxCzAJBgNVBAYTAkVHMRQwEgYDVQQKEwtFZ3lwdCBUcnVzdDEkMCIGA1UEAxMbRWd5cHQgVHJ1c3QgQ29ycG9yYXRlIENBIEcyAhBBZDqkVZVWq9BdJ9xWTi4iMAsGCSqGSIb3DQEBAQSCAQB13E1WX\\u002BzbWppfJi3DBK9MMSB1TXuxcNkGXQ19OcRUUAaAe2K\\u002BisobYrUCZbi3ygc2AWOMyafboxjjomzrnvXKrFgspT4wAFPYaAGFzKWq\\u002BW/nqMhIqJVIpS/NM7Al4HvuBA5iGuZEQFusElB0yIxOIiYDI4v8Ilkff4/duj/V2CNaN5cqXLOpL5RP6Y5i\\u002BVsPGb89t/L0dSIldGN0JqaqarqYo5/RwsUFJJq01DFpPGNbOIX3gSCDmycfhJPS9csnne9Zt\\u002BabNpja5ZR6KA8JMe4DHes7FDZqHBNHdC\\u002BRDXT4crqmnyiJjizULu6MqDc0Fv3vrMMWDLRlwDecgq7i';   
                }
                    
                if($sale->DiscountTax == 0){
                
            curl_setopt_array($curl, array(
  CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS =>'{
    "documents": [
        {
            "issuer": {
                "address": {
                    "branchID": "'.$BRANCHCODE.'",
                    "country": "EG",
                    "governate": "'.$Company->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Company->City()->first()->Arabic_Name.'",
                    "street": "'.$Company->Street.'",
                    "buildingNumber": "'.$Company->Buliding_Num.'",
                    "postalCode": "'.$Company->Postal_Code.'",
                    "floor": "'.$Company->Floor.'",
                    "room": "'.$Company->Room.'",
                    "landmark": "'.$Company->Landmark.'",
                    "additionalInformation": "'.$Company->Add_Info.'"
                },
                "type": "'.$Company->work_nature.'",
                "id": "'.$Company->Tax_Registration_Number.'",
                "name": "'.$Company->Name.'"
            },
            "receiver": {
                "address": {
                    "country": "'.$Customer->Nationality()->first()->Code.'",
                    "governate": "'.$Customer->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Customer->City()->first()->Arabic_Name.'",
                    "street": "'.$Customer->Street.'",
                    "buildingNumber": "'.$Customer->Buliding_Num.'",
                    "postalCode": "'.$Customer->Postal_Code.'",
                    "floor": "'.$Customer->Floor.'",
                    "room": "'.$Customer->Room.'",
                    "landmark": "'.$Customer->Landmark.'",
                    "additionalInformation": "'.$Customer->Add_Info.'"
                },
                "type": "'.$Customer->work_nature.'",
                "id": "'.$Customer->Tax_Registration_Number.'",
                "name": "'.$Customer->Name.'"
            },
            "documentType": "I",
            "documentTypeVersion": "'.$Company->Version_Type.'",
            "dateTimeIssued": "'.$sale->Date.'T02:04:45Z",  
            "taxpayerActivityCode": "'.$Company->Tax_activity_code.'",
            "internalID": "IID'.$sale->TaxCode.'",
            "invoiceLines": '.$xx.',
             "totalSalesAmount": '.$sale->Total_Price.',  
            "totalDiscountAmount": '.$sale->Total_Discount.',
            "netAmount": '.($sale->Total_Price - $sale->Total_Discount).',
            "taxTotals": [
                {
                    "taxType": "T1",
                    "amount": '.$sale->Total_Taxes.'
                }
            ],
      
            "extraDiscountAmount": 0.00,
            "totalItemsDiscountAmount": 0.00,
            "totalAmount": '.($sale->Total_Price + ($sale->Total_Taxes - $sale->DiscountTax) - $sale->Total_Discount).',
            "signatures": [
                {
                    "signatureType": "I",
                    "value": "'.$SIGN.'"
                }
            ]
        }
    ]
}',
  CURLOPT_HTTPHEADER => array(
      'Authorization: Bearer  '.$x.' ',
    'Content-Type: application/json'
    

    
  ),
));      
                
                
                
                
                }else{
            curl_setopt_array($curl, array(
  CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS =>'{
    "documents": [
        {
            "issuer": {
                "address": {
                    "branchID": "'.$BRANCHCODE.'",
                    "country": "EG",
                    "governate": "'.$Company->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Company->City()->first()->Arabic_Name.'",
                    "street": "'.$Company->Street.'",
                    "buildingNumber": "'.$Company->Buliding_Num.'",
                    "postalCode": "'.$Company->Postal_Code.'",
                    "floor": "'.$Company->Floor.'",
                    "room": "'.$Company->Room.'",
                    "landmark": "'.$Company->Landmark.'",
                    "additionalInformation": "'.$Company->Add_Info.'"
                },
                "type": "'.$Company->work_nature.'",
                "id": "'.$Company->Tax_Registration_Number.'",
                "name": "'.$Company->Name.'"
            },
            "receiver": {
                "address": {
                    "country": "'.$Customer->Nationality()->first()->Code.'",
                    "governate": "'.$Customer->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Customer->City()->first()->Arabic_Name.'",
                    "street": "'.$Customer->Street.'",
                    "buildingNumber": "'.$Customer->Buliding_Num.'",
                    "postalCode": "'.$Customer->Postal_Code.'",
                    "floor": "'.$Customer->Floor.'",
                    "room": "'.$Customer->Room.'",
                    "landmark": "'.$Customer->Landmark.'",
                    "additionalInformation": "'.$Customer->Add_Info.'"
                },
                "type": "'.$Customer->work_nature.'",
                "id": "'.$Customer->Tax_Registration_Number.'",
                "name": "'.$Customer->Name.'"
            },
            "documentType": "I",
            "documentTypeVersion": "'.$Company->Version_Type.'",
            "dateTimeIssued": "'.$sale->Date.'T02:04:45Z",  
            "taxpayerActivityCode": "'.$Company->Tax_activity_code.'",
            "internalID": "IID'.$sale->TaxCode.'",
            "invoiceLines": '.$xx.',
             "totalSalesAmount": '.$sale->Total_Price.',  
            "totalDiscountAmount": '.$sale->Total_Discount.',
            "netAmount": '.($sale->Total_Price - $sale->Total_Discount).',
            "taxTotals": [
                {
                    "taxType": "T1",
                    "amount": '.$sale->Total_Taxes.'
                },
                {
                    "taxType": "T4",
                    "amount": '.$sale->DiscountTax.'
                }
            ],
      
            "extraDiscountAmount": 0.00,
            "totalItemsDiscountAmount": 0.00,
            "totalAmount": '.($sale->Total_Price + ($sale->Total_Taxes - $sale->DiscountTax) - $sale->Total_Discount).',
            "signatures": [
                {
                    "signatureType": "I",
                    "value": "'.$SIGN.'"
                }
            ]
        }
    ]
}',
  CURLOPT_HTTPHEADER => array(
      'Authorization: Bearer  '.$x.' ',
    'Content-Type: application/json'
    

    
  ),
));
                }
            
            
            
$response = curl_exec($curl);

curl_close($curl);

   $i=json_decode($response,true); 


            if(!empty($i['submissionId'])){

              Sales::where('id',$id)->update([
                  'Sent'=>1,
                  'uuid'=>$i['acceptedDocuments'][0]['uuid'],
                  'longId'=>$i['acceptedDocuments'][0]['longId'],
                  'hashKey'=>$i['acceptedDocuments'][0]['hashKey'],
                  'submissionId'=>$i['submissionId'],
              ]);  
               session()->flash('success','Sent Successfully');     
         return  $x=session()->flash('success','Sent Successfully') ;
            }else{
              
               if(!empty($i['error'])){
                   $msg=$i['error'];
               }elseif(!empty($i['rejectedDocuments'])){
                   $msg=$i['rejectedDocuments'][0]['error']['details'][0]['message']; 
               }else{
                   $msg='Rejected';
               } 
              session()->flash('error',$msg);   
            return  $x=session()->flash('error',$msg) ;
                 
            }    
                   
      
            
}    
    
        //Bill_Purchases_Sent
         public function Bill_Purchases_Sent(){
         
        $items=Purchases::orderBy('id','desc')->where('Sent',1)->paginate(100);

            $Def=PurchasesDefaultData::orderBy('id','desc')->first();
     
     if($Def->V_and_C == 0){
                $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->get();
     }elseif($Def->V_and_C == 1){
         
                     $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();
         
         
     }
          
          
         
         return view('admin.ElectonicBill.Bill_Purchases_Sent',[
             'items'=>$items,
             'Vendors'=>$Vendors,
         ]);
    }
    
    //Bill_Sales_Sent
       public function Bill_Sales_Sent(){

                $items=Sales::orderBy('id','desc')
            ->where('Sent',1)->where('TaxBill',1)->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Bill_Sales_Sent',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }

            public function Bill_Sales_SentFilter(){

                
                  $Client=request('Client');
         $Payment_Method=request('Payment_Method');
         $Code=request('Code');
         $UUID=request('UUID');
         $Submission_UUID=request('Submission_UUID');
    
                $items=Sales::whereBetween('Date',[request('From'),request('To')])
            ->where('Sent',1)
                    ->where('TaxBill',1)
                    
             ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client', $Client);
    })     
                    
                                  ->when(!empty($Payment_Method), function ($query) use ($Payment_Method) {
        return $query->where('Payment_Method', $Payment_Method);
    })    
                    
                                  ->when(!empty($Code), function ($query) use ($Code) {
        return $query->where('TaxCode', $Code);
    })    
                    
                    
                                        ->when(!empty($UUID), function ($query) use ($UUID) {
        return $query->where('uuid', $UUID);
    })      
                    
                    
                                    ->when(!empty($Submission_UUID), function ($query) use ($Submission_UUID) {
        return $query->where('submissionId', $Submission_UUID);
    })                      
                    
                    ->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Bill_Sales_Sent',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
     public function Bill_Sales_Sent_Web(){

         return view('admin.ElectonicBill.Bill_Sales_Sent_Web');
    }
    
      public function FilterBill_Sales_Sent_Web(Request $request){
          
  $page = $request->get('Page');      
  $type = $request->get('type');      
  $status = $request->get('status');      
  $uuid = $request->get('uuid');      
 $Company=CompanyData::orderBy('id','desc')->first();
          
          
          if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
          
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));


$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 

          $this->FilterSales($i['access_token'],$page,$type,$status,$uuid);

        

    }

    private function FilterSales($x,$page,$type,$status,$uuid){
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

        
        
        
                  if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1.0/documents/recent?pageNo='.$page.'&pageSize=10';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1.0/documents/recent?pageNo='.$page.'&pageSize=10';    
}
          
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
  CURLOPT_HTTPHEADER => array(
 'Authorization: Bearer  '.$x.' '
  ),
));

$response = curl_exec($curl);
curl_close($curl);  
$xs=json_decode($response,true); 

          $output = '';
 
    for($i=0; $i < count($xs['result']); $i++){
            if($xs['result'][$i]['status'] == 'Valid'){
              $v='';  
            }else{
              $v='none';  
            }
            
              if($xs['result'][$i]['receiverId'] == $Company->Tax_Registration_Number){
              $vv='';  
              $v='none';  
            }else{
              $vv='none';  
          
            }
        if($type == 1){
        
        if($status != '' and $uuid != ''){
           if($uuid == $xs['result'][$i]['uuid']){ 
            if($status == $xs['result'][$i]['status']){
           
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
  <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           
           }  
           }  
            
        }elseif($status != '' and $uuid == ''){
           if($status == $xs['result'][$i]['status']){ 
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
      <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           }  
        }elseif($status == '' and $uuid != ''){
                      if($uuid == $xs['result'][$i]['uuid']){ 
                      
                      
                        $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
        <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
                      }  
            
        }elseif($status == '' and $uuid == ''){
            
     
          $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
          <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
         }
   
        }elseif($type == 2){
             if($Company->Tax_Registration_Number == $xs['result'][$i]['issuerId']){  
             
               if($status != '' and $uuid != ''){
           if($uuid == $xs['result'][$i]['uuid']){ 
            if($status == $xs['result'][$i]['status']){
           
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
      <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           
           }  
           }  
            
        }elseif($status != '' and $uuid == ''){
           if($status == $xs['result'][$i]['status']){ 
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
       <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           }  
        }elseif($status == '' and $uuid != ''){
                      if($uuid == $xs['result'][$i]['uuid']){ 
                      
                      
                        $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
    <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
                      }  
            
        }elseif($status == '' and $uuid == ''){
            
     
          $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
      <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
         }
             
             }
        }elseif($type == 3){
             if($Company->Tax_Registration_Number == $xs['result'][$i]['receiverId']){  
             
       if($status != '' and $uuid != ''){
           if($uuid == $xs['result'][$i]['uuid']){ 
            if($status == $xs['result'][$i]['status']){
           
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
    <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           
           }  
           }  
            
        }elseif($status != '' and $uuid == ''){
           if($status == $xs['result'][$i]['status']){ 
             $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
      <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
           
           }  
        }elseif($status == '' and $uuid != ''){
                      if($uuid == $xs['result'][$i]['uuid']){ 
                      
                      
                        $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
       <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
                      }  
            
        }elseif($status == '' and $uuid == ''){
            
     
          $output .= '<tr>
          
          <td>'.$xs['result'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['result'][$i]['internalId'].'</td>
          <td>'.$xs['result'][$i]['issuerName'].'</td>
          <td>'.$xs['result'][$i]['issuerId'].'</td>
          <td>'.$xs['result'][$i]['receiverName'].'</td>
          <td>'.$xs['result'][$i]['receiverId'].'</td>
          <td>'.$xs['result'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['result'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['result'][$i]['totalSales'].'</td>
          <td>'.$xs['result'][$i]['totalDiscount'].'</td>
          <td>'.$xs['result'][$i]['netAmount'].'</td>
          <td>'.$xs['result'][$i]['total'].'</td>
          <td>'.$xs['result'][$i]['uuid'].'</td>
          <td>'.$xs['result'][$i]['submissionUUID'].'</td>
          <td>'.$xs['result'][$i]['status'].'</td>
    <td>
          <a class="btn btn-success" target="_blank" href="'.$xs['result'][$i]['publicUrl'].'"> Open in Website</a>
          <a class="btn btn-primary" style="display:'.$v.'" target="_blank"  href="'.url("FilterprintPDFSalesElectronic/".$xs['result'][$i]['uuid']."/".$xs['result'][$i]['longId']).'"> Print</a>
          
          <a class="btn btn-danger" style="display:'.$v.'"   href="'.url("CancelSalesElectronic/".$xs['result'][$i]['uuid']).'"> Cancel</a>
          <a class="btn btn-danger" style="display:'.$vv.'"   href="'.url("RejectSalesElectronic/".$xs['result'][$i]['uuid']).'"> Reject</a>
          </td>
          
          </tr>';
         }
             
             }
        }
        
 
    }    
  $data = array(
       'table_data'  => $output,
      );
        
        
      echo json_encode($data);
        

    }

        public function printPDFSalesElectronic($uuid){

          
         return view('admin.ElectonicBill.Print',[
             'uuid'=>$uuid,

    
         ]);
    }
    
      public function FilterprintPDFSalesElectronic($uuid,$longid){
          
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

              
                if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));


$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
    
          $this->PrintSales($i['access_token'],$uuid);
   return redirect('https://invoicing.eta.gov.eg/print/documents/'.$uuid.'/share/'.$longid.'');

    }
    
    private function PrintSales($x,$uuid){
        $Company=CompanyData::orderBy('id','desc')->first();
if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/documents/'.$uuid.'/pdf';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/documents/'.$uuid.'/pdf';    
} 
        $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
  CURLOPT_HTTPHEADER => array(
 'Authorization: Bearer  '.$x.' '
  ),
));

$response = curl_exec($curl);

curl_close($curl);

        
    }

    //ReturnSendSalesBill
        public function ReturnSendSalesBill($id){
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

                 if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
         
            
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));


$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
    
          $this->ReturnPAY($i['access_token'],$id);

         return redirect('Send_Bill_ReturnSales');

    }
    
        private function ReturnPAY($x,$id){
            $Company=CompanyData::orderBy('id','desc')->first(); 
            $sale=ReturnSales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
     
            $out=[];
            $Prods=ReturnSalesProducts::where('Return',$sale->id)->get();
            
             
             $PRODUCTS=array();

          foreach($Prods as $pro){
              
            $Q=$pro->Recived_Qty;  
            $SalesTotal=($pro->Price * $pro->Recived_Qty);  
            $discountAmount=$pro->Discount * $pro->Recived_Qty;    
            $netTotal=$SalesTotal -  $discountAmount;  
            $amountEGP=$pro->Price;  
            $taxableItems=($pro->Tax()->first()->Rate / 100) *  $netTotal;
            $taxableItemsAmount=number_format($taxableItems, 2, '.', '');
            $total=$netTotal + $taxableItemsAmount;

       array_push($PRODUCTS,[
                
                    "description"=>$pro->P_Ar_Name,
                    "itemType"=>$pro->Product()->first()->Code_Type,
                    "itemCode"=>$pro->Product()->first()->World_Code,
                    "unitType"=>$pro->Unit()->first()->Code,
                    "quantity"=>(float)$Q,
                    "internalCode"=>$pro->Product_Code,
                    "salesTotal"=>(float)$pro->SalesTotal,  // price * qty  el egmaly kabl el dreba lw masery lw agnbya htdrn amountEgp f el qty 
                    "netTotal"=>(float)$pro->NetTotal, // sales total - amount discount
                    "valueDifference"=>0.00,  // da bytktb lw el dreba btt7sb 3ala haga tanya gher egmaly s3r
           "unitValue"=>[
                        "currencySold"=>$pro->CoinCode,
                        "amountSold"=>(float)$pro->CoinPrice,
                        "currencyExchangeRate"=>(float)$pro->CoinRate,
                            "amountEGP"=>(float)$pro->AmountEGP// lw 3omla agnbya tdrb sold f rate  lw masry 7ot el s3r bs
                    ],
                    "discount"=> [
                        "rate"=> 0.00,
                        "amount"=>(float)$pro->DiscountAmount 
                    ],
                    "taxableItems"=> [
                        [
                            "taxType"=>$pro->TaxType,
                            "amount"=>(float)$pro->TaxAmount,  // rate tax * net total
                            "subType"=>$pro->TaxSubType,  // V001 da m3nah m3fy mn dreba lsabb w brdu V002  bs v009 da sal3a el 3dya 
                            "rate"=> (float)$pro->TaxRate
                        ]
                    ],
                    "totalTaxableFees"=>0.00,  // magmo3 kool amount daryeb 
     "itemsDiscount"=> 0.00,   // da lw ana b3ml el dreba el awel w b3den khasm lakn lw bnst3ml khasm el awel w b3den dareba yb2a discount elly fook kafya y3ni y3tbr lw 3yaz a3ml khsm mysrsh 3 dreba
        "total"=>(float)$pro->TotalBill  //net total + Tax amount - itmesDiscount
               ]);    
          }
     
//totalSalesAmount => egmaly el ftora mngher dareba wla khsm 
//totalDiscountAmount => egmaly el khasm elly hwa qty f sa3r
//netAmount  => elly hwa zy netTotal  bs 3ala kol asnaf
//taxTotals => mgmo3 el dreba 
//totalAmount => hngm3 total bta3 kol sanf - extraDiscount
//extraDiscountAmount  => lw f khsm zyada 3 ftora
//totalItemsDiscountAmount => da elly hwa itemsDiscount mgmo3hm f kol lines elly f ghalb hykwn bsfr 

            $xx=json_encode($PRODUCTS);
       
$curl = curl_init();
if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/documentsubmissions';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/documentsubmissions';    
}
            
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
       
            
                       $homepage = file_get_contents("".$Company->Path."\ReturnInvoice".$id.".json");
    $values = json_decode($homepage);
    $valuesXy =  (array) $values;
    $valuesXyX =  (array) $valuesXy;
    $valuesXyXY =  (array) $valuesXyX['documents'][0];
    $valuesXyXYX =  (array) $valuesXyXY['signatures'][0];
            
           
                if($Company->Version_Type == 1.0){
                    $SIGN=$valuesXyXYX['value'];
                }else{
                  $SIGN='MIIGywYJKoZIhvcNAQcCoIIGvDCCBrgCAQMxDTALBglghkgBZQMEAgEwCwYJKoZIhvcNAQcFoIID/zCCA/swggLjoAMCAQICEEFkOqRVlVar0F0n3FZOLiIwDQYJKoZIhvcNAQELBQAwSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzIwHhcNMjAwMzMxMDAwMDAwWhcNMjEwMzMwMjM1OTU5WjBgMRUwEwYDVQQKFAxFZ3lwdCBUcnVzdCAxGDAWBgNVBGEUD1ZBVEVHLTExMzMxNzcxMzELMAkGA1UEBhMCRUcxIDAeBgNVBAMMF1Rlc3QgU2VhbGluZyBEZW1vIHVzZXIyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApmVGVJtpImeq\\u002BtIJiVWSkIEEOTIcnG1XNYQOYtf5\\u002BDg9eF5H5x1wkgR2G7dvWVXrTsdNv2Q\\u002Bgvml9SdfWxlYxaljg2AuBrsHFjYVEAQFI37EW2K7tbMT7bfxwT1M5tbjxnkTTK12cgwxPr2LBNhHpfXp8SNyWCxpk6eyJb87DveVwCLbAGGXO9mhDj62glVTrCFit7mHC6bZ6MOMAp013B8No9c8xnrKQiOb4Tm2GxBYHFwEcfYUGZNltGZNdVUtu6ty\\u002BNTrSRRC/dILeGHgz6/2pgQPk5OFYRTRHRNVNo\\u002BjG\\u002BnurUYkSWxA4I9CmsVt2FdeBeuvRFs/U1I\\u002BieKg1wIDAQABo4HHMIHEMAkGA1UdEwQCMAAwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL21wa2ljcmwuZWd5cHR0cnVzdC5jb20vRWd5cHRUcnVzdENvcnBvcmF0ZUNBRzIvTGF0ZXN0Q1JMLmNybDAdBgNVHQ4EFgQUqzFDImtytsUbghbmtnl2/k4d5jEwEQYJYIZIAYb4QgEBBAQDAgeAMB8GA1UdIwQYMBaAFCInP8ziUIPmu86XJUWXspKN3LsFMA4GA1UdDwEB/wQEAwIGwDANBgkqhkiG9w0BAQsFAAOCAQEAxE3KpyYlPy/e3\\u002B6jfz5RqlLhRLppWpRlKYUvH1uIhCNRuWaYYRchw1xe3jn7bLKbNrUmey\\u002BMRwp1hZptkxFMYKTIEnNjOKCrLmVIuPFcfLXAQFq5vgLDSbnUhG/r5D\\u002B50ndPucyUPhX3gw8gFlA1R\\u002BtdNEoeKqYSo9v3p5qNANq12OuZbkhPg6sAD4dojWoNdlkc8J2ML0eq4a5AQvb4yZVb\\u002BezqJyqKj83RekRZi0kMxoIm8l82CN8I/Bmp6VVNJRhQKhSeb7ShpdkZcMwcfKdDw6LW02/XcmzVl8NBBbLjKSJ/jxdL1RxPPza7RbGqSx9pfyav5\\u002BAxO9sXnXXc5jGCApIwggKOAgEBMF0wSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzICEEFkOqRVlVar0F0n3FZOLiIwCwYJYIZIAWUDBAIBoIIBCjAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcFMBwGCSqGSIb3DQEJBTEPFw0yMTAyMDEyMzUwMjFaMC8GCSqGSIb3DQEJBDEiBCD5bGXJu9uJZIPMGXK98UrHzJM/V2U/WAO6BErxpX5wdTCBngYLKoZIhvcNAQkQAi8xgY4wgYswgYgwgYUEIAJA8uO/ek3l9i3ZOgRtPhGWwwFYljbeJ7yAgEnyYNCWMGEwTaBLMEkxCzAJBgNVBAYTAkVHMRQwEgYDVQQKEwtFZ3lwdCBUcnVzdDEkMCIGA1UEAxMbRWd5cHQgVHJ1c3QgQ29ycG9yYXRlIENBIEcyAhBBZDqkVZVWq9BdJ9xWTi4iMAsGCSqGSIb3DQEBAQSCAQB13E1WX\\u002BzbWppfJi3DBK9MMSB1TXuxcNkGXQ19OcRUUAaAe2K\\u002BisobYrUCZbi3ygc2AWOMyafboxjjomzrnvXKrFgspT4wAFPYaAGFzKWq\\u002BW/nqMhIqJVIpS/NM7Al4HvuBA5iGuZEQFusElB0yIxOIiYDI4v8Ilkff4/duj/V2CNaN5cqXLOpL5RP6Y5i\\u002BVsPGb89t/L0dSIldGN0JqaqarqYo5/RwsUFJJq01DFpPGNbOIX3gSCDmycfhJPS9csnne9Zt\\u002BabNpja5ZR6KA8JMe4DHes7FDZqHBNHdC\\u002BRDXT4crqmnyiJjizULu6MqDc0Fv3vrMMWDLRlwDecgq7i';   
                }
                   
              
            
            curl_setopt_array($curl, array(
  CURLOPT_URL =>$version,                
    CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',            
  CURLOPT_POSTFIELDS =>'{
    "documents": [
        {
            "issuer": {
                "address": {
                    "branchID": "'.$BRANCHCODE.'",
                    "country": "EG",
                    "governate": "'.$Company->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Company->City()->first()->Arabic_Name.'",
                    "street": "'.$Company->Street.'",
                    "buildingNumber": "'.$Company->Buliding_Num.'",
                    "postalCode": "'.$Company->Postal_Code.'",
                    "floor": "'.$Company->Floor.'",
                    "room": "'.$Company->Room.'",
                    "landmark": "'.$Company->Landmark.'",
                    "additionalInformation": "'.$Company->Add_Info.'"
                },
                "type": "'.$Company->work_nature.'",
                "id": "'.$Company->Tax_Registration_Number.'",
                "name": "'.$Company->Name.'"
            },
            "receiver": {
                "address": {
                    "country": "'.$Customer->Nationality()->first()->Code.'",
                    "governate": "'.$Customer->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Customer->City()->first()->Arabic_Name.'",
                    "street": "'.$Customer->Street.'",
                    "buildingNumber": "'.$Customer->Buliding_Num.'",
                    "postalCode": "'.$Customer->Postal_Code.'",
                    "floor": "'.$Customer->Floor.'",
                    "room": "'.$Customer->Room.'",
                    "landmark": "'.$Customer->Landmark.'",
                    "additionalInformation": "'.$Customer->Add_Info.'"
                },
                "type": "'.$Customer->work_nature.'",
                "id": "'.$Customer->Tax_Registration_Number.'",
                "name": "'.$Customer->Name.'"
            },
            "documentType": "C",
            "documentTypeVersion": "'.$Company->Version_Type.'",
            "dateTimeIssued": "'.$sale->Date.'T02:04:45Z",  
            "taxpayerActivityCode": "'.$Company->Tax_activity_code.'",
            "internalID": "CID'.$sale->TaxCode.'",
              "references": [
                "'.$sale->Sales()->first()->uuid.'"
            ],
            "invoiceLines": '.$xx.',
             "totalSalesAmount": '.$sale->Total_Return_Value   - $sale->Total_Taxes + $sale->Total_Discount.',  
            "totalDiscountAmount": '.$sale->Total_Discount.',
            "netAmount": '.($sale->Total_Return_Value - $sale->Total_Discount - $sale->Total_Taxes).',
            "taxTotals": [
                {
                    "taxType": "T1",
                    "amount": '.$sale->Total_Taxes.'
                }
            ],
      
            "extraDiscountAmount": 0.00,
            "totalItemsDiscountAmount": 0.00,
            "totalAmount": '.($sale->Total_Return_Value).',
            "signatures": [
                {
                    "signatureType": "I",
                      "value": "'.$SIGN.'"
                }
            ]
        }
    ]
}',
  CURLOPT_HTTPHEADER => array(
      'Authorization: Bearer  '.$x.' ',
    'Content-Type: application/json'
    

    
  ),
));

$response = curl_exec($curl);

curl_close($curl);

 
   $i=json_decode($response,true); 


            if(!empty($i['submissionId'])){

              ReturnSales::where('id',$id)->update([
                  'Sent'=>1,
                  'uuid'=>$i['acceptedDocuments'][0]['uuid'],
                  'longId'=>$i['acceptedDocuments'][0]['longId'],
                  'hashKey'=>$i['acceptedDocuments'][0]['hashKey'],
                  'submissionId'=>$i['submissionId'],
              ]);  
               session()->flash('success','Sent Successfully');     
         return  $x=session()->flash('success','Sent Successfully') ;
            }else{
              
               if(!empty($i['error'])){
                   $msg=$i['error'];
               }elseif(!empty($i['rejectedDocuments'])){
                   $msg=$i['rejectedDocuments'][0]['error']['details'][0]['message']; 
               }else{
                   $msg='Rejected';
               } 
              session()->flash('error',$msg);   
            return  $x=session()->flash('error',$msg) ;
                 
            }    
                   
      
            
}    

        public function Bill_ReturnSales_Sent(){

                $items=ReturnSales::orderBy('id','desc')
            ->where('Sent',1)->where('TaxBill',1)->paginate(100);

          
                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Bill_ReturnSales_Sent',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
     public function Bill_ReturnSales_SentFilter(){

                
                  $Client=request('Client');
         $Payment_Method=request('Payment_Method');
         $Code=request('Code');
         $UUID=request('UUID');
         $Submission_UUID=request('Submission_UUID');
    
                $items=ReturnSales::whereBetween('Date',[request('From'),request('To')])
            ->where('Sent',1)
                    ->where('TaxBill',1)
                    
             ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client', $Client);
    })     
                    
                                  ->when(!empty($Payment_Method), function ($query) use ($Payment_Method) {
        return $query->where('Payment_Method', $Payment_Method);
    })    
                    
                                  ->when(!empty($Code), function ($query) use ($Code) {
        return $query->where('TaxCode', $Code);
    })    
                    
                    
                                        ->when(!empty($UUID), function ($query) use ($UUID) {
        return $query->where('uuid', $UUID);
    })      
                    
                    
                                    ->when(!empty($Submission_UUID), function ($query) use ($Submission_UUID) {
        return $query->where('submissionId', $Submission_UUID);
    })                      
                    
                    ->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Bill_ReturnSales_Sent',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
//CancelSalesElectronic
         public function CancelSalesElectronic($uuid){
          
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://id.preprod.eta.gov.eg/connect/token',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));


$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
    
        $i=$this->CancelSales($i['access_token'],$uuid);
      
              if(!empty($i['error'])){
                  $msg=$i['error']['details'][0]['message'];
                session()->flash('error',$msg);   
                 
            }else{
 
                $msg='Canceled';
             session()->flash('success',$msg);
            }

return redirect('Bill_Sales_Sent_Web');
        

    }
    
    private function CancelSales($x,$uuid){
         $Company=CompanyData::orderBy('id','desc')->first(); 
      $curl = curl_init();

        if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1.0/documents/state/'.$uuid.'/state';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1.0/documents/state/'.$uuid.'/state';    
}
        
curl_setopt_array($curl, array(
  CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'PUT',
  CURLOPT_POSTFIELDS =>'{
	"status":"cancelled",
	"reason":"some reason for cancelled document"
}',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/json',
 'Authorization: Bearer  '.$x.' '
  ),
));

$response = curl_exec($curl);

curl_close($curl);
     
           return json_decode($response, true);
        

           
        
    }

    //RejectSalesElectronic
             public function RejectSalesElectronic($uuid){
          
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://id.preprod.eta.gov.eg/connect/token',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/x-www-form-urlencoded',
    ': '
  ),
));


$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
    
        $i=$this->RejectSales($i['access_token'],$uuid);
      
              if(!empty($i['error'])){
                  $msg=$i['error']['details'][0]['message'];
                session()->flash('error',$msg);   
                 
            }else{
 
                $msg='Canceled';
             session()->flash('success',$msg);
            }

return redirect('Bill_Sales_Sent_Web');
        

    }

    private function RejectSales($x,$uuid){
         $Company=CompanyData::orderBy('id','desc')->first(); 
$curl = curl_init();

        
                if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1.0/documents/state/'.$uuid.'/state';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1.0/documents/state/'.$uuid.'/state';    
}
        
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'PUT',
  CURLOPT_POSTFIELDS =>'{
	"status":"rejected",
	"reason":"some reason for rejected document"
}',
  CURLOPT_HTTPHEADER => array(
    'Content-Type: application/json',
'Authorization: Bearer  '.$x.' '
  ),
));

$response = curl_exec($curl);

curl_close($curl);

           return json_decode($response, true);
        

           
        
    }
    
// ======  Recipt  ===================================================================  

           public function Send_Recipt_Sales(){

                $items=Sales::orderBy('id','desc')
            ->where('Sent',null)->where('TaxBill',1)->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.Send_Recipt_Sales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
      public function SendSalesRecipt($id){
 $Company=CompanyData::orderBy('id','desc')->first();
                      $sale=Sales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
           $Prods=ProductSales::where('Sales',$sale->id)->get();
             $PRODUCTS=array();
             $FIRST=array();
             $SECOND=array();
          
          
          
                 if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
  
          
$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'posserial: '.$Company->Computer_SN.' ',
    'pososversion: '.$Company->POS_Version.' ',
    'Content-Type: application/x-www-form-urlencoded'
  ),
));
          
$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
          
  //UUID 
     array_push($FIRST,
                
         "HEADER",
         "DATETIMEISSUED",
         "".$sale->Date."T00:34:00Z",
         "RECEIPTNUMBER",
          "".$sale->TaxCode."",
         "UUID",
         "",
         "PREVIOUSUUID",
         "",
         "REFERENCEOLDUUID",
         "",
         "CURRENCY",
         "EGP",
         "EXCHANGERATE",
         "0",
         "SORDERNAMECODE",
         "sOrderNameCode",
         "ORDERDELIVERYMODE",
         "",
         "GROSSWEIGHT",
         "0.00",
         "NETWEIGHT",
         "0.00",
         "DOCUMENTTYPE",
         "RECEIPTTYPE",
         "S",
         "TYPEVERSION",
         "1.2",
         "SELLER",
         "RIN",
         "".$Company->Tax_Registration_Number."",
         "COMPANYTRADENAME",
         "".$Company->Name."",
         "BRANCHCODE",
         "".$BRANCHCODE."",
         "BRANCHADDRESS",
         "COUNTRY",
         "EG",
         "GOVERNATE",
         "".$Company->Governrate()->first()->Arabic_Name."",
         "REGIONCITY",
         "".$Company->City()->first()->Arabic_Name."",
         "STREET",
         "".$Company->Street."",
         "BUILDINGNUMBER",
         "".$Company->Buliding_Num."",
         "POSTALCODE",
         "".$Company->Postal_Code."",
         "FLOOR",
         "".$Company->Floor."",
         "ROOM",
         "".$Company->Room."",
         "LANDMARK",
         "".$Company->Landmark."",
         "ADDITIONALINFORMATION",
         "".$Company->Add_Info."",
         "DEVICESERIALNUMBER",
         "".$Company->Computer_SN."",
         "SYNDICATELICENSENUMBER",
         "1000056",
         "ACTIVITYCODE",
         "".$Company->Tax_activity_code."",
         "BUYER",
         "TYPE",
         "".$Customer->work_nature."",
         "ID",
         "".$Customer->Tax_Registration_Number."",
         "NAME",
         "".$Customer->Name."",
         "MOBILENUMBER",
         "",
         "PAYMENTNUMBER",
         "987654",
         "ITEMDATA"
               );    

   
       array_push($SECOND,
                
        "TOTALSALES",
        "".$sale->Total_Price."",
        "TOTALCOMMERCIALDISCOUNT",
        "0.00",
        "TOTALITEMSDISCOUNT",
        "".(float)$sale->Total_Discount."",
        "EXTRARECEIPTDISCOUNTDATA",
        "EXTRARECEIPTDISCOUNTDATA",
        "AMOUNT",
        "0",
         "DESCRIPTION",
         "ABC",
         "NETAMOUNT",
         "".(float)($sale->Total_Price - $sale->Total_Discount)."",
         "FEESAMOUNT",
         "0",
         "TOTALAMOUNT",
         "".(float)($sale->Total_Price + ($sale->Total_Taxes - $sale->DiscountTax) - $sale->Total_Discount)."",
         "TAXTOTALS",
         "TAXTOTALS",
         "TAXTYPE",
         "T1",
         "AMOUNT",
         "".$sale->Total_Taxes."",
        "TAXTOTALS",
         "TAXTYPE",
         "T4",
         "AMOUNT",
         "".(float)$sale->DiscountTax."",          
         "PAYMENTMETHOD",
         "C",
         "ADJUSTMENT",
         "0",
         "CONTRACTOR",
        "NAME",          
         "contractor1",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00",
         "BENEFICIARY",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00"
               );    
        
      
              foreach($Prods as $pro){
             
       array_push($PRODUCTS,
                
                    "ITEMDATA",
                    "INTERNALCODE",
                    "".$pro->Product_Code."",
                    "DESCRIPTION",
                    "".$pro->P_Ar_Name."",
                    "ITEMTYPE",
                    "".$pro->Product()->first()->Code_Type."",
                    "ITEMCODE",
                   "".$pro->Product()->first()->World_Code."",
                    "UNITTYPE",
                    "".$pro->Unit()->first()->Code."",
                    "QUANTITY",
                    "".$pro->Qty."",
                    "UNITPRICE",
                    "".$pro->Price."", 
                    "NETSALE",
                    "".$pro->NetTotal."",
                    "TOTALSALE",
                    "".$pro->SalesTotal."", 
                    "TOTAL",
                    "".$pro->TotalBill."",
                    "COMMERCIALDISCOUNTDATA",
                    "COMMERCIALDISCOUNTDATA",
                    "AMOUNT",
                   "0",  
                   "DESCRIPTION",
                   "XYZ",  
                   "ITEMDISCOUNTDATA",
                   "ITEMDISCOUNTDATA",
                   "AMOUNT",
                   "".$pro->DiscountAmount."",  
                   "DESCRIPTION",
                   "ABC",  
                   "VALUEDIFFERENCE",
                    "0", 
                    "TAXABLEITEMS",
                    "TAXABLEITEMS",
                    "TAXTYPE",
                    "".$pro->TaxType."", 
                    "AMOUNT",
                    "".$pro->TaxAmount."", 
                    "SUBTYPE",
                    "".$pro->TaxSubType."", 
                    "RATE",
                    "".$pro->TaxRate."" 
               );    
          }

            
            $fir='"'.implode('""', $FIRST).'"';
            $Pros='"'.implode('""', $PRODUCTS).'"';
            $sec='"'.implode('""', $SECOND).'"';
           
             $z=''.$fir.''.$Pros.''.$sec.'';
             $hash = hash('sha256',$z);      
           $uuid=strtoupper($hash);     

          $this->ReciptPAY($i['access_token'],$id,$uuid);

         return redirect('Send_Recipt_Sales');

    }
    
            private function ReciptPAY($x,$id,$uuid){
                   ini_set('precision', 6);
          ini_set('serialize_precision', 6);
            $Company=CompanyData::orderBy('id','desc')->first(); 
            $sale=Sales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
        
            $out=[];
            $Prods=ProductSales::where('Sales',$sale->id)->get();
             $PRODUCTS=array();

          foreach($Prods as $pro){
              
            $Q=$pro->Qty;  
            $SalesTotal=($pro->Price * $pro->Qty);  
            $discountAmount=$pro->Discount * $pro->Qty;    
            $netTotal=$SalesTotal -  $discountAmount;  
            $amountEGP=$pro->Price;  
            $taxableItems=($pro->Tax()->first()->Rate / 100) *  $netTotal;
            $taxableItemsAmount=number_format($taxableItems, 2, '.', '');
            $total=$netTotal + $taxableItemsAmount;

       array_push($PRODUCTS,[
                 
                    "internalCode"=>$pro->Product_Code,
                    "description"=>$pro->P_Ar_Name,
                    "itemType"=> $pro->Product()->first()->Code_Type,
                    "itemCode"=> $pro->Product()->first()->World_Code,
                    "unitType"=> $pro->Unit()->first()->Code,
                    "quantity"=> (float)$Q,
                    "unitPrice"=>  (float)$pro->Price ,
                    "netSale"=>  (float)$pro->NetTotal ,
                    "totalSale"=>  (float)$pro->SalesTotal ,
                    "total"=>  (float)$pro->TotalBill ,
                    "commercialDiscountData"=> [
                        [
                             "amount"=> 0, 
                             "description"=> "XYZ"
                         ]
                    ],
                    "itemDiscountData"=> [
                      [  
                            "amount"=> (float)$pro->DiscountAmount,
                            "description"=>"ABC"
                        ]
                    ],
                    "valueDifference"=> 0,
                    "taxableItems"=> [
                        [
                                "taxType"=> $pro->TaxType,
                                "amount"=>  (float)$pro->TaxAmount ,
                                "subType"=> $pro->TaxSubType,
                                "rate"=> (float)$pro->TaxRate
                        ]
                    ]
        
               ]);    
          }

            $xx=json_encode($PRODUCTS);

$curl = curl_init();
if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/receiptsubmissions';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/receiptsubmissions';    
}
            
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
                
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS =>'{
    "receipts": [
        {
            "header": {
                "dateTimeIssued": "'.$sale->Date.'T00:34:00Z",
                "receiptNumber": "'.$sale->TaxCode.'",
                "uuid": "'.$uuid.'",
                "previousUUID": "",
                "referenceOldUUID": "",
                "currency": "EGP",
                "exchangeRate": 0,
                "sOrderNameCode": "sOrderNameCode",
                "orderdeliveryMode": "",
                "grossWeight": 0.00,
                "netWeight": 0.00
              
            },
            "documentType": {
                "receiptType": "S",
                "typeVersion": "1.2"
            },
            "seller": {
                "rin": "'.$Company->Tax_Registration_Number.'",
                "companyTradeName": "'.$Company->Name.'",
                "branchCode": "'.$BRANCHCODE.'",
                "branchAddress": {
                    "country": "EG",
                    "governate": "'.$Company->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Company->City()->first()->Arabic_Name.'",
                    "street": "'.$Company->Street.'",
                    "buildingNumber": "'.$Company->Buliding_Num.'",
                    "postalCode": "'.$Company->Postal_Code.'",
                    "floor": "'.$Company->Floor.'",
                    "room": "'.$Company->Room.'",
                    "landmark": "'.$Company->Landmark.'",
                    "additionalInformation": "'.$Company->Add_Info.'"
                },
                "deviceSerialNumber": "'.$Company->Computer_SN.'",
                "syndicateLicenseNumber": "1000056",
                "activityCode": "'.$Company->Tax_activity_code.'"
            },
            "buyer": {
                "type": "'.$Customer->work_nature.'",
                "id": "'.$Customer->Tax_Registration_Number.'",
                "name": "'.$Customer->Name.'",
                "mobileNumber": "",
                "paymentNumber": "987654"
            },
            "itemData": '.$xx.' ,
            "totalSales": '.$sale->Total_Price.',
            "totalCommercialDiscount": 0.00,
            "totalItemsDiscount": '.(float)$sale->Total_Discount.',
            "extraReceiptDiscountData": [
               {
                   "amount": 0,
                   "description": "ABC"
               }
            ],
            "netAmount": '.($sale->Total_Price - $sale->Total_Discount).',
            "feesAmount": 0,
            "totalAmount": '.($sale->Total_Price + ($sale->Total_Taxes - $sale->DiscountTax) - $sale->Total_Discount).',
            "taxTotals": [
                    {
                        "taxType": "T1",
                        "amount": '.$sale->Total_Taxes.'
                    },
                    {
                        "taxType": "T4",
                        "amount": '.$sale->DiscountTax.'
                    }
            ],
            "paymentMethod": "C",
            "adjustment": 0,
            "contractor": {
                "name": "contractor1",
                "amount": 0.00,
                "rate": 0.00
            },
            "beneficiary": {
                "amount": 0.00,
                "rate": 0.00
            }
        }
    ]
    
}',
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer '.$x.' ',
    'Content-Type: application/json'
  ),
));

          
$response = curl_exec($curl);
                

curl_close($curl);

   $i=json_decode($response,true); 


            if(!empty($i['submissionId'])){

              Sales::where('id',$id)->update([
                  'Sent'=>1,
                  'uuid'=>$i['acceptedDocuments'][0]['uuid'],
                  'longId'=>$i['acceptedDocuments'][0]['longId'],
                  'hashKey'=>$i['acceptedDocuments'][0]['hashKey'],
                  'submissionId'=>$i['submissionId'],
              ]);  
               session()->flash('success','Sent Successfully');     
         return  $x=session()->flash('success','Sent Successfully') ;
            }else{
              
               if(!empty($i['error'])){
                   $msg=$i['error'];
               }elseif(!empty($i['rejectedDocuments'])){
                   $msg=$i['rejectedDocuments'][0]['error']['details'][0]['message']; 
               }else{
                   $msg='Rejected';
               } 
              session()->flash('error',$msg);   
            return  $x=session()->flash('error',$msg) ;
                 
            }    
                   
      
            
}    
    
    //Recipt Sent
         public function Recipt_Sales_Sent(){

         return view('admin.ElectonicBill.Recipt_Sales_Sent');
    }
    
          public function FilterRecipt_Sales_Sent_Web(Request $request){
          
  $page = $request->get('Page');          
  $status = $request->get('status');      
  $uuid = $request->get('uuid');      
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();
        
    if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
  
          
$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'posserial: '.$Company->Computer_SN.' ',
    'pososversion: '.$Company->POS_Version.' ',
    'Content-Type: application/x-www-form-urlencoded'
  ),
));
          
$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
    
          $this->FilterSalesReciptt($i['access_token'],$page,$status,$uuid);

        

    }

    private function FilterSalesReciptt($x,$page,$status,$uuid){
        
 $Company=CompanyData::orderBy('id','desc')->first();
$curl = curl_init();


            if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/receipts/recent?Uuid='.$uuid.'&Status='.$status.'&PageNo='.$page.'&PageSize=10';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/receipts/recent?Uuid='.$uuid.'&Status='.$status.'&PageNo='.$page.'&PageSize=10';    
}
        
        
        
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer '.$x.' '
  ),
));        
        
        
$response = curl_exec($curl);

curl_close($curl);
        
        
$xs=json_decode($response,true); 

          $output = '';
 
        if(!empty($xs['receipts'])){
    for($i=0; $i < count($xs['receipts']); $i++){
     
     $output .= '<tr>
          
          <td>'.$xs['receipts'][$i]['submissionUuid'].'</td>
          <td>'.$xs['receipts'][$i]['uuid'].'</td>
          <td>'.$xs['receipts'][$i]['direction'].'</td>
          <td>'.$xs['receipts'][$i]['receiptNumber'].'</td>
          <td>'.$xs['receipts'][$i]['dateTimeIssued'].'</td>
          <td>'.$xs['receipts'][$i]['dateTimeReceived'].'</td>
          <td>'.$xs['receipts'][$i]['totalAmount'].'</td>
          <td>'.$xs['receipts'][$i]['posSerialNumber'].'</td>
          <td>'.$xs['receipts'][$i]['paymentMethod'].'</td>
          <td>'.$xs['receipts'][$i]['submitterName'].'</td>
          <td>'.$xs['receipts'][$i]['submissionChannel'].'</td>
          <td>'.$xs['receipts'][$i]['status'].'</td>
          <td>'.$xs['receipts'][$i]['issuerId'].'</td>
          <td>'.$xs['receipts'][$i]['issuerName'].'</td>
          <td>'.$xs['receipts'][$i]['receiverType'].'</td>
          <td>'.$xs['receipts'][$i]['receiverId'].'</td>
          <td>'.$xs['receipts'][$i]['receiverName'].'</td>
          <td>'.$xs['receipts'][$i]['typeName'].'</td>
          <td>'.$xs['receipts'][$i]['documentTypeNamePrimaryLang'].'</td>
          <td>'.$xs['receipts'][$i]['documentTypeNameSecondaryLang'].'</td>
          <td>'.$xs['receipts'][$i]['documentTypeBase'].'</td>
          <td>'.$xs['receipts'][$i]['typeVersionName'].'</td>
          <td>'.$xs['receipts'][$i]['currency'].'</td>
          <td>'.$xs['receipts'][$i]['currencyNamePrimaryLang'].'</td>
          <td>'.$xs['receipts'][$i]['currencyNameSecondaryLang'].'</td>
          <td>'.$xs['receipts'][$i]['exchangeRate'].'</td>
      <td>
     <a class="btn btn-success" target="_blank" href="https://invoicing.eta.gov.eg/receipts/search/'.$xs['receipts'][$i]['uuid'].'/share/'.$xs['receipts'][$i]['dateTimeIssued'].'"> Open in Website</a>
 <a class="btn btn-primary"  target="_blank"  href="https://invoicing.eta.gov.eg/receipts/details/print/'.$xs['receipts'][$i]['uuid'].'/share/'.$xs['receipts'][$i]['dateTimeIssued'].'"> Print</a>
          </td>

          </tr>';
        
 
    }    
        }else{
            
             $output .= '<tr>
            <td> No Data  </td>
 </tr>';
        }
  $data = array(
       'table_data'  => $output,
      );
        
        
      echo json_encode($data);
        

    }

    
    //ReturnSend_Recipt_Sales
           public function ReturnSend_Recipt_Sales(){

                $items=ReturnSales::orderBy('id','desc')
            ->where('Sent',null)->where('TaxBill',1)->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
               
       
       
         return view('admin.ElectonicBill.ReturnSend_Recipt_Sales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }

         public function ReturnSend_Recipt_SalesFilter(){
         $Client=request('Client');
         $Payment_Method=request('Payment_Method');
         $Code=request('Code');
   
                $items=ReturnSales::whereBetween('Date',[request('From'),request('To')])
                           ->where('Sent',null)
                    ->where('TaxBill',1)
                             ->when(!empty($Client), function ($query) use ($Client) {
        return $query->where('Client', $Client);
    })     
                    
                                  ->when(!empty($Payment_Method), function ($query) use ($Payment_Method) {
        return $query->where('Payment_Method', $Payment_Method);
    })    
                    
                                  ->when(!empty($Code), function ($query) use ($Code) {
        return $query->where('TaxCode', $Code);
    })                
                    
              
                    
                    ->paginate(100);

                    $Clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();
    
         return view('admin.ElectonicBill.ReturnSend_Recipt_Sales',[
             'items'=>$items,
             'Clients'=>$Clients,
    
         ]);
    }
    
          public function ReturnSendSalesRecipt($id){
 $Company=CompanyData::orderBy('id','desc')->first();
             $sale=ReturnSales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
          $Prods=ReturnSalesProducts::where('Return',$sale->id)->get();
             $PRODUCTS=array();
             $FIRST=array();
             $SECOND=array();
          
          
          
                 if($Company->Invoice_Type == 'Experimental'){
  $version='https://id.preprod.eta.gov.eg/connect/token';  
}else{
  $version='https://id.eta.gov.eg/connect/token';    
}
  
          
$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL =>$version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => 'grant_type=client_credentials&client_id='.$Company->Client_ID.'&client_secret='.$Company->Serial_Client_ID.'',
  CURLOPT_HTTPHEADER => array(
    'posserial: '.$Company->Computer_SN.' ',
    'pososversion: '.$Company->POS_Version.' ',
    'Content-Type: application/x-www-form-urlencoded'
  ),
));
          
$response = curl_exec($curl);

curl_close($curl);

$i=json_decode($response,true); 
          
  //UUID 
     array_push($FIRST,
                
         "HEADER",
         "DATETIMEISSUED",
         "".$sale->Date."T00:34:00Z",
         "RECEIPTNUMBER",
          "".$sale->TaxCode."",
         "UUID",
         "",
         "PREVIOUSUUID",
         "",
         "REFERENCEUUID",
         "".$sale->Sales()->first()->uuid."",
         "REFERENCEOLDUUID",
         "",
         "CURRENCY",
         "EGP",
         "EXCHANGERATE",
         "0",
         "SORDERNAMECODE",
         "sOrderNameCode",
         "ORDERDELIVERYMODE",
         "",
         "GROSSWEIGHT",
         "0.00",
         "NETWEIGHT",
         "0.00",
         "DOCUMENTTYPE",
         "RECEIPTTYPE",
         "R",
         "TYPEVERSION",
         "1.2",
         "SELLER",
         "RIN",
         "".$Company->Tax_Registration_Number."",
         "COMPANYTRADENAME",
         "".$Company->Name."",
         "BRANCHCODE",
         "".$BRANCHCODE."",
         "BRANCHADDRESS",
         "COUNTRY",
         "EG",
         "GOVERNATE",
         "".$Company->Governrate()->first()->Arabic_Name."",
         "REGIONCITY",
         "".$Company->City()->first()->Arabic_Name."",
         "STREET",
         "".$Company->Street."",
         "BUILDINGNUMBER",
         "".$Company->Buliding_Num."",
         "POSTALCODE",
         "".$Company->Postal_Code."",
         "FLOOR",
         "".$Company->Floor."",
         "ROOM",
         "".$Company->Room."",
         "LANDMARK",
         "".$Company->Landmark."",
         "ADDITIONALINFORMATION",
         "".$Company->Add_Info."",
         "DEVICESERIALNUMBER",
         "".$Company->Computer_SN."",
         "SYNDICATELICENSENUMBER",
         "1000056",
         "ACTIVITYCODE",
         "".$Company->Tax_activity_code."",
         "BUYER",
         "TYPE",
         "".$Customer->work_nature."",
         "ID",
         "".$Customer->Tax_Registration_Number."",
         "NAME",
         "".$Customer->Name."",
         "MOBILENUMBER",
         "",
         "PAYMENTNUMBER",
         "987654",
         "ITEMDATA"
               );    




       array_push($SECOND,
                
        "TOTALSALES",
        "".($sale->Total_Return_Value   -  ($sale->Total_Taxes + $sale->Total_Discount) )."",
        "TOTALCOMMERCIALDISCOUNT",
        "0.00",
        "TOTALITEMSDISCOUNT",
        "".$sale->Total_Discount."",
        "EXTRARECEIPTDISCOUNTDATA",
        "EXTRARECEIPTDISCOUNTDATA",
        "AMOUNT",
        "0",
         "DESCRIPTION",
         "ABC",
         "NETAMOUNT",
         "".($sale->Total_Return_Value - $sale->Total_Discount - $sale->Total_Taxes)."",
         "FEESAMOUNT",
         "0",
         "TOTALAMOUNT",
         "".($sale->Total_Return_Value)."",
         "TAXTOTALS",
         "TAXTOTALS",
         "TAXTYPE",
         "T1",
         "AMOUNT",
         "".$sale->Total_Taxes."",
         "PAYMENTMETHOD",
         "C",
         "ADJUSTMENT",
         "0",
         "CONTRACTOR",
        "NAME",          
         "contractor1",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00",
         "BENEFICIARY",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00"
               );    
        
      
              foreach($Prods as $pro){
             
       array_push($PRODUCTS,
                
                    "ITEMDATA",
                    "INTERNALCODE",
                    "".$pro->Product_Code."",
                    "DESCRIPTION",
                    "".$pro->P_Ar_Name."",
                    "ITEMTYPE",
                    "".$pro->Product()->first()->Code_Type."",
                    "ITEMCODE",
                   "".$pro->Product()->first()->World_Code."",
                    "UNITTYPE",
                    "".$pro->Unit()->first()->Code."",
                    "QUANTITY",
                    "".$pro->Recived_Qty."",
                    "UNITPRICE",
                    "".$pro->Price."", 
                    "NETSALE",
                    "".$pro->NetTotal."",
                    "TOTALSALE",
                    "".$pro->SalesTotal."", 
                    "TOTAL",
                    "".$pro->TotalBill."",
                    "COMMERCIALDISCOUNTDATA",
                    "COMMERCIALDISCOUNTDATA",
                    "AMOUNT",
                   "0",  
                   "DESCRIPTION",
                   "XYZ",  
                   "ITEMDISCOUNTDATA",
                   "ITEMDISCOUNTDATA",
                   "AMOUNT",
                   "".$pro->DiscountAmount."",  
                   "DESCRIPTION",
                   "ABC",  
                   "VALUEDIFFERENCE",
                    "0", 
                    "TAXABLEITEMS",
                    "TAXABLEITEMS",
                    "TAXTYPE",
                    "".$pro->TaxType."", 
                    "AMOUNT",
                    "".$pro->TaxAmount."", 
                    "SUBTYPE",
                    "".$pro->TaxSubType."", 
                    "RATE",
                    "".$pro->TaxRate."" 
               );    
          }

                            
            
            $fir='"'.implode('""', $FIRST).'"';
            $Pros='"'.implode('""', $PRODUCTS).'"';
            $sec='"'.implode('""', $SECOND).'"';
           
             $z=''.$fir.''.$Pros.''.$sec.'';
             $hash = hash('sha256',$z);      
           $uuid=strtoupper($hash);     

          $this->ReturnReciptPAY($i['access_token'],$id,$uuid);

         return redirect('ReturnSend_Recipt_Sales');

    }
    
            private function ReturnReciptPAY($x,$id,$uuid){
                   ini_set('precision', 6);
          ini_set('serialize_precision', 6);
            $Company=CompanyData::orderBy('id','desc')->first(); 
            $sale=ReturnSales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
        
            $out=[];
           $Prods=ReturnSalesProducts::where('Return',$sale->id)->get();
             $PRODUCTS=array();

          foreach($Prods as $pro){
              
            $Q=$pro->Recived_Qty;  
            $SalesTotal=($pro->Price * $pro->Qty);  
            $discountAmount=$pro->Discount * $pro->Qty;    
            $netTotal=$SalesTotal -  $discountAmount;  
            $amountEGP=$pro->Price;  
            $taxableItems=($pro->Tax()->first()->Rate / 100) *  $netTotal;
            $taxableItemsAmount=number_format($taxableItems, 2, '.', '');
            $total=$netTotal + $taxableItemsAmount;

       array_push($PRODUCTS,[
                 
                    "internalCode"=>$pro->Product_Code,
                    "description"=>$pro->P_Ar_Name,
                    "itemType"=> $pro->Product()->first()->Code_Type,
                    "itemCode"=> $pro->Product()->first()->World_Code,
                    "unitType"=> $pro->Unit()->first()->Code,
                    "quantity"=> (float)$Q,
                    "unitPrice"=>  (float)$pro->Price ,
                    "netSale"=>  (float)$pro->NetTotal ,
                    "totalSale"=>  (float)$pro->SalesTotal ,
                    "total"=>  (float)$pro->TotalBill ,
                    "commercialDiscountData"=> [
                        [
                             "amount"=> 0, 
                             "description"=> "XYZ"
                         ]
                    ],
                    "itemDiscountData"=> [
                      [  
                            "amount"=> (float)$pro->DiscountAmount,
                            "description"=>"ABC"
                        ]
                    ],
                    "valueDifference"=> 0,
                    "taxableItems"=> [
                        [
                                "taxType"=> $pro->TaxType,
                                "amount"=>  (float)$pro->TaxAmount ,
                                "subType"=> $pro->TaxSubType,
                                "rate"=> (float)$pro->TaxRate
                        ]
                    ]
        
               ]);    
          }

                        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
                
                            $xx=json_encode($PRODUCTS);
$curl = curl_init();
if($Company->Invoice_Type == 'Experimental'){
  $version='https://api.preprod.invoicing.eta.gov.eg/api/v1/receiptsubmissions';  
}else{
  $version='https://api.invoicing.eta.gov.eg/api/v1/receiptsubmissions';    
}
     
                
           
                
curl_setopt_array($curl, array(
  CURLOPT_URL => $version,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS =>'{
    "receipts": [
        {
            "header": {
                "dateTimeIssued": "'.$sale->Date.'T00:34:00Z",
                "receiptNumber": "'.$sale->TaxCode.'",
                "uuid": "'.$uuid.'",
                "previousUUID": "",
                "referenceUUID":"'.$sale->Sales()->first()->uuid.'",
                "referenceOldUUID": "",
                "currency": "EGP",
                "exchangeRate": 0,
                "sOrderNameCode": "sOrderNameCode",
                "orderdeliveryMode": "",
                "grossWeight": 0.00,
                "netWeight": 0.00
              
            },
            "documentType": {
                "receiptType": "R",
                "typeVersion": "1.2"
            },
            "seller": {
                "rin": "'.$Company->Tax_Registration_Number.'",
                "companyTradeName": "'.$Company->Name.'",
                "branchCode": "'.$BRANCHCODE.'",
                "branchAddress": {
                    "country": "EG",
                    "governate": "'.$Company->Governrate()->first()->Arabic_Name.'",
                    "regionCity": "'.$Company->City()->first()->Arabic_Name.'",
                    "street": "'.$Company->Street.'",
                    "buildingNumber": "'.$Company->Buliding_Num.'",
                    "postalCode": "'.$Company->Postal_Code.'",
                    "floor": "'.$Company->Floor.'",
                    "room": "'.$Company->Room.'",
                    "landmark": "'.$Company->Landmark.'",
                    "additionalInformation": "'.$Company->Add_Info.'"
                },
                "deviceSerialNumber": "'.$Company->Computer_SN.'",
                "syndicateLicenseNumber": "1000056",
                "activityCode": "'.$Company->Tax_activity_code.'"
            },
            "buyer": {
                "type": "'.$Customer->work_nature.'",
                "id": "'.$Customer->Tax_Registration_Number.'",
                "name": "'.$Customer->Name.'",
                "mobileNumber": "",
                "paymentNumber": "987654"
            },
            "itemData": '.$xx.' ,
            "totalSales": '.($sale->Total_Return_Value   -  ($sale->Total_Taxes + $sale->Total_Discount) ).',
            "totalCommercialDiscount": 0.00,
            "totalItemsDiscount": '.$sale->Total_Discount.',
            "extraReceiptDiscountData": [
               {
                   "amount": 0,
                   "description": "ABC"
               }
            ],
            "netAmount": '.($sale->Total_Return_Value - $sale->Total_Discount - $sale->Total_Taxes).',
            "feesAmount": 0,
            "totalAmount": '.($sale->Total_Return_Value).',
            "taxTotals": [
                    {
                        "taxType": "T1",
                        "amount": '.$sale->Total_Taxes.'
                    }
            ],
            "paymentMethod": "C",
            "adjustment": 0,
            "contractor": {
                "name": "contractor1",
                "amount": 0.00,
                "rate": 0.00
            },
            "beneficiary": {
                "amount": 0.00,
                "rate": 0.00
            }
        }
    ]
    
}',
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer '.$x.' ',
    'Content-Type: application/json'
  ),
));

                
                
          
$response = curl_exec($curl);
           

curl_close($curl);

   $i=json_decode($response,true); 


            if(!empty($i['submissionId'])){

              ReturnSales::where('id',$id)->update([
                  'Sent'=>1,
                  'uuid'=>$i['acceptedDocuments'][0]['uuid'],
                  'longId'=>$i['acceptedDocuments'][0]['longId'],
                  'hashKey'=>$i['acceptedDocuments'][0]['hashKey'],
                  'submissionId'=>$i['submissionId'],
              ]);  
               session()->flash('success','Sent Successfully');     
         return  $x=session()->flash('success','Sent Successfully') ;
            }else{
              
               if(!empty($i['error'])){
                   $msg=$i['error'];
               }elseif(!empty($i['rejectedDocuments'])){
                   $msg=$i['rejectedDocuments'][0]['error']['details'][0]['message']; 
               }else{
                   $msg='Rejected';
               } 
              session()->flash('error',$msg);   
            return  $x=session()->flash('error',$msg) ;
                 
            }    
                   
      
            
}    
    

    //Explain Create UUID
        public function HASHING(){

$x='"HEADER""DATETIMEISSUED""2023-03-28T00:34:00Z""RECEIPTNUMBER""27""UUID""""PREVIOUSUUID""""REFERENCEOLDUUID""""CURRENCY""EGP""EXCHANGERATE""0""SORDERNAMECODE""sOrderNameCode""ORDERDELIVERYMODE""""GROSSWEIGHT""0.00""NETWEIGHT""0.00""DOCUMENTTYPE""RECEIPTTYPE""S""TYPEVERSION""1.2""SELLER""RIN""*********""COMPANYTRADENAME""شركةالصوفى""BRANCHCODE""0""BRANCHADDRESS""COUNTRY""EG""GOVERNATE""cairo""REGIONCITY""1""STREET""1""BUILDINGNUMBER""1""POSTALCODE""74235""FLOOR""1F""ROOM""3R""LANDMARK""1""ADDITIONALINFORMATION""1""DEVICESERIALNUMBER""HB92NBX""SYNDICATELICENSENUMBER""1000056""ACTIVITYCODE""4761""BUYER""TYPE""F""ID""*********""NAME""taxpayer 1""MOBILENUMBER""""PAYMENTNUMBER""987654""ITEMDATA""ITEMDATA""INTERNALCODE""423837""DESCRIPTION""Blink""ITEMTYPE""GS1""ITEMCODE""*************""UNITTYPE""EA""QUANTITY""1""UNITPRICE""1000.00""NETSALE""1000.00""TOTALSALE""1000.00""TOTAL""1140.00""COMMERCIALDISCOUNTDATA""COMMERCIALDISCOUNTDATA""AMOUNT""0.00""DESCRIPTION""XYZ""ITEMDISCOUNTDATA""ITEMDISCOUNTDATA""AMOUNT""0.00""DESCRIPTION""ABC""VALUEDIFFERENCE""0.00""TAXABLEITEMS""TAXABLEITEMS""TAXTYPE""T1""AMOUNT""140.00""SUBTYPE""V009""RATE""14""TOTALSALES""1000.00""TOTALCOMMERCIALDISCOUNT""0.00""TOTALITEMSDISCOUNT""0.00""EXTRARECEIPTDISCOUNTDATA""EXTRARECEIPTDISCOUNTDATA""AMOUNT""0""DESCRIPTION""ABC""NETAMOUNT""1000.00""FEESAMOUNT""0""TOTALAMOUNT""1140.00""TAXTOTALS""TAXTOTALS""TAXTYPE""T1""AMOUNT""140.00""TAXTOTALS""TAXTYPE""T4""AMOUNT""0.00""PAYMENTMETHOD""C""ADJUSTMENT""0""CONTRACTOR""NAME""contractor1""AMOUNT""0.00""RATE""0.00""BENEFICIARY""AMOUNT""0.00""RATE""0.00"';         
            
            
            
$f='';
            
            
             $uuid = hash('sha256',$f);
            dd(strtoupper($uuid)); 

            $id=195;
            

            
// ay haga liha array bttkrr f bdaya mara w m3 kol bday ttktb example lw 3ndk sanfen masln     hukon bdaya  => "ITEMDATA"  b3d kida m3 kol sanf htktb kida =>
            
//         "ITEMDATA"
//         "INTERNALCODE""880609"
//         "DESCRIPTION""Blink"
//         "ITEMTYPE""GS1"
//         "ITEMCODE""*************"
//         "UNITTYPE""EA"
//         "QUANTITY""35" .....
//   tany sanf =>
            //         "ITEMDATA"
//         "INTERNALCODE""880609"
//         "DESCRIPTION""Blink"
//         "ITEMTYPE""GS1"
//         "ITEMCODE""*************"
//         "UNITTYPE""EA"
//         "QUANTITY""35" .....
            //w hkza  b2a f drayp w khsomat w ay haga liha array 
            
       $Company=CompanyData::orderBy('id','desc')->first(); 
            $sale=Sales::find($id);
            $Customer=Customers::where('Account',$sale->Client)->first(); 
                    
        if(!empty($sale->Branch()->first()->Code)){    
    $BRANCHCODE=$sale->Branch()->first()->Code;
        }else{
         $BRANCHCODE=0;   
        }
           $Prods=ProductSales::where('Sales',$sale->id)->get();
             $PRODUCTS=array();
             $FIRST=array();
             $SECOND=array();
       array_push($FIRST,
                
         "HEADER",
         "DATETIMEISSUED",
         "".$sale->Date."T02:04:45Z",
         "RECEIPTNUMBER",
          "".$sale->TaxCode."",
         "UUID",
         "",
         "PREVIOUSUUID",
         "",
         "REFERENCEOLDUUID",
         "",
         "CURRENCY",
         "EGP",
         "EXCHANGERATE",
         "0",
         "SORDERNAMECODE",
         "sOrderNameCode",
         "ORDERDELIVERYMODE",
         "",
         "GROSSWEIGHT",
         "0.00",
         "NETWEIGHT",
         "0.00",
         "DOCUMENTTYPE",
         "RECEIPTTYPE",
         "S",
         "TYPEVERSION",
         "1.2",
         "SELLER",
         "RIN",
         "".$Company->Tax_Registration_Number."",
         "COMPANYTRADENAME",
         "".$Company->Name."",
         "BRANCHCODE",
         "".$BRANCHCODE."",
         "BRANCHADDRESS",
         "COUNTRY",
         "EG",
         "GOVERNATE",
         "".$Company->Governrate()->first()->Arabic_Name."",
         "REGIONCITY",
         "".$Company->City()->first()->Arabic_Name."",
         "STREET",
         "".$Company->Street."",
         "BUILDINGNUMBER",
         "".$Company->Buliding_Num."",
         "POSTALCODE",
         "".$Company->Postal_Code."",
         "FLOOR",
         "".$Company->Floor."",
         "ROOM",
         "".$Company->Room."",
         "LANDMARK",
         "".$Company->Landmark."",
         "ADDITIONALINFORMATION",
         "".$Company->Add_Info."",
         "DEVICESERIALNUMBER",
         "".$Company->Computer_SN."",
         "SYNDICATELICENSENUMBER",
         "1000056",
         "ACTIVITYCODE",
         "".$Company->Tax_activity_code."",
         "BUYER",
         "TYPE",
         "".$Customer->work_nature."",
         "ID",
         "".$Customer->Tax_Registration_Number."",
         "NAME",
         "".$Customer->Name."",
         "MOBILENUMBER",
         "",
         "PAYMENTNUMBER",
         "987654",
         "ITEMDATA"
               );    

   
       array_push($SECOND,
                
        "TOTALSALES",
        "".$sale->Total_Price."",
        "TOTALCOMMERCIALDISCOUNT",
        "0.00",
        "TOTALITEMSDISCOUNT",
        "".$sale->Total_Discount."",
        "EXTRARECEIPTDISCOUNTDATA",
        "EXTRARECEIPTDISCOUNTDATA",
        "AMOUNT",
        "0",
         "DESCRIPTION",
         "ABC",
         "NETAMOUNT",
         "".($sale->Total_Price - $sale->Total_Discount)."",
         "FEESAMOUNT",
         "0",
         "TOTALAMOUNT",
         "".($sale->Total_Price + ($sale->Total_Taxes - $sale->DiscountTax) - $sale->Total_Discount)."",
         "TAXTOTALS",
         "TAXTOTALS",
         "TAXTYPE",
         "T1",
         "AMOUNT",
         "".$sale->Total_Taxes."",
        "TAXTOTALS",
         "TAXTYPE",
         "T4",
         "AMOUNT",
         "".$sale->DiscountTax."",          
         "PAYMENTMETHOD",
         "C",
         "ADJUSTMENT",
         "0",
         "CONTRACTOR",
         "NAME",
         "Name",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00",
         "BENEFICIARY",
         "AMOUNT",
         "0.00",
         "RATE",
         "0.00"
               );    
        
      
              foreach($Prods as $pro){
              
       array_push($PRODUCTS,
                
                    "ITEMDATA",
                    "INTERNALCODE",
                    "".$pro->Product_Code."",
                    "DESCRIPTION",
                    "".$pro->P_Ar_Name."",
                    "ITEMTYPE",
                    "".$pro->Product()->first()->Code_Type."",
                    "ITEMCODE",
                   "".$pro->Product()->first()->World_Code."",
                    "UNITTYPE",
                    "".$pro->Unit()->first()->Code."",
                    "QUANTITY",
                    "".(float)$pro->Qty."",
                    "UNITPRICE",
                    "".(float)$pro->CoinPrice."", 
                    "NETSALE",
                    "".(float)$pro->NetTotal."",
                    "TOTALSALE",
                    "".(float)$pro->SalesTotal."", 
                    "TOTAL",
                    "".(float)$pro->TotalBill."",
                    "COMMERCIALDISCOUNTDATA",
                    "COMMERCIALDISCOUNTDATA",
                    "AMOUNT",
                   "0.00",  
                   "DESCRIPTION",
                   "No",  
                   "ITEMDISCOUNTDATA",
                   "ITEMDISCOUNTDATA",
                   "AMOUNT",
                   "".(float)$pro->DiscountAmount."",  
                   "DESCRIPTION",
                   "Discount",  
                   "VALUEDIFFERENCE",
                    "0.00", 
                    "TAXABLEITEMS",
                    "TAXABLEITEMS",
                    "TAXTYPE",
                    "".$pro->TaxType."", 
                    "AMOUNT",
                    "".(float)$pro->TaxAmount."", 
                    "SUBTYPE",
                    "".$pro->TaxSubType."", 
                    "RATE",
                    "".(float)$pro->TaxRate."" 
               );    
          }

            
            $fir='"'.implode('""', $FIRST).'"';
            $Pros='"'.implode('""', $PRODUCTS).'"';
            $sec='"'.implode('""', $SECOND).'"';
           
            
             $z=''.$fir.''.$Pros.''.$sec.'';
             $uuid = hash('sha256',$z);
 
           
        /*   $Daate=date('Y-m-d'); $x='"HEADER""DATETIMEISSUED""'.$Daate.'T02:04:00Z""RECEIPTNUMBER""1""UUID""""PREVIOUSUUID""""REFERENCEOLDUUID""""CURRENCY""EGP""EXCHANGERATE""0""SORDERNAMECODE""sOrderNameCode""ORDERDELIVERYMODE""""GROSSWEIGHT""6.58""NETWEIGHT""6.89""DOCUMENTTYPE""RECEIPTTYPE""S""TYPEVERSION""1.2""SELLER""RIN""*********""COMPANYTRADENAME""شركةالصوفى""BRANCHCODE""0""BRANCHADDRESS""COUNTRY""EG""GOVERNATE""cairo""REGIONCITY""city center""STREET""16 street""BUILDINGNUMBER""14BN""POSTALCODE""74235""FLOOR""1F""ROOM""3R""LANDMARK""tahrir square""ADDITIONALINFORMATION""talaat harb street""DEVICESERIALNUMBER""HB92NBX""SYNDICATELICENSENUMBER""1000056""ACTIVITYCODE""4772""BUYER""TYPE""F""ID""*********""NAME""taxpayer 1""MOBILENUMBER""+************""PAYMENTNUMBER""987654""ITEMDATA""ITEMDATA""INTERNALCODE""880609""DESCRIPTION""Blink""ITEMTYPE""GS1""ITEMCODE""*************""UNITTYPE""EA""QUANTITY""35""UNITPRICE""247.96000""NETSALE""7810.74000""TOTALSALE""8678.60000""TOTAL""8887.04360""COMMERCIALDISCOUNTDATA""COMMERCIALDISCOUNTDATA""AMOUNT""867.86000""DESCRIPTION""XYZ""ITEMDISCOUNTDATA""ITEMDISCOUNTDATA""AMOUNT""10""DESCRIPTION""ABC""ITEMDISCOUNTDATA""AMOUNT""10""DESCRIPTION""XYZ""VALUEDIFFERENCE""20""TAXABLEITEMS""TAXABLEITEMS""TAXTYPE""T1""AMOUNT""1096.30360""SUBTYPE""V009""RATE""14""TOTALSALES""8678.60000""TOTALCOMMERCIALDISCOUNT""867.86000""TOTALITEMSDISCOUNT""20""EXTRARECEIPTDISCOUNTDATA""EXTRARECEIPTDISCOUNTDATA""AMOUNT""0""DESCRIPTION""ABC""NETAMOUNT""7810.74000""FEESAMOUNT""0""TOTALAMOUNT""8887.04360""TAXTOTALS""TAXTOTALS""TAXTYPE""T1""AMOUNT""1096.30360""PAYMENTMETHOD""C""ADJUSTMENT""0""CONTRACTOR""NAME""contractor1""AMOUNT""2.563""RATE""2.3""BENEFICIARY""AMOUNT""20.569""RATE""2.147"';  */
            
            
   // Steps to uuid          
            
//     "HEADER"
//         "DATETIMEISSUED""'.$sale->Date.'T02:04:45Z"
//         "RECEIPTNUMBER""1"
//         "UUID"""
//         "PREVIOUSUUID"""
//         "REFERENCEOLDUUID"""
//         "CURRENCY""EGP"
//         "EXCHANGERATE""0"
//         "SORDERNAMECODE""sOrderNameCode"
//         "ORDERDELIVERYMODE"""
//         "GROSSWEIGHT""6.58"
//         "NETWEIGHT""6.89"
//         "DOCUMENTTYPE"
//         "RECEIPTTYPE""S"
//         "TYPEVERSION""1.2"
//            
//         "SELLER"
//         "RIN""*********"
//         "COMPANYTRADENAME""شركةالصوفى"
//         "BRANCHCODE""0"
//         "BRANCHADDRESS"
//         "COUNTRY""EG"
//         "GOVERNATE""cairo"
//         "REGIONCITY""city center"
//         "STREET""16 street"
//         "BUILDINGNUMBER""14BN"
//         "POSTALCODE""74235"
//         "FLOOR""1F"
//         "ROOM""3R"
//         "LANDMARK""tahrir square"
//         "ADDITIONALINFORMATION""talaat harb street"
//         "DEVICESERIALNUMBER""HB92NBX"
//         "SYNDICATELICENSENUMBER""1000056"
//         "ACTIVITYCODE""4772"
//            
//         "BUYER"
//         "TYPE""F"
//         "ID""*********"
//         "NAME""taxpayer 1"
//         "MOBILENUMBER""+************"
//         "PAYMENTNUMBER""987654"
//            
//         "ITEMDATA"
            
//         "ITEMDATA"
//         "INTERNALCODE""880609"
//         "DESCRIPTION""Blink"
//         "ITEMTYPE""GS1"
//         "ITEMCODE""*************"
//         "UNITTYPE""EA"
//         "QUANTITY""35"
//         "UNITPRICE""247.96000"
//         "NETSALE""7810.74000"
//         "TOTALSALE""8678.60000"
//         "TOTAL""8887.04360"
//         "COMMERCIALDISCOUNTDATA"
            
//         "COMMERCIALDISCOUNTDATA"
//         "AMOUNT""867.86000"
//         "DESCRIPTION""XYZ"
            
//         "ITEMDISCOUNTDATA"
            
//         "ITEMDISCOUNTDATA"
//         "AMOUNT""10"
//         "DESCRIPTION""ABC"
            
//         "VALUEDIFFERENCE""20"
//         "TAXABLEITEMS"
            
//         "TAXABLEITEMS"
//         "TAXTYPE""T1"
//         "AMOUNT""1096.30360"
//         "SUBTYPE""V009"
//         "RATE""14"
            
//         "TOTALSALES""8678.60000"
//         "TOTALCOMMERCIALDISCOUNT""867.86000"
//         "TOTALITEMSDISCOUNT""20"
//         "EXTRARECEIPTDISCOUNTDATA"
//            
//         "EXTRARECEIPTDISCOUNTDATA"
//         "AMOUNT""0"
//         "DESCRIPTION""ABC"
//            
//         "NETAMOUNT""7810.74000"
//         "FEESAMOUNT""0"
//         "TOTALAMOUNT""8887.04360"
//         "TAXTOTALS"
//            
//         "TAXTOTALS"
//         "TAXTYPE""T1"
//         "AMOUNT""1096.30360"
//            
//         "PAYMENTMETHOD""C"
//         "ADJUSTMENT""0"
//            
//         "CONTRACTOR"
//         "NAME""contractor1"
//         "AMOUNT""2.563"
//         "RATE""2.3"
//            
//         "BENEFICIARY"
//         "AMOUNT""20.569"
//         "RATE""2.147"
             
            

             

           return back();
        

           
        
    }
    
        
 
   
    
    
    
    
}

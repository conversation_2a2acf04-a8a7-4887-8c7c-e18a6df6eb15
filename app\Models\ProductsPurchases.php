<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductsPurchases extends Model
{
    use HasFactory;
         protected $table = 'products_purchases';
      protected $fillable = [
        'Product_Code',
        'P_Ar_Name',
        'P_En_Name',
        'V_Name',
        'VV_Name',
        'Original_Qty',
        'SmallCode',  
        'Qty',
        'SmallQty',
        'Price',
        'Discount',
        'TDiscount',
        'Tax',
        'Total_Bf_Tax',
        'Total_Tax',
        'Total',
        'Store',
        'Product',
        'Exp_Date',
        'V1',
        'V2',
        'Unit',
        'Purchase',
        'Date',
        'Code',
        'Refernce_Number',
        'Safe',
        'Vendor',
        'Delegate',
        'Coin',
        'User',
        'Cost_Center',
        'Type',
        'Ship',  
        'Payment_Method',  
    ];

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
    
            public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }
    
            public function V1()
    {
        return $this->belongsTo(SubVirables::class,'V1');
    }
    
            public function V2()
    {
        return $this->belongsTo(SubVirables::class,'V2');
    }
    
            public function Unit()
    {
        return $this->belongsTo(Measuerments::class,'Unit');
    }
    
               public function Tax()
    {
        return $this->belongsTo(Taxes::class,'Tax');
    }
    
            public function Purchase()
    {
        return $this->belongsTo(Purchases::class,'Purchase');
    }
    
             public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
       public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
         public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
                 public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }
    
    
}

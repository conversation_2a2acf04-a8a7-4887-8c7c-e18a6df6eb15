<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_default_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Payment_Method');
            $table->string('Status');
            $table->string('V_and_C');
            $table->string('Mainus');
            $table->string('Price_Sale');
            $table->integer('Safe');
            $table->integer('Client');
            $table->integer('Delegate');
            $table->integer('Store');
            $table->integer('Coin');
            $table->timestamps();
            $table->string('Brand')->nullable();
            $table->string('Group')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Expire')->nullable();
            $table->string('Draw');
            $table->string('Shift_Pass');
            $table->string('Empp');
            $table->string('Discount');
            $table->string('Delivery');
            $table->string('Execute_Precent');
            $table->string('StoresQty')->nullable();
            $table->string('DelegateEmp')->nullable();
            $table->string('TaxType')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales_default_data');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BonesType extends Model
{
    use HasFactory;
                protected $table = 'bones_types';
      protected $fillable = [
        'Name',
        'NameEn',
        'Account',

     
    ];
    
    
      public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }
}

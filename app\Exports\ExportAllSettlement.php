<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use App\Models\Sales;
use App\Models\GeneralDaily;
use DB ;
class ExportAllSettlement implements FromCollection ,WithHeadings 
{
 
    
  
    public function collection()
    {

        
        
       if(app()->getLocale() == 'ar' ){     
           $prods = DB::table('settlements')
            ->leftJoin('product_settlements', function ($join) {
    
            $join->on('settlements.id', '=', 'product_settlements.Set_ID');
        })
                  
                   ->leftJoin('stores', function ($join) {
    
            $join->on('settlements.Store', '=', 'stores.id');
        })
        
         
           ->leftJoin('measuerments', function ($join) {
    
            $join->on('product_settlements.Unit', '=', 'measuerments.id');
        })          

->select('settlements.Code'
     ,'settlements.Date'
         ,'stores.Name as StoreName'
           ,'settlements.Total_Dificit'
           ,'settlements.Total_Excess'
           ,'settlements.Total_Dificit_Price'
           ,'settlements.Total_Excess_Price'
         
         ,'product_settlements.P_Ar_Name'
         ,'product_settlements.P_Code'
         ,'product_settlements.Av_Qty'
         ,'product_settlements.Price'
          ,'measuerments.Name as UnitName'
         ,'product_settlements.Inventory'
         ,'product_settlements.Deficit'
         ,'product_settlements.Excess'
         ,'product_settlements.TotalDificitP'
         ,'product_settlements.TotalExcessP'
        
        

         
        )
                  ->get();
       }else{
           
            $prods = DB::table('settlements')
            ->leftJoin('product_settlements', function ($join) {
    
            $join->on('settlements.id', '=', 'product_settlements.Set_ID');
        })
                  
                   ->leftJoin('stores', function ($join) {
    
            $join->on('settlements.Store', '=', 'stores.id');
        })
        
         
           ->leftJoin('measuerments', function ($join) {
    
            $join->on('product_settlements.Unit', '=', 'measuerments.id');
        })          

->select('settlements.Code'
     ,'settlements.Date'
         ,'stores.NameEn as StoreName'
           ,'settlements.Total_Dificit'
           ,'settlements.Total_Excess'
           ,'settlements.Total_Dificit_Price'
           ,'settlements.Total_Excess_Price'
         ,'product_settlements.P_En_Name'
         ,'product_settlements.P_Code'
         ,'product_settlements.Av_Qty'
         ,'product_settlements.Price'
          ,'measuerments.NameEn as UnitName'
         ,'product_settlements.Inventory'
         ,'product_settlements.Deficit'
         ,'product_settlements.Excess'
         ,'product_settlements.TotalDificitP'
         ,'product_settlements.TotalExcessP'
        
        

         
        )
                  ->get();  
           
           
       }


        return collect($prods);
    }
    

    public function headings(): array
    {
        return [
          'Code',
          'Date',
          'Store',
          'Total Qty Dificit',
          'Total Qty Excess',
          'Total Dificit Price',
          'Total Excess Price',
          'Product Name',
          'Product Code',
          'Qty',
          'Price',
          'Unit',
          'Inventory',
          'Deficit',
          'Excess',
          'Total Dificit',
          'Total Excess',
        
         
        
        ];
    }
    
    
    

}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class EmpExcc implements ToCollection, WithChunkReading , WithBatchInserts
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        

         DB::table('emp_excs')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('emp_excs')->insert([

     
            'Name'	   =>$value[1]
            ,'Account'  =>$value[2]
            ,'Emp_Type'  =>$value[3]
            ,'Salary'  =>$value[4]
            ,'Phone'  =>$value[5]
            ,'Job'  =>$value[6]
            ,'Department'  =>$value[7]
            ,'Store1'  =>$value[8]
            ,'Store2'  =>$value[9]
            ,'Store3'  =>$value[10]
            ,'created_at'  =>$value[11]
            ,'updated_at'  =>$value[12]
           

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}
	

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMaintainceColorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('maintaince_colors', function (Blueprint $table) {
            $table->increments('id');
            $table->string('Refuse')->nullable();
            $table->string('Reported_Client')->nullable();
            $table->string('Refused_Client')->nullable();
            $table->string('Edited')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('maintaince_colors');
    }
}
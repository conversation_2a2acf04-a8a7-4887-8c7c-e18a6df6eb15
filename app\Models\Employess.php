<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employess extends Model
{
    use HasFactory;
        protected $table = 'employesses';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Emp_Type',
        'Salary',
        'Attendence',
        'Departure',
        'Hours_Numbers',
        'Days_Numbers',
        'Day_Price',
        'Precentage_of_Sales',
        'Precentage_of_Profits',
        'Precentage_of_Execution',
        'Image',
        'Note',
        'Bank_Account',
        'Qualifications',
        'Address',
        'Social_Status',
        'ID_Number',
        'Contract_Start',
        'Contract_End',
        'Phone',
        'Phone2',
        'Email',
        'Password',
        'Job',
        'Department',
        'Account',
        'Covenant',
        'Commission',
        'Account_Emp',
        'User',
        'Price_Level',
        'Bill_Num',
        'NumbersOfBill',
        'EmpSort',
        'CV',
        'ID_Image',
        'Criminal_status',
        'Contract',
        'health_certificate',
        'Search_Card',
        'Recruitment_certificate',
        'employee_profile',
        'duration_criminal_investigation',
        'Birthdate',
        'Attitude_recruiting',
        'Job_Number',
        'date_resignation',
        'Living',
        'Branch',
        'Level',
        'Religion',
        'Insurance_salary',
        'Insurance_companies',
        'Previous_experience',
        'Nationality',
        'MonthlyTarget',
        'QuarterTarget',
        'SemiTarget',
        'YearlyTarget',
        'IDExpireDate',
        'LicensExpireDate',
        'PassportExpireDate',
        'Merit',
        'Pro_Group',
        'SearchCode',
        'Active',

          


     
    ];
    
    
       public function Job()
    {
        return $this->belongsTo(JobsTypes::class,'Job');
    }
    
      public function Pro_Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Pro_Group');
    }
    
           public function Department()
    {
        return $this->belongsTo(WorkDepartments::class,'Department');
    }
    
           public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }       
    
    public function Merit()
    {
        return $this->belongsTo(AcccountingManual::class,'Merit');
    }
    
            public function Covenant()
    {
        return $this->belongsTo(AcccountingManual::class,'Covenant');
    }
    
            public function Commission()
    {
        return $this->belongsTo(AcccountingManual::class,'Commission');
    }
    
           public function Account_Emp()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Emp');
    }
    
           public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
    
        public function Admin()
    {
        return $this->hasOne(Admin::class);
    }
    
               public function PurchasesOrder()
    {
        return $this->hasOne(PurchasesOrder::class);
    }
    
                    public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }
    
                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }
    
                                   public function CustomersTickets()
    {
        return $this->hasOne(CustomersTickets::class);
    }
    
                      public function Interviews()
    {
        return $this->hasOne(Interviews::class);
    }
    
      
        public function Quote()
    {
        return $this->hasOne(Quote::class);
    }
    
           public function SalesOrder()
    {
        return $this->hasOne(SalesOrder::class);
    }
    
         public function Sales()
    {
        return $this->hasOne(Sales::class);
    }
    
           public function Borrowa()
    {
        return $this->hasOne(Borrowa::class);
    }
    
           public function Deduction()
    {
        return $this->hasOne(Deduction::class);
    }
    
                 public function Entitlement()
    {
        return $this->hasOne(Entitlement::class);
    }
    
                   public function AttendanceEmp()
    {
        return $this->hasOne(AttendanceEmp::class);
    }
    
                   public function DepartureEmp()
    {
        return $this->hasOne(DepartureEmp::class);
    }
    
                  public function RegOverTime()
    {
        return $this->hasOne(RegOverTime::class);
    } 
    
                       public function Loan()
    {
        return $this->hasOne(Loan::class);
    }
     
    
                        public function EmpInstallment()
    {
        return $this->hasOne(EmpInstallment::class);
    }
    
    
                        public function EmpInstallmentDetails()
    {
        return $this->hasOne(EmpInstallmentDetails::class);
    }
    
    
                    public function PaySalary()
    {
        return $this->hasOne(PaySalary::class);
    }
    
                       public function EmpRatio()
    {
        return $this->hasOne(EmpRatio::class);
    }          
    public function SafeTransfers()
    {
        return $this->hasOne(SafeTransfers::class);
    }
    
               public function Level()
    {
        return $this->belongsTo(Employment_levels::class,'Level');
    }
    
               public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }
    
               public function Insurance_companies()
    {
        return $this->belongsTo(Insurance_companies::class,'Insurance_companies');
    }
    
                  public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }
    


    

}

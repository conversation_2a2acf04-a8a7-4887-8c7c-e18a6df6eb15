<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReciptsSalesPetrol extends Model
{
    use HasFactory;
       protected $table = 'recipts_sales_petrols';
      protected $fillable = [
        'Recipt_Amount',
        'Recipt',       
        'SalesPetrol',       
    ];

            public function Recipt()
    {
        return $this->belongsTo(ReciptsType::class,'Recipt');
    }
    
             public function SalesPetrol()
    {
        return $this->belongsTo(SalesPetrol::class,'SalesPetrol');
    }
}

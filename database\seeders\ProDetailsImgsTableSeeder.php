<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProDetailsImgsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('pro_details_imgs')->delete();
        
        \DB::table('pro_details_imgs')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Image' => 'WebsliderImages/v0CfLNCcIGAOnQeBUxOm.png',
                'Arabic_Title' => 'Safe Payment',
                'Arabic_Desc' => 'Pay with the world\'s most payment methods.',
                'English_Title' => 'Safe Payment',
                'English_Desc' => 'Pay with the world\'s most payment methods.',
                'created_at' => NULL,
                'updated_at' => '2022-06-04 19:45:52',
            ),
            1 => 
            array (
                'id' => 2,
                'Image' => 'WebsliderImages/Sl0geXTxU3RwT7CXWSx1.png',
                'Arabic_Title' => 'Confidence',
                'Arabic_Desc' => 'Protection covers your purchase and personal data.',
                'English_Title' => 'Confidence',
                'English_Desc' => 'Protection covers your purchase and personal data.',
                'created_at' => NULL,
                'updated_at' => '2022-06-04 19:46:40',
            ),
            2 => 
            array (
                'id' => 3,
                'Image' => 'WebsliderImages/h2xcgSYBdXMeAaCf83Ob.png',
                'Arabic_Title' => 'Worldwide Delivery',
                'Arabic_Desc' => 'FREE & fast shipping to over 200+ countries & regions.',
                'English_Title' => 'Worldwide Delivery',
                'English_Desc' => 'FREE & fast shipping to over 200+ countries & regions.',
                'created_at' => NULL,
                'updated_at' => '2022-06-04 19:47:09',
            ),
            3 => 
            array (
                'id' => 4,
                'Image' => 'WebsliderImages/DgVQ96oWJbl2ZfP3DGiM.png',
                'Arabic_Title' => 'Hotline',
                'Arabic_Desc' => 'Talk to help line for your question on 4141 456 789, 4125 666 888',
                'English_Title' => 'Hotline',
                'English_Desc' => 'Talk to help line for your question on 4141 456 789, 4125 666 888',
                'created_at' => NULL,
                'updated_at' => '2022-06-04 19:47:44',
            ),
        ));
        
        
    }
}
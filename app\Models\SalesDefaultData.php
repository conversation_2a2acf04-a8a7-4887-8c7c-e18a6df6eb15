<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesDefaultData extends Model
{
    use HasFactory;
         protected $table = 'sales_default_data';
      protected $fillable = [
        'Payment_Method',
        'Status',
        'V_and_C',
        'Mainus',
        'Price_Sale',
        'Safe',
        'Client',
        'Delegate',
        'Store',
        'Coin',
        'Draw',
        'Shift_Pass',
        'Brand',
        'Group',
        'English_Name',
        'Expire',
        'Empp',
        'Discount',
        'Delivery',
        'Execute_Precent',
        'StoresQty',
        'DelegateEmp',
        'TaxType',
        'DiscountTaxShow',
        'SalesOrderType',
        'ECommercceSaleType',
        'Kitchen_Order',
        'Waiter',
        'Hall_Service_Type',
        'Hall_Service_Precent',
        'CountryResturantWebsite',
        'Bank',
        'Country',
        'SalesLowCostPrice',
        'ShowJobOrders',
        'LimitSalesQty',
        'Total_Wight_Bill',
        'Duplicate_Items',
   
    ];
    
         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
    
    
             public function Bank()
    {
        return $this->belongsTo(AcccountingManual::class,'Bank');
    }
    
    
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }          public function Waiter()
    {
        return $this->belongsTo(Employess::class,'Waiter');
    }
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }
    public function Delivery()
    {
        return $this->belongsTo(Employess::class,'Delivery');
    } 
    public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }    public function Country()
    {
        return $this->belongsTo(Countris::class,'Country');
    }
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;



class ProductFileStart implements ToCollection, WithChunkReading , WithBatchInserts
{
 
    public function collection(Collection $collection)
    {
        

         DB::table('import_new_prods_starts')->truncate();
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
                DB::table('import_new_prods_starts')->insert([

            'Name'	   =>$value[1]
            ,'Type'	   =>$value[2]
            ,'Group'  =>$value[3]
            ,'Brand'  =>$value[4]
            ,'Unit'  =>$value[5]
            ,'Rate'  =>$value[6]
            ,'Barcode'  =>$value[7]
            ,'Price_1'  =>$value[8]
            ,'Price_2'  =>$value[9]
            ,'Price_3'  =>$value[10]
            ,'Def'  =>$value[11]
            ,'Num'  =>$value[12]
            ,'Store'  =>$value[13]
            ,'Qty'  =>$value[14]
            ,'CostPrice'  =>$value[15]
            ,'Total'  =>$value[16]
            ,'created_at'  =>$value[17]
            ,'updated_at'  =>$value[18]
            ,'Code_Type'  =>$value[19]
            ,'World_Code'  =>$value[20]

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomPrint extends Model
{
    use HasFactory;
         protected $table = 'custom_prints';
      protected $fillable = [
          
        'Sales_Print_Type',
          
        'Sales_Bill_Code',
        'Sales_Date',
        'Sales_Coin',
        'Sales_Draw',
        'Sales_Payment_Method',
        'Sales_Status',
        'Sales_Executor',
        'Sales_Refernce_Number',
        'Sales_Safe',
        'Sales_Client',
        'Sales_Delegate',
        'Sales_Store',
        'Sales_User',
        'Sales_Cost_Center',
        'Sales_Notes',
          
        'Sales_Pro_Code',
        'Sales_Pro_Name',
        'Sales_Pro_Unit',
        'Sales_Pro_Qty',
        'Sales_Pro_Price',
        'Sales_Pro_Discount',
        'Sales_Pro_Total_Bf_Tax',
        'Sales_Pro_Total_Tax',
        'Sales_Pro_Total',
        'Sales_Pro_Store',
        'Sales_Pro_Desc',
        'Sales_Pro_Exp_Date',
        'Sales_Pro_Weight',    
        'Sales_Pro_Patch_Number',    
          
        'Sales_Product_Numbers',
        'Sales_Total_Qty',
        'Sales_Total_Discount',
        'Sales_Total_Bf_Taxes',
        'Sales_Total_Taxes',
        'Sales_Total_Price',
        'Sales_Paid',
        'Sales_Residual',
        'Sales_Taknet',
        'Sales_Credit',
        'Sales_Barcode',
        'Sales_Text',
        'Sales_Seal',
          
          
        'Purch_Print_Type',
          
        'Purch_Bill_Code',
        'Purch_Date',
        'Purch_Vendor_Bill_Date',
        'Purch_Coin',
        'Purch_Draw',
        'Purch_Payment_Method',
        'Purch_Status',
        'Purch_Refernce_Number',
        'Purch_Safe',
        'Purch_Vendor',
        'Purch_Delegate',
        'Purch_Store',
        'Purch_User',
        'Purch_Cost_Center',
        'Purch_Notes',
          
        'Purch_Pro_Code',
        'Purch_Pro_Name',
        'Purch_Pro_Unit',
        'Purch_Pro_Qty',
        'Purch_Pro_Price',
        'Purch_Pro_Discount',
        'Purch_Pro_Total_Bf_Tax',
        'Purch_Pro_Total_Tax',
        'Purch_Pro_Total',
        'Purch_Pro_Store',
       'Purch_Pro_Exp_Date',

          
        'Purch_Product_Numbers',
        'Purch_Total_Qty',
        'Purch_Total_Discount',
        'Purch_Total_Bf_Taxes',
        'Purch_Total_Taxes',
        'Purch_Total_Price',
        'Purch_Paid',
        'Purch_Residual',
          
        'Purch_Taknet',
        'Purch_Credit',
        'Purch_Barcode',
        'Purch_Text',
        'Purch_Seal',
          


    
    ];
}

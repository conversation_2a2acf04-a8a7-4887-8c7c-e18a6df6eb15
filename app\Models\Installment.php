<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Installment extends Model
{
    use HasFactory;
         protected $table = 'installments';
      protected $fillable = [
        'presenter',
        'annual_interest',
        'monthly_installment',
        'Years_Number',
        'total',
        'installment_Num',
        'Date_First_installment',
        'Residual',
        'Status',
        'Client',
        'Sales',

     
    ];
    
    
       public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
    
      public function Sales()
    {
        return $this->belongsTo(Sales::class,'Sales');
    }
    
                  public function InstallmentDates()
    {
        return $this->hasOne(InstallmentDates::class);
    }
    
}

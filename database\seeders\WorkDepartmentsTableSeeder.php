<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class WorkDepartmentsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('work_departments')->delete();
        
        \DB::table('work_departments')->insert(array (
            0 => 
            array (
                'id' => 8,
                'Arabic_Name' => 'مدير ٢',
                'English_Name' => 'Manager 2',
                'created_at' => '2022-05-31 18:19:43',
                'updated_at' => '2022-05-31 18:19:43',
                'Parent' => '6',
            ),
            1 => 
            array (
                'id' => 10,
                'Arabic_Name' => 'فرع ١',
                'English_Name' => 'Branch 1',
                'created_at' => '2022-05-31 18:25:55',
                'updated_at' => '2022-05-31 18:25:55',
                'Parent' => '7',
            ),
            2 => 
            array (
                'id' => 11,
                'Arabic_Name' => 'فرع ٢',
                'English_Name' => 'Branch 2',
                'created_at' => '2022-05-31 18:26:21',
                'updated_at' => '2022-05-31 18:26:21',
                'Parent' => '7',
            ),
            3 => 
            array (
                'id' => 12,
                'Arabic_Name' => 'فرع ٣',
                'English_Name' => 'Branch 3',
                'created_at' => '2022-05-31 18:26:45',
                'updated_at' => '2022-05-31 18:26:45',
                'Parent' => '8',
            ),
            4 => 
            array (
                'id' => 13,
                'Arabic_Name' => 'فرع ٤',
                'English_Name' => 'Branch 4',
                'created_at' => '2022-05-31 18:27:04',
                'updated_at' => '2022-05-31 18:27:04',
                'Parent' => '8',
            ),
            5 => 
            array (
                'id' => 15,
                'Arabic_Name' => 'Manager 3',
                'English_Name' => 'Manager 3',
                'created_at' => '2022-05-31 19:41:57',
                'updated_at' => '2022-05-31 19:41:57',
                'Parent' => '6',
            ),
            6 => 
            array (
                'id' => 16,
                'Arabic_Name' => 'Default',
                'English_Name' => 'Default',
                'created_at' => '2022-05-31 19:49:55',
                'updated_at' => '2022-05-31 19:49:55',
                'Parent' => '12',
            ),
            7 => 
            array (
                'id' => 17,
                'Arabic_Name' => 'X',
                'English_Name' => 'X',
                'created_at' => '2022-05-31 19:51:11',
                'updated_at' => '2022-05-31 19:51:11',
                'Parent' => '16',
            ),
            8 => 
            array (
                'id' => 18,
                'Arabic_Name' => 'Y',
                'English_Name' => 'Y',
                'created_at' => '2022-05-31 19:51:37',
                'updated_at' => '2022-05-31 19:51:37',
                'Parent' => '12',
            ),
            9 => 
            array (
                'id' => 6,
                'Arabic_Name' => 'رئيس مجلس الاداره',
                'English_Name' => 'Chairman of Board of Directors',
                'created_at' => '2022-04-13 02:49:57',
                'updated_at' => '2022-04-13 02:49:57',
                'Parent' => '0',
            ),
            10 => 
            array (
                'id' => 7,
                'Arabic_Name' => 'مدير ١',
                'English_Name' => 'Manager 1',
                'created_at' => '2022-05-31 18:19:18',
                'updated_at' => '2022-06-27 18:09:39',
                'Parent' => '6',
            ),
        ));
        
        
    }
}
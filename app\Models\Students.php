<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Students extends Model
{
    use HasFactory;
     protected $table = 'students';
      protected $fillable = [

                'Code',
                'Arabic_Name',
                'English_Name',
                'Group',
                'Phone1',
                'Phone2',
                'Dad_Phone',
                'Mom_Phone',
                'Special_Case',
                '<PERSON>',
                'Email',
                'Whatsapp',
                'Age',
                'Gov',
                'City',
                'Place',
                'Nationality',
                'Address',
                'Profession',
                'Ntional_ID',
                'Account',
             

    ];
    
    
        public function Nationality()
    {
        return $this->belongsTo(Countris::class,'Nationality');
    }    
        public function Gov()
    {
        return $this->belongsTo(Governrate::class,'Gov');
    }    
        public function City()
    {
        return $this->belongsTo(City::class,'City');
    }    
        public function Place()
    {
        return $this->belongsTo(Places::class,'Place');
    }   
    

                   public function Special_Case()
    {
        return $this->belongsTo(SpecialCases::class,'Special_Case');
    }  
    
    
               public function Group()
    {
        return $this->belongsTo(StudentGroup::class,'Group');
    }  
    
    
               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }       
    

}

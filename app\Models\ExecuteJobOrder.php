<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExecuteJobOrder extends Model
{
    use HasFactory;
       protected $table = 'execute_job_orders';
      protected $fillable = [

        'Code',                         
        'Date',
        'Note',
        'Client',
        'Executor',
        'Delegate',
        'RecivedDate',
        'Recipient',
       'Order',
       'Transfer',
          
    ];

    
    

    public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }         
    
    
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
    
              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }          
    
    public function Order()
    {
        return $this->belongsTo(JobOrder::class,'Order');
    }
    
    
}

<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use DB;

class ProductMovess implements ToCollection, WithChunkReading , WithBatchInserts
{

    public function collection(Collection $collection)
    {
        
        foreach($collection as $key => $value)
        {
            if($key > 0)
            {
            DB::table('product_moves')->insert([

            'Date'	   =>$value[1]
            ,'Type'  =>$value[2]
            ,'Bill_Num'  =>$value[3]
            ,'Incom'  =>$value[4]
            ,'Outcom'  =>$value[5]
            ,'Current'  =>$value[6]
            ,'P_Ar_Name'  =>$value[7]
            ,'P_En_Name'  =>$value[8]
            ,'P_Code'  =>$value[9]
            ,'Unit'  =>$value[10]
            ,'Group'  =>$value[11]
            ,'Store'  =>$value[12]
            ,'Product'  =>$value[13]
            ,'V1'  =>$value[14]
            ,'V2'  =>$value[15]
            ,'User'  =>$value[16]
            ,'created_at'  =>$value[17]
            ,'updated_at'  =>$value[18]
            ,'CostIn'  =>$value[19]
            ,'CostOut'  =>$value[20]
            ,'CostCurrent'  =>$value[21]
          

                ]);
            }
        }
  
    }
    
           public function batchSize(): int
    {
        return 10000;
    }
    
    
    
       public function chunkSize(): int
    {
        return 10000;
    }
}
